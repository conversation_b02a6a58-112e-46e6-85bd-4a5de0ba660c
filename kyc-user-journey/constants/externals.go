package constants

// request names.
const (
	FaceMatch                            = "faceMatch"
	HyperVergeLogin                      = "hyperVergeLogin"
	SendEmailSync                        = "sendEmailSync"
	NSDLPanVerification                  = "nsdlPanVerification"
	GetProfile                           = "getProfile"
	InitiateDigio                        = "initiateDigio"
	GetKYCStatusDIGIO                    = "getKycStatusDigio"
	GETDigioKYCMedia                     = "getDigioKycMediaPanAadhaar"
	SendOTPSMS                           = "sendOTPSMS"
	GetWorkflowInstanceCount             = "getProcessInstanceCount"
	StartWorkflowInstance                = "startProcessInstance"
	DeleteWorkflowInstance               = "deleteProcessInstance"
	GetWorkflowInstance                  = "getProcessInstance"
	InitiateSetu                         = "initiateSetu"
	CreateESignPDF                       = "createESignPDF"
	GetCvlKRAPanStatusV3ExternalAdapter  = "getCvlKRAPanStatusV3ExternalAdapter"
	GetCvlKRAPanDetailsV3ExternalAdapter = "getCvlKRAPanDetailsV3ExternalAdapter"
	CreateESignPDFKYCForm                = "createESignPDFKYCForm"
	CreateESignPDFKYCFormV2              = "createESignPDFKYCFormV2"
	CreateNRIEsignPDFKYCForm             = "createNRIESignPDFKYCForm"
	RedirectFromSetu                     = "redirectFromSetu"
	SendNotification                     = "sendNotification"
	SendEmailAndSmsCallbackAsync         = "sendEmailAndSmsCallbackAsync"
	GetKYPDetails                        = "getKYPDetails"
	ResetOnboardingEsign                 = "resetOnboardingEsign"
	GetHoldingDetailsFromPortfolio       = "getHoldingDetails"
	GetIMPSInfo                          = "getIMPSInfo"
	OCRCheque                            = "ocrCheque"
	HyperVergeNameMatch                  = "hyperVergeNameMatch"
	ClcmGetClientDetailsByAttributes     = "clcmGetClientDetailsByAttributes"
	GetClientProductsFromCLCM            = "getClientProductsFromCLCM"
	GetPartnerProfile                    = "getPartnerProfile"
	IMPSHyperVerge                       = "impsHyperVerge"
	CamsAuthentication                   = "camsAuthentication"
	CamsCreateConsent                    = "camsCreateConsent"
	CheckNetBankingSupport               = "checkNetBankingSupport"
	SearchClient                         = "searchClient"
	GoogleTokenVerification              = "googleTokenVerification"
	CreateRPDDeepLink                    = "createRPDDeepLink"
	GetRPDStatus                         = "getRPDStatus"
	InitiatePayout                       = "initiatePayout"
	InitiateWriteoff                     = "initiateWriteoff"
	GetLedgerBalanceSegmentWise          = "getLedgerBalanceSegmentWise"
	GetLedgerBalanceDetails              = "getLedgerBalance"
	InitiateUpiPayment                   = "initiateUpiPayment"
	InitiateNetBankingPayment            = "initiateNetBankingPayment"
	GetTransactionStatus                 = "getTransactionStatus"
	GetSuperPortfolio                    = "getSuperPortfolio"
	GetPortfolioHoldings                 = "getPortfolioHoldings"
	GetSubscriptionStatus                = "getSubscriptionStatus"
	GetRMSLimit                          = "getRMSLimit"
	GetRMSLimitS2S                       = "getRMSLimitS2S"
	GetIPOOrders                         = "getIPOOrders"
	GetMFOrders                          = "getMFOrders"
	GetMFSIPs                            = "getMFSIPs"
	GetMFSWPs                            = "getMFSWPs"
	GetOrderbook                         = "getOrderbook"
	GetOrderbookS2S                      = "getOrderbookS2S"
	GetPositions                         = "getPositions"
	GetPositionsS2S                      = "getPositionsS2S"
	GetPositionsS2SWithV5Api             = "getPositionsS2SWithV5Api"
	GetReferralDetails                   = "getReferralDetails"
	LSQB2CSerach                         = "lsqB2CSearch"
	LSQB2BSearch                         = "lsqB2BSearch"
	LSQB2CDeleteLead                     = "lsqB2CDeleteLead"
	LSQB2BDeleteLead                     = "lsqB2BDeleteLead"
	LSQB2CLeadCapture                    = "lsqB2CLeadCapture"
	LSQB2BLeadCapture                    = "lsqB2BLeadCapture"
	LSQB2BLeadCaptureSync                = "lsqB2BLeadCaptureSync"
	GetGeolocation                       = "getGeolocation"
	HyperVergeOcrAadhar                  = "aadharOcr"
	PanOCRAINXT                          = "panOCRAINXT"
	DerivativeOCRAINXT                   = "derivativeOCRAINXT"
	HyperVergePanOCR                     = "panOCRHyperverge"
	HyperVergeSignatureDetection         = "signatureDetectionHyperverge"
	GetWorkflowInstanceByMobile          = "getProcessInstanceByMobile"
	GetCVLKRAPanStatus                   = "getCvlKRAPanStatus"
	GetCvlKRAPassword                    = "getCvlKRAPassword"
	GetCvlKRAPanDetails                  = "getCvlKRAPanDetails"
	ClcmUpdateAccountClosureNudge        = "clcmUpdateAccountClosureNudge"
	SendWhatsappCommunication            = "sendWhatsAppCommunication"
	SetuDocumentUpload                   = "setuDocumentUpload"
	SetuInitiateSignature                = "setuInitiateSignature"
	SetuGetSignatureStatus               = "setuGetSignatureStatus"
	SetuDownloadSignedDocument           = "setuDownloadSignedDocument"
	NSDLV2PanVerification                = "nsdlV2PanVerification"
	GetStockSIP                          = "getStockSIP"
	GetBrokerageFromRTB                  = "getBrokerageFromRTB"
	GetGeoCoding                         = "getGeoCoding"
	GetProcessInstanceVariables          = "getProcessInstanceVariables"
	GetExperianAuthToken                 = "getExperianAuthToken"
	GetExperianPanMatchData              = "getExperianPanMatchData"
	OnekycSync                           = "onekycSync"
	UpdateCamundaVariables               = "updateCamundaVariables"
	GetProcessInstanceHistory            = "getProcessInstanceHistory"
	CreatePhyMfESignPDFKYCForm           = "createPhyMfESignPDFKYCForm"
	Getsubbrokercommdetails              = "getsubbrokercommdetails"
	QueryProfileDetailsFromCLCM          = "queryProfileDetailsFromCLCM"
	GetAppsflyerReferralGcdSdkDetails    = "getAppsflyerReferralGcdSdkDetails"
	ClcmPartnerProfile                   = "clcmPartnerProfile"
	HypervergeGetSignatureStatus         = "hypervergeGetSignatureStatus"
	HypervergeDownloadSignedDocument     = "hypervergeDownloadSignedDocument"
)

// pan clcm.
const (
	PanClcmSource = "kyc"
)

// clcm header key.
const (
	MobileHeaderKey = "mobile"
	EmailHeaderKey  = "email"
)

// workflow.
const (
	WorkflowNameParamKey              = "processDefinitionKey"
	WorkflowBusinessKeyParamKey       = "businessKey"
	StartWorkflowURLKeyPathParamKey   = "key"
	WorkflowProcessInstanceIDParamKey = "id"
	Value1ParamKey                    = "v1"
	Value2ParamKey                    = "v2"
	Value3ParamKey                    = "v3"
	SkipCustomListenersQueryParamKey  = "skipCustomListeners"
	SkipIOMappingsQueryParamKey       = "skipIoMappings"
	SkipSubProcessesQueryParamKey     = "skipSubprocesses"
	SubBrokerCode                     = "subBrokerCode"
)

// Query params.
const (
	PanQueryKey               = "pan"
	AccessTokenQueryKey       = "access_token"
	IDTokenQueryKey           = "id_token"
	GetEsignPDFUserIDQueryKey = "userid"
)

// Path params.
const (
	SetuDocumentID       = "documentID"
	HypervergeDocumentID = "documentID"
	GeolocationIPAddress = "ipAddress"
	GeoCodingLatLong     = "LatLng"
)

const (
	Success  = "success"
	Y        = "y"
	Angelite = "Angelite"
	Failure  = "failure"
)

var ProfileAttributesForPAN = []string{"active", "deactiveReason", "pan", "inactiveDate", "applicationNo"}

var ProfileAttributesForEmail = []string{"active", "deactiveReason", "inactiveDate", "applicationNo"}
var PartnerProfileAttributes = []string{"partner.partner_type", "partner.mobile", "partner.email"}

var ProfileAttributesForEmodCheck = []string{"active", "dpDetails", "mobile", "pan"}
var ProfileAttributesForDDPI = append(ProfileAttributesForEmodCheck, "ddpiStatus", "clientDetails")
var ProfileAttributesForMTF = append(ProfileAttributesForEmodCheck, "productDetails", "email")
var ProfileAttributesForReKYC = append(ProfileAttributesForEmodCheck, "deactiveReason", "applicationNo")
var ProfileAttributesForBank = append(ProfileAttributesForEmodCheck, "bankDetails")
var ProfileAttributesForEmodPersonalDetails = append(ProfileAttributesForEmodCheck, "fatherName", "birthdate", "email")
var ProfileAttributesForWelcomeEmail = append(ProfileAttributesForEmodCheck, "fatherName", "birthdate", "email")
var ProfileAttributesForNominee = append(ProfileAttributesForEmodCheck, "email")
var ProfileAttributesForEmodSegment = append(ProfileAttributesForEmodCheck, "email", "address", "birthdate")
var ProfileAttributesForEmodSettlementFrequency = []string{"clientDetails.Email", "clientDetails.LongName"}
var ProfileAttributesForEmodGender = append(ProfileAttributesForEmodCheck, "email")
var ProfileAttributesForEmodBank = []string{"clientDetails.Email", "clientDetails.LongName"}
var ProfileAttributesForMobile = []string{"active", "deactiveReason", "mobile", "inactiveDate", "applicationNo"}
var ProfileAttributesForPartnerIDCheck = []string{"subBroker"}
var PartnerProfileAttributesForEmodPersonalDetails = []string{"partnerType", "mobile", "email"}
var ProfileAttributesForIncomeDetailsCheck = append(ProfileAttributesForEmodCheck, "clientDetails")
var ProfileAttributesForASBAActivation = append(ProfileAttributesForEmodCheck, "clientAccountType", "clientAccountSubType", "userType", "productDetails", "creationDate")
var ProfileAttributesForAccountFreeze = []string{"mobile", "email", "fullName"}

var CLCMProfileAttributesForEmail = []string{"clientDetails.ApplicationNo", "clientDetails.ClientId", "segmentDetails.ClientId", "segmentDetails.Exchange", "segmentDetails.Segment", "segmentDetails.ActiveFrom", "segmentDetails.InactiveFrom", "segmentDetails.DeactivationReason", "segmentDetails.Status",
	"mfSegmentDetails.ClientId", "mfSegmentDetails.Exchange", "mfSegmentDetails.Type", "mfSegmentDetails.ActiveFrom", "mfSegmentDetails.InactiveFrom", "mfSegmentDetails.DeactivationReason"}
var CLCMProfileAttributesForPAN = append(CLCMProfileAttributesForEmail, "clientDetails.Pan")

var CLCMProfileAttributesForEmodCheck = []string{"clientDetails.Mobile", "clientDetails.Pan", "clientDetails.ClientId", "segmentDetails.ClientId", "segmentDetails.Exchange", "segmentDetails.Segment", "segmentDetails.ActiveFrom", "segmentDetails.InactiveFrom", "segmentDetails.DeactivationReason", "segmentDetails.Status",
	"mfSegmentDetails.ClientId", "mfSegmentDetails.Exchange", "mfSegmentDetails.Type", "mfSegmentDetails.ActiveFrom", "mfSegmentDetails.InactiveFrom", "mfSegmentDetails.DeactivationReason", "dpDetails.ClientId", "dpDetails.DpIdNo", "dpDetails.DpIdStatus", "dpDetails.BoAccountNo", "dpDetails.DpName",
	"dpDetails.DpSchemeName", "dpDetails.DpType", "dpDetails.IsDefaultDp", "dpDetails.DpPOA", "dpDetails.DpDDPI"}

var CLCMProfileAttributesForReKYC = append(CLCMProfileAttributesForEmodCheck, "clientDetails.ApplicationNo")
var CLCMProfileAttributesForDDPI = append(CLCMProfileAttributesForEmodCheck, "clientDetails.LongName", "clientDetails.FatherName", "clientDetails.Email", "clientDetails.Branch", "clientDetails.SubBroker", "clientDetails.BirthDate", "clientDetails.IncomeSlabCode")
var CLCMProfileAttributesForEmodSegment = append(CLCMProfileAttributesForEmodCheck, "clientDetails.Email", "clientDetails.BirthDate")
var CLCMProfileAttributesForAccountFreeze = []string{"clientDetails.Mobile", "clientDetails.Email", "clientDetails.LongName", "clientDetails.ClientId"}
var CLCMProfileAttributesForNominee = append(CLCMProfileAttributesForEmodCheck, "clientDetails.Email")
var CLCMProfileAttributesForIncomeDetailsCheck = append(CLCMProfileAttributesForEmodCheck, "clientDetails.LongName", "clientDetails.FatherName", "clientDetails.Email", "clientDetails.Branch", "clientDetails.SubBroker", "clientDetails.BirthDate", "clientDetails.IncomeSlabCode")
var CLCMProfileAttributesForEmodGender = append(CLCMProfileAttributesForEmodCheck, "clientDetails.Email")
var CLCMProfileAttributesForEmodPersonalDetails = append(CLCMProfileAttributesForEmodCheck, "clientDetails.FatherName", "clientDetails.BirthDate", "clientDetails.Email")
var CLCMProfileAttributesForWelcomeEmail = []string{"clientDetails.Email"}
var CLCMProfileAttributesForASBAActivation = append(CLCMProfileAttributesForEmodCheck, "clientDetails.ClientAccountType", "clientDetails.ClientAccountSubType", "clientDetails.UserType", "clientDetails.IsMtfActive", "clientDetails.MtfActivationDate", "clientDetails.CreateTs")
var CLCMProfileAttributesForMTF = append(CLCMProfileAttributesForEmodCheck, "clientDetails.IsMtfActive", "clientDetails.MtfActivationDate", "clientDetails.Email")

// attributes list for clcm.
var (
	CLCMAttributesForMobileFetch                = []string{"clientCode.Mobile", "clientDetails.Mobile"}
	CLCMAttributesForAccountTypeFetch           = []string{"clientDetails.ClientAccountType"}
	CLCMAttributesForAccountTypeAndSubTypeFetch = []string{"clientDetails.ClientAccountType", "clientDetails.ClientAccountSubType"}
	CLCMAttributesForGenderFetch                = []string{"clientDetails.Gender"}
	CLCMAttributesForPanFetch                   = []string{"panDetails.Pan", "panDetails.FullName", "panDetails.GuardianType", "panDetails.GuardianName"}
	CLCMAttributesForSegmentFetch               = []string{"segmentDetails.Exchange", "segmentDetails.Segment", "segmentDetails.DeactivationReason", "segmentDetails.Status"}
	CLCMAttributesForSegmentFetchV2             = []string{"segmentDetails.Exchange", "segmentDetails.Segment", "segmentDetails.Status",
		"mfSegmentDetails.Exchange", "mfSegmentDetails.Type", "mfSegmentDetails.Status"}
	CLCMAttributesForMetadata     = []string{"clientMetadata.AsbaStatus", "clientMetadata.AccountFreeze", "clientMetadata.FirstTradeReadyTs"}
	CLCMAttributesForBasicDetails = []string{"panDetails.Pan", "panDetails.DOB", "panDetails.FullName", "panDetails.IsAadhaarPanSeededNSDL",
		"panDetails.IsAadhaarPanSeededNSE", "panDetails.IsAadhaarPanSeededCDSL", "kraDetails.KraStatus", "kraDetails.UpdateStatus", "kraDetails.EntryTs"}
	CLCMAttributesForNomineeDetails = []string{
		"nomineeDetails.OptedIn",
		"nomineeDetails.FirstName",
		"nomineeDetails.GuardianName",
		"nomineeDetails.GuardianPan",
		"nomineeDetails.AddressLine1",
		"nomineeDetails.AddressLine2",
		"nomineeDetails.AddressLine3",
		"nomineeDetails.City",
		"nomineeDetails.State",
		"nomineeDetails.Country",
		"nomineeDetails.Pincode",
		"nomineeDetails.StateCode",
		"nomineeDetails.CountryCode",
		"nomineeDetails.PrimaryPhoneType",
		"nomineeDetails.PrimaryPhoneNumber",
		"nomineeDetails.Pan",
		"nomineeDetails.DOB",
		"nomineeDetails.Email",
		"nomineeDetails.IsPrimary",
		"nomineeDetails.Relation",
		"nomineeDetails.Percentage",
	}
)

const (
	KYCCorrectionJourneyDisabledStatus      = "disabled"
	KYCCorrectionJourneyNotEligibleStatus   = "not eligible"
	KYCCorrectionJourneyActivatedStatus     = "activated"
	KYCCorrectionJourneyCountExceededStatus = "count exceeded"
	DigioPartialMessage                     = "Digilocker Partial"
)

const (
	KYCCorrectionJourneySubmittedStatus = "submitted"
)

const (
	ProfileProductDetailsActiveStatus   = "active"
	ProfileProductDetailsInactiveStatus = "inactive"
)

const (
	Match = "match"
)

// query param keys.
const (
	SortBy    = "sortBy"
	SortOrder = "sortOrder"
)

// query param values.
const (
	StartTime = "startTime"
	Desc      = "desc"
)

// workflow constants.
const (
	ProcessInstanceBusinessKey = "processInstanceBusinessKey"
	ProcessDefinitionKey       = "processDefinitionKey"
)

// lsq constants.
const (
	Stage  = "stage"
	AppNo  = "appno"
	Phone  = "phone"
	Live   = "Live"
	LeadID = "leadId"
)

// common auth constants.
const (
	S2SCommonAuthIssuerName = "kyc"
	S2SCommonAuthUserType   = "application"
	S2SEnabled              = "s2sEnabled"
	ClientCodeHeaderKey     = "ClientCode"
)

// common auth service name.
const (
	CNS = "cns"
)

// document types.
const (
	AddressDocument   = "ADDRESS"
	BasicDocument     = "BASIC"
	FinancialDocument = "FINANCIAL"
	FNODocument       = "FNO"
	PhotoDocument     = "PHOTO"
	SignatureDocument = "SIGNATURE"
	SelfieDocument    = "SELFIE"
)

// document flags.
const (
	UserUploadedPhotoFlag = "0"
	UserSelfiePhotoFlag   = "4"
	DigilockerPhotoFlag   = "2"
)

// profile constant.
const (
	AccountTypeCLI             = "CLI"
	AccountTypeIndividual      = "IND"
	AccountTypeHUF             = "HUF"
	AccountTypeBCP             = "BCP"
	AccountTypeLLP             = "LLP"
	AccountTypeForeignNational = "FN"
)

// camunda variable types.
const (
	StringType = "String"
	BoolType   = "Boolean"
	LongType   = "Long"
	DoubleType = "Double"
)

const (
	MobileQueryParam = "mobile"
)

const (
	AppsflyerAPIAppID         = "appId"
	AppsflyerDeviceIDParam    = "device_id"
	AppsflyerIOSAppID         = "id1060530981"
	AppsflyerAndroidAppID     = "com.msf.angelmobile"
	PhyMfB2CCampaignSubString = "nbu_physical_mf"
)

const (
	PaymentModeUpi        = "UPI"
	PaymentModeNetBanking = "NETBANKING"
)

const (
	HypervergeESignInputAPI  = "hypervergeEsignInputAPI"
	HypervergeESignOutputAPI = "hypervergeEsignOutputAPI"
)
