package constants

const (
	ApplicationName = "kyc-user-journey"
	TestMode        = "test"
	ReleaseMode     = "release"
	EnvProd         = "prod"
)

// context constants.
const (
	AuthRealmContextKey             = "auth_realm"
	MobileContextKey                = "mobile"
	AppNumberContextKey             = "app_number"
	CorrectionJourneyTypeContextKey = "correction_journey_type"
	KycJourneyTypeContextKey        = "kyc_journey_type"
	IntentContextKey                = "intent"
	ClientCodeContextKey            = "client_code"
	CorrelationIDContextKey         = "correlationId"
	ApplicationStatusContextKey     = "status"
	PartnerIDContextKey             = "partnerId"
	EmailMobileRealmKey             = "emailMobile"
	KycTypeContextKey               = "kyc_type"
	AuthTokenContextKey             = "auth_token"
	AgentIDContextKey               = "agent_id"
	AccountTypeContextKey           = "account_type"
	RoleContextKey                  = "role"
	PIIRuleContextKey               = "piiRule"
)

// string constants.
const (
	Empty        = ""
	Comma        = ","
	Dot          = "."
	OpenBracket  = "("
	CloseBracket = ")"
	Pipe         = "|"
	SemiColon    = ";"
	Colon        = ":"
	Space        = " "
	Dash         = "-"
	Slash        = "/"
	Equal        = "="
	Underscore   = "_"
	Hyphen       = "-"
	Null         = "null"
	EmptyJSON    = "{}"
	AtRate       = "@"
	PLUS         = "+"
	DOT          = "."
	RUPEE        = "₹"
	Percentage   = "%"
)

// defaults.
const (
	IntegerDefault = 0
)

// int constants.
const (
	Five    = 5
	One     = 1
	Hundred = 100
)

// supported databases.
const (
	KYC2Database               = "kyc2"
	KYC2ReadReplicaDatabase    = "kyc2_read_replica"
	KYCInHouseDatabase         = "kyc_in_house"
	KYCFulfillment             = "kyc_fulfillment"
	KYCCIDatabase              = "kyc_ci"
	KYCInternalInHouseDatabase = "kyc_internal_in_house"
	KYCRisk                    = "kyc_risk"
	KYCMSAJAG                  = "kyc_msajag"
	KYCCMS                     = "kyc_cms"
	KYCCRMDB                   = "kyc_crmdb"
	KYCEvents                  = "kyc_events"
	KYCSCCS                    = "kyc_sccs"
)

// mapper.
const (
	CustomMapperTagName = "kycmapper"
)

// flow names.
const (
	CamsFlowName                = "cams"
	DigioFlowName               = "digio"
	PoaFlowName                 = "poa"
	RPDFlowName                 = "rpd"
	SelfieFlowName              = "selfie"
	StartFlowName               = "start"
	SignatureFlowName           = "signature"
	PanFlowName                 = "pan"
	ESignFlowName               = "esign"
	EmailFlowName               = "email"
	BankFlowName                = "bank"
	NomineeFlowName             = "nominee"
	DerivativesFlowName         = "derivatives"
	CancelledChequeFlowName     = "cancelledCheque"
	BankStatementFlowName       = "bankStatement"
	MarriageCertificateFlowName = "marriageCertificate"
	ClientDetailsFlowName       = "clientDetails"
	NriIndianAddress            = "nriIndianAddress"
	ReactivationFlowName        = "reactivation"
	DeactivationFlowName        = "deactivation"
	NriDigioFlowName            = "nriDigio"
)

// flow status values.
const (
	ZERO = "0"
	TWO  = "2"
	ONE  = "1"
)

// common constants.
const (
	HyperVerge                  = "hyperverge"
	AINxt                       = "AiNxt"
	HyperVergeSource            = "HyperVerge"
	Bearer                      = "Bearer"
	DigioXML                    = "xml"
	DOBLayout                   = "02/01/2006"
	Pan                         = "pan"
	Aadhaar                     = "aadhaar"
	INDCountryCode              = "+91"
	PDFExtension                = ".pdf"
	Poa                         = "poa"
	DOBDateTimeLayout           = "2006-01-02 15:04:05"
	TimeFormatYyyyMmDdHhMmSs    = "2006-01-02 15:04:05"
	TimeFormatYyyyMmDdHhMmSsSss = "2006-01-02 15:04:05.000"
	ISO8601Format               = "2006-01-02T15:04:05Z"
	YesString                   = "Yes"
	NoString                    = "No"
	CvlKraDobSource             = "cvlKra"
	DateFormatWithMonthName     = "02 Jan 2006"
)

// Digio Constants.
const (
	SuccessCallBackParam              = "a1SuccessCallBack"
	FailureCallBackParam              = "a1FailureCallBack"
	ToastMessageParam                 = "ToastMessage"
	AadhaarDigioDigilockerPanFilename = "DigioPan"
	AadhaarDigilockerPanPDFExt        = ".pdf"
	Message                           = "message"
	AadhaarDigilockerPhotoFilename    = "Photo"
	AadhaarDigilockerPhotoFileExt     = ".jpeg"
	AadhaarDigilockerXMLFilename      = "Digilocker"
	AadhaarDigilockerXMLFileExt       = ".xml"
	AadhaarDigio                      = "digio"
	AutomationDigio                   = "automation"
	OnlyAadhaarDigioSource            = "digio-poa"
	AadhaarDigilockerPDFFilename      = "AadhaarFile"
)

// Digilocker Constants.
const (
	AngelDigilockerSource     = "angel-digilocker"
	AngelDigilockerFolderName = "Angel-digilocker"
)

// address.
const (
	PermanentAddressType         = "P"
	CorrespondenceAddressType    = "C"
	AddressLine1Key              = "address_line_1"
	AddressLine2Key              = "address_line_2"
	AddressLine3Key              = "address_line_3"
	Pincode                      = "pincode"
	City                         = "city"
	State                        = "state"
	Country                      = "country"
	India                        = "India"
	AdministrativeAreaLevelOne   = "administrative_area_level_1"
	AdministrativeAreaLevelThree = "administrative_area_level_3"
	Locality                     = "locality"
	PostalCode                   = "postal_code"
	INDIA                        = "INDIA"
	USACountry                   = "US"
)

// Digio Gender Constants.
const (
	DigioSingleCharacterMaleGender   = "M"
	DigioSingleCharacterFemaleGender = "F"
	DigioSingleCharacterOthersGender = "T"
	DigioMaleGender                  = "MALE"
	DigioFemaleGender                = "FEMALE"
	DigioOthersGender                = "OTHERS"
)

// KYC2 Gender Constants.
const (
	KYC2MaleGender   = "MALE"
	KYC2FemaleGender = "FEMALE"
	KYC2OthersGender = "OTHERS"
	KRAMaleGender    = "M"
	KRAFeMaleGender  = "F"
	KRAOthersGender  = "T"
)

// KYC2 Marital Status Constants.
const (
	KYC2SingleMaritalStatus  = "UNMARRIED"
	KYC2MarriedMaritalStatus = "MARRIED"
	KRASingleMaritalStatus   = "02"
	KRAMarriedMaritalStatus  = "01"
	KRAOtherMaritalStatus    = "03"
)

const (
	GenderTypeTransgender = "TRANSGENDER"
)

// Limit constants.
const (
	MaxAllowedAppsForPan = 2
	BankCodeSize         = 4
)

// income ranges.
const (
	IncomeLessThan5Lakhs  = "ONE_TO_FIVE_LAKHS_INR"
	Income5To10Lakhs      = "FIVE_TO_TEN_LAKHS_INR"
	Income10To25Lakhs     = "TEN_TO_TWENTY_FIVE_LAKHS_INR"
	IncomeMoreThan25Lakhs = "TWENTY_FIVE_LAKHS_TO_ONE_CRORE_INR"
)

// signature labels.
const (
	Text = "Text"
)

// poa types.
const (
	AadhaarPOAType        = "aadhaar"
	DigilockerPOAType     = "digilocker"
	PassportPOAType       = "passport"
	DrivingLicensePOAType = "drivinglicense"
	VoterCardPOAType      = "votercard"
)

// imps defaults.
const (
	IMPSDefaultAddress  = "Mumbai"
	IMPSDefaultSourceID = "2021"
	IMPSDefaultSegment  = "eq"
)

// Possible invalid full names received while trying to fetch IMPS details from KYC 1.0.
const (
	Unregistered    = "UNREGISTERED"
	NoName          = "NO NAME"
	RNSB            = "RNSB"
	BCB             = "BCB"
	PJSB            = "PJSB"
	IMPSCustomer    = "IMPS CUSTOMER"
	IMPSUser        = "IMPSUSER"
	ECollectAccount = "E-COLLECT ACCOUNT"
	DNSB            = "DNSB"
	Cosmos          = "COSMOS"
)

const (
	BankCheque          = "bankCheque"
	CancelledCheque     = "cancelledCheque"
	BankStatement       = "bankStatement"
	MarriageCertificate = "marriageCertificate"
)

// notification modes.
const (
	SMS   = "sms"
	Email = "email"
	Push  = "push"
)

// esign redirection constants.
const (
	TypeKey             = "type"
	SetuESignType       = "SETU"
	HypervergeESignType = "HYPERVERGE"
	ESignProvider       = "esp"
	ErrorCode           = "errorCode"
	AadhaarMismatch     = "AADHAAR-NUMBER-MISMATCH"
)

// email sources.
const (
	Google = "GOOGLE"
	User   = "user"
)

// email token types.
const (
	CredentialToken = "credentialToken"
)

// source keys.
const (
	SparkWeb  = "spark-web"
	Spark     = "spark"
	SparkDIY  = "spark-diy"
	ONRSource = "onr"
)

const (
	Kyc       = "kyc"
	KycPrefix = "kyc-"
	Intent    = "intent"
	KycAdmin  = "kyc-admin"
)

const (
	RNE = "RNE"
)

// referral types.
const (
	B2C = "B2C"
	B2B = "B2B"
	DRA = "DRA"
	SB  = "SB"
)

const (
	Mobile  = "mobile"
	AgentID = "agentId"
)

// lead block reasons.
const (
	B2BAndB2BLeadBlocked = "B2BAndB2BLeadBlocked"
	B2CAndB2BLeadBlocked = "B2CAndB2BLeadBlocked"
	B2BAndB2CLeadBlocked = "B2BAndB2CLeadBlocked"
	DeleteLeadB2BAndB2B  = "B2BAndB2BLeadDeleted"
	DeleteLeadB2BAndB2C  = "B2BAndB2CLeadDeleted"
	DeleteLeadB2CAndB2B  = "B2CAndB2BLeadDeleted"
)

// flows.
const (
	Start = "start"
)

// workflows.
const (
	PayOutDataPushWorkflow = "payout-data-push"
)

// emod bank account types.
const (
	Savings = "S"
)

// integer constants.
const (
	IntegerOne            = 1
	IntegerTwo            = 2
	IntegerFive           = 5
	MillisecondsInASecond = 1000
)

// ocr types.
const (
	BankChequeOCRType = "bankCheque"
)

// app number types.
const (
	EQ = "EQ"
	NR = "NR"
	SP = "SP"
)

// onboarding types.
const (
	EQUITY = "EQUITY"
	NRI    = "NRI"
)

// settlement frequency constants.
const (
	Quarterly = "quarterly"
	No        = "N"
	YesStr    = "Y"
	Source    = "SYSTEM|KYC"
)

// data placeholder constants.
const (
	FullNameKey            = "fullNameKey"
	AppNumberKey           = "appNumberKey"
	SettlementFrequencyKey = "settlementFrequencyKey"
	SubmissionDateKey      = "submissionDateKey"
)

const (
	DIYESignCommunicationType   = "diy-esign"
	DIYJourneyCommunicationType = "diy-journey"
)

const (
	DIYMessageIDPrefix = "DIY"
)

// LSQ constants.
const (
	LSQClientTypeB2B         = "B2B"
	LSQClientTypeB2C         = "B2C"
	LSQJourneyKRACompliant   = "KRA Compliant"
	LSQJourneyDigilocker     = "Digilocker"
	LSQJourneyOnlineAadhaar  = "Online Aadhaar"
	LSQJourneyManual         = "Manual Journey"
	LSQDiy                   = "DIY"
	IPVTypeUpload            = "Upload"
	NetMarginAmountZero      = "0"
	ReferrerClientID         = "referrer"
	LeadSource               = "rne_source"
	LeadMedium               = "rne_field2"
	LeadCampaign             = "rne_campaign"
	LSQDRABranch             = "RFRL"
	SubmittedByDAE           = "submitted by dae"
	DeletedStatus            = "Deleted"
	UploadedStatus           = "Uploaded"
	NotUploadedStatus        = "Not Uploaded"
	OptedIn                  = "Opted-in"
	OptedOut                 = "Opted-out"
	B2BReferralType          = "B2B"
	B2CReferralType          = "B2C"
	Itrade                   = "itrade"
	SBReferralType           = "SB"
	ItradeRNE                = "ITRADE_RNE"
	ParentRNE                = "Parent_RNE"
	ParentTagNC              = "ParentTag_NC"
	LSQCodingStatusInProcess = "In Process"
	Referral                 = "referral"
)

// SSO Login constants.
const (
	EmployeeID = "employeeID"
	AdminPanel = "adminPanel"
)

// nsdl dummy values.
const (
	NsdlDummyDoB  = "08/07/1992"
	NsdlDummyName = "."
)

const (
	MobileCode = "91"
)

// cams journey types.
const (
	Correction = "correction"
	Onboarding = "onboarding"
)

// commodity brokerage option constants.
const (
	ZINC     = "Zinc"
	COPPER   = "Copper"
	GOLD     = "GOLD"
	SILVER   = "SILVER"
	CRUDEOIL = "CRUDEOIL"
	ALL      = "ALL"
)

// referral plan names.
const (
	ITradePrimeVBB      = "iTrade Prime VBB"
	B2bAssistedPlanName = "Value Add Plan"
	ITradePremier       = "iTrade Premier"
)

// name match and dob match sources for pan.
const (
	PanSource    = "PAN"
	POASource    = "POA"
	BankSource   = "BANK"
	CVLKRASource = "CVLKRA"
	UserSource   = "USER"
)

const (
	CamsBankStatementPath = "/derivatives/CamsBankStatement"
)

// sqs message types.
const (
	LeadAttributeProcessingWebQueue = "leadAttributeProcessingWebQueue"
)

// one kyc flow status.
const (
	BankFlowStatusVariableKey          = "bankFlowStatus"
	PanFlowStatusVariableKey           = "panFlowStatus"
	SignatureFlowStatusVariableKey     = "signatureFlowStatus"
	PoaFlowStatusVariableKey           = "poaFlowStatus"
	SelfieFlowStatusVariableKey        = "selfieFlowStatus"
	EmailFlowStatusVariableKey         = "emailFlowStatus"
	DerivativesStatusVariableKey       = "derivativesFlowStatus"
	ClientDetailsFlowStatusVariableKey = "clientDetailsFlowStatus"
	ESignFlowStatusVariableKey         = "eSignFlowStatus"
	NomineeFlowStatusVariableKey       = "nomineeFlowStatus"
)

// camunda process states.
const (
	ProcessInstanceActiveState = "ACTIVE"
)

const (
	TWENTYFIVE = 25
	THIRTY     = 30
)

const (
	PhysicalMF            = "mf_physical"
	PhysicalMFOnlyJourney = "PHY_MF"
	PhysicalMFDMATJourney = "DEM_EQ_PHY_MF"
	DemEqDemMfJourney     = "DEM_EQ_DEM_MF"
	NRIJourney            = "NRI"
)

const (
	StringDateFormatDdMmmYyyy    = "02-Jan-2006"
	TimeFormatYyyyMmDdWithHyphen = "2006-01-02"
	PhysicalMFToEquity           = "physical_mf_to_equity"
	PhysicalMFEquityJourney      = "DEM_EQ"
)

const (
	Experian                     = "experian"
	ExperianCreditScoreStatement = "ExperianStatement"
)

// digio statuses.
const (
	DigioInitiated  = "Initiated"
	DigioRedirected = "Redirected"
)

// digio statuses.
const (
	CamsInitiated  = "Initiated"
	CamsRedirected = "Redirected"
)

const (
	PercentageRangeSeparator = "-"
)

const (
	ApplicationRolloutOnekycMigrationConfigKey      = "onekycMigration"
	ApplicationRolloutOnekycV2ConfigKey             = "onekycV2"
	ApplicationRolloutPanImageConfigKey             = "panImage"
	ApplicationRolloutDigioMandatoryConfigKey       = "digioMandatory"
	ApplicationRolloutSelfieRejectionConfigKey      = "selfieRejection"
	ApplicationRolloutSignatureRejectionConfigKey   = "signatureRejection"
	ApplicationRolloutDiyRejectionJourneyAdoption   = "diyRejectionJourneyAdoption"
	ApplicationRolloutPartnerAssistConfigKey        = "partnerAssist"
	ProcessInstanceIDKey                            = "processInstanceId"
	ApplicationRolloutDerivativesOCRConfigKey       = "derivativesOCR"
	ApplicationRolloutRegisterEmailReorderConfigKey = "registerEmailReorder"
	ApplicationRolloutRegisterV2ConfigKey           = "registerV2"
)

const (
	ESignDeleteMaxAllowedLength = 50
)
const (
	MessageType = "MessageType"
	Metadata    = "metadata"
)

const (
	ApplicationStatusOpen           = "Open"
	ApplicationStatusSubmitted      = "Submitted"
	ApplicationStatusSTPReject      = "STP Reject"
	ApplicationStatusScrutinyReject = "Rejected"
	ApplicationStatusDIY            = "DIY"
	ApplicationStatusCoded          = "Coded"
)

const (
	AddressStep = "address"
	BankStep    = "bank"
	FNOStep     = "fno"
	OVERSEA     = "oversea"
	INDIAN      = "indian"
)

const (
	MaxRetryForSendMessageInPrismSqs = 3
)

const (
	SetNo = "no"
)
const (
	BeneficiaryNameIsEmpty = "beneficiary name is Empty"
)

const (
	ACTIVE = "ACTIVE"
	CDSL   = "CDSL"
)

const (
	ImageMimePrefixJPEG = "data:image/jpeg;base64,"
)
