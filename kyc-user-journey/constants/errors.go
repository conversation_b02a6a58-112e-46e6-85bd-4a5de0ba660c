package constants

import (
	"net/http"

	"github.com/angel-one/go-utils/errors"
)

const ToastMessageCategory = "ToastMessage"
const InternalCategory = "Internal"
const UserCategory = "User"
const ToastMessageWithRefreshCategory = "ToastMessageWithRefresh"

var (
	ErrRecordNotFound          = errors.New("record not found in database")
	ErrIncorrectPassword       = errors.New("pdfcpu: please provide the correct password")
	ErrInvalidDateOfBirth      = errors.New("Invalid Date of Birth")
	ErrFetchingAadhaar         = errors.New("Error Fetching aadhaar")
	ErrFetchingPan             = errors.New("Error Fetching Pan")
	ErrPdfCPUIncorrectPassword = errors.New("pdfcpu: please provide the correct password")
	ErrNoMessageBody           = errors.New("no message body")
	ErrKRAInCorrectDOB         = errors.New("error incorrect DOB")
	ErrPanicRecovered          = errors.New("recovered from panic")
	ErrClcmDataFetchIssue      = errors.New("not able to fetch data from clcm api for clientCode")
	ErrorFetchClcmNominee      = errors.New("error fetching clcm nominee")
	ErrFetchingPhysicalMFdata  = errors.New("error fetching physical mfdata")
	ErrFetchingAadharImage     = errors.New("Error Fetching AAdhar Image")
)

// 4xx.
var (
	ErrUnauthorized                 = errors.Error{StatusCode: http.StatusUnauthorized, Code: "ERR-UNAUTHORIZED", Message: "Unauthorised. To prevent any errors that you may be encountering, please log out and log back in", Category: ToastMessageCategory}
	ErrInvalidAuthenticationRequest = errors.Error{StatusCode: http.StatusUnauthorized, Code: "ERR-INVALID-AUTH", Message: "invalid authentication", Category: ToastMessageCategory}
	ErrUnauthorizedAPIAccess        = errors.Error{StatusCode: http.StatusUnauthorized, Code: "ERR-UNAUTHORIZED-API-ACCESS", Message: "access to the API for this journey is not allowed.", Category: ToastMessageCategory}

	ErrPersonalDetailsNonIndividual = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-EMOD-PD-NON-INDIVIDUAL",
		Message: "Personal details cannot be updated for Non Individual Accounts", Category: ToastMessageCategory}
	ErrEModNotAllowedForNonIndividual         = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-EMOD-NOT-ALLOWED-FOR-NON-INDIVIDUAL-ACCOUNT", Message: "Online profile modification is not allowed for non individual account. Please contact support.", Category: ToastMessageCategory}
	ErrEModNotAllowedForMFOnlyUsers           = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-EMOD-NOT-ALLOWED-FOR-MF-ONLY-USERS", Message: "Online profile modification is not allowed for MF ONLY users. Please contact support.", Category: ToastMessageCategory}
	ErrIncomeUpdateNotAllowedForNonIndividual = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-INCOME-UPDATE-NOT-ALLOWED-FOR-NON-INDIVIDUAL-ACCOUNT", Message: "Income update not allowed for non individual accounts", Category: ToastMessageCategory}
	ErrPersonalDetailsMultipleHolder          = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-EMOD-PD-MULTIPLE-HOLDER", Message: "Personal details cannot be updated for accounts with multiple holders", Category: ToastMessageCategory}
	ErrKraStatusNotEligible                   = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-KRA-STATUS-ELIGIBLE", Message: "KRA Status Not Eligible", Category: ToastMessageCategory}
	ErrMultipleHolderAccount                  = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-DDPI-NOT-ALLOWED", Message: "DDPI not allowed for accounts with multiple holders", Category: ToastMessageCategory}
	ErrUserNotDormant                         = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-USER-NOT-DORMANT", Message: "User is not dormant", Category: ToastMessageCategory}
	ErrDuplicateEmodRequest                   = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-DUPLICATE-EMOD-REQUEST",
		Message: "Previous Modification request is in progress, kindly wait for it to complete before submitting a new one.", Category: ToastMessageCategory}
	ErrAnotherEmodRequestInProgress = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-ANOTHER-EMOD-REQUEST-IN-PROGRESS",
		Message: "Previous Modification request is in progress, kindly wait for it to complete before submitting a new one.", Category: ToastMessageCategory}
	ErrAnotherEmod10RequestInProgress = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-EMOD-1.0-REQUEST-IN-PROGRESS",
		Message: "Previous Modification request is in progress, kindly wait for it to complete before submitting a new one.", Category: ToastMessageCategory}
	ErrKYCNotCompleted         = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-KYC-NOT-COMPLETED", Message: "User kyc is not completed", Category: ToastMessageCategory}
	ErrInactiveProfile         = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-INACTIVE-PROFILE", Message: "The account has been flagged as inactive. No modifications are permitted for inactive accounts.", Category: ToastMessageCategory}
	ErrPleaseReachOut          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PLEASE-REACH", Message: "Modification not allowed. Please contact support", Category: ToastMessageCategory}
	ErrOngoingMFtoDematJourney = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ONGOING-MF-TO-DEMAT", Message: "There is an active mf to demat journey in progress", Category: ToastMessageCategory}
	ErrMinorEmodUser           = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-MINOR-USER", Message: "Online modification not allowed for minor accounts. Please contact support", Category: ToastMessageCategory}

	ErrMissingSource                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SOURCE", Message: "source is mandatory"}
	ErrUnsupportedSource                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNSUPPORTED-SOURCE", Message: "source is not supported"}
	ErrMissingPlatform                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PLATFORM", Message: "platform is mandatory"}
	ErrUnsupportedPlatform                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNSUPPORTED-PLATFORM", Message: "platform is not supported"}
	ErrMissingAppVersion                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-APPVERSION", Message: "appVersion is mandatory"}
	ErrInvalidAppVersion                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-APPVERSION", Message: "appVersion not supported"}
	ErrCorrectionJourneyNotFound             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CORRECTION-JOURNEY-NOT-FOUND", Message: "KYC Resubmission journey does not exist."}
	ErrFetchingCorrectionJourney             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FETCHING-CORRECTION-JOURNEY", Message: "correction journey does not exist"}
	ErrFetchingEmodCorrectionSignatureData   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FETCHING-EMOD-CORRECTION-SIG-DATA", Message: "error fetching data"}
	ErrEmodCorrectionSignatureNotEligible    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMOD-CORRECTION-SIG-NOT-ELIGIBLE", Message: "Not eligible for the journey.", Category: ToastMessageCategory}
	ErrMultipleActiveCorrectionJourneysFound = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MULTIPLE-ACTIVE-CORRECTION-JOURNEYS-FOUND", Message: "we currently do not support multiple active correction journeys"}
	ErrMissingSelfieImageFile                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SELFIE-FILE", Message: "selfie file is missing in request"}
	ErrMissingSelfieImageContentType         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SELFIE-IMAGE-CONTENT-TYPE", Message: "Content type is missing in selfie request header"}
	ErrInvalidSelfieImageContentType         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-SELFIE-IMAGE-CONTENT-TYPE", Message: "this selfie content type is not valid"}
	ErrSelfieFileSizeExceeded                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELFIE-FILE-SIZE-EXCEEDED", Message: "File size too big, kindly upload less then 15 MB file size", Category: ToastMessageCategory}
	ErrIDPathNotFound                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ID-PATH-NOT-FOUND", Message: "id path not found"}
	ErrSelfieLivelinessScore                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELFIE-LIVELINESS", Message: "Invalid selfie liveliness score", Category: ToastMessageCategory}
	ErrSelfieFileNotFound                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELFIE-FILE-NOT-FOUND", Message: "Selfie file not found"}
	ErrMissingSignatureImageFile             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SIGNATURE-FILE", Message: "signature file is missing in request"}
	ErrMissingSignatureImageContentType      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SIGNATURE-IMAGE-CONTENT-TYPE", Message: "Content type is missing in signature request header"}
	ErrInvalidSignatureImageContentType      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-SIGNATURE-IMAGE-CONTENT-TYPE", Message: "Only images are supported"}
	ErrSignatureFileSizeExceeded             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SIGNATURE-FILE-SIZE-EXCEEDED", Message: "File size too big, kindly upload less then 15 MB file size", Category: ToastMessageCategory}
	ErrSignatureFileNotFound                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SIGNATURE-FILE-NOT-FOUND", Message: "Signature file not found"}
	ErrExpiredOTPEmail                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EXPIRED-EMAIL-OTP", Message: "OTP expired", Category: ToastMessageCategory}
	ErrInvalidOTPEmail                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-EMAIL-OTP", Message: "Invalid OTP", Category: ToastMessageCategory}
	ErrAlreadyUsedOTPEmail                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-USED-EMAIL-OTP", Message: "Email OTP already used", Category: ToastMessageCategory}
	ErrInvalidEmail                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-EMAIL", Message: "Email is invalid", Category: ToastMessageCategory}
	ErrSelfieNotMatched                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELFIE-NOT-MATCHED", Message: "Selfie did not match with the uploaded identity proofs", Category: ToastMessageCategory}
	ErrMissingPAN                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN", Message: "PanDetails is missing"}
	ErrInValidPAN                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAN", Message: "Please enter valid pan number", Category: ToastMessageCategory}
	ErrMissingFatherName                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NAME", Message: "father's name is missing", Category: ToastMessageCategory}
	ErrInValidFatherName                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NAME", Message: "Please enter valid father's name", Category: ToastMessageCategory}
	ErrDuplicatePAN                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-PAN", Message: "This PAN Card is already registered with an account", Category: ToastMessageCategory}
	ErrMissingDigioDocID                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-DOC-ID", Message: "doc id is missing"}
	ErrMissingStatus                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-STATUS", Message: "status is missing"}
	ErrMissingRequestData                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-REQUEST-DATA", Message: "Request data is missing"}
	ErrMissingAppNumber                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-APP-NUMBER", Message: "app number is mandatory"}
	ErrTooManyAppNumbers                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-TOO-MANY-APP-NUMBERS", Message: "Too many app_numbers"}
	ErrParsingDOB                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PARSING-DOB", Message: "Error parsing dob"}
	ErrMinorApplication                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MINOR-APPLICATION", Message: "You must be at least 18 years of age to open an account online", Category: ToastMessageCategory}
	ErrAgeLimitExceeded                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-AGE-LIMIT-EXCEEDED", Message: "Currently, we don't accept applications of people aged over 99 years", Category: ToastMessageCategory}
	ErrPANMisMatch                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-MISMATCH", Message: "Digio PAN and client PAN do not match", Category: ToastMessageCategory}
	ErrSEBIBannedPAN                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SEBI-BANNED-PAN", Message: "This pan is banned by SEBI", Category: ToastMessageCategory}
	ErrPANAadhaarSeeding                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-AADHAAR-SEEDING", Message: "Pan is not linked with aadhaar", Category: ToastMessageCategory}
	ErrMissingPan                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN", Message: "pan is mandatory"}
	ErrInvalidPan                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAN", Message: "pan is invalid"}
	ErrInvalidPanWithCategory                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAN-WITH-CATEGORY", Message: "pan is invalid", Category: ToastMessageCategory}
	ErrInvalidDOB                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DOB", Message: "Invalid Date of birth", Category: ToastMessageCategory}
	ErrPanDoesNotMatch                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-DOES-NOT-MATCH", Message: "PAN incorrect. Please enter a valid PAN or validate through DoB", Category: ToastMessageCategory}
	ErrDOBDoesNotMatch                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DOB-DOES-NOT-MATCH", Message: "Date of birth incorrect. Please enter a valid DoB or validate through PAN", Category: ToastMessageCategory}
	ErrValueDoesNotMatch                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DOB-DOES-NOT-MATCH", Message: "the entered value does not match", Category: ToastMessageCategory}
	ErrMissingClientCode                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CLIENT-CODE"}
	ErrMissingIPAddress                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-IP-ADDRESS"}
	ErrNoBankAccount                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-ACCOUNT"}
	ErrMissingCorrectionJourneyType          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CORRECTION-JOURNEY-TYPE", Message: "journey type is missing"}
	ErrDDPIStatusAlreadyActive               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-ALREADY-ACTIVE", Message: "DDPI for the user is already active", Category: ToastMessageCategory}
	ErrDDPIStatusNotActive                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-NOT-ACTIVE", Message: "DDPI for the user is not active", Category: ToastMessageCategory}
	ErrDDPIDisabledForAsbaUsers              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-NOT-ALLOWED", Message: "DDPI is disabled for ASBA users", Category: ToastMessageCategory}
	ErrDDPIAllowedAfterAsbaDeactivation      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-NOT-ALLOWED", Message: "DDPI can be activated once ASBA is deactivated", Category: ToastMessageCategory}
	ErrMtfStatusAlreadyActive                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MTF-ALREADY-ACTIVE", Message: "MTF for the user is already active", Category: ToastMessageCategory}
	ErrBindingRequest                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BINDING-JSON", Message: "error binding request json"}
	ErrInvalidMobile                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-MOBILE", Message: "Invalid Mobile Number"}
	ErrExpiredOTPMobile                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EXPIRED-MOBILE-OTP", Message: "OTP expired", Category: ToastMessageCategory}
	ErrIncorrectOTPMobile                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INCORRECT-MOBILE-OTP", Message: "Incorrect OTP", Category: ToastMessageCategory}
	ErrAlreadyUsedOTPMobile                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-USED-MOBILE-OTP", Message: "Mobile OTP already used", Category: ToastMessageCategory}
	ErrMissingMobile                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-MOBILE", Message: "Mobile is Mandatory to send SMS"}
	ErrMissingEmailID                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-EMAIL-ID", Message: "Email id is Mandatory"}
	ErrMissingTemplateID                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-TEMPLATE-ID", Message: "Template id is Mandatory"}
	ErrMissingClientID                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CLIENT-ID", Message: "Client id is Mandatory for push notification"}
	ErrInvalidEmailID                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-EMAIL-ID", Message: "Email id is invalid"}
	ErrNoSegmentSelected                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NO-SEGMENT", Message: "No Segments Selected", Category: ToastMessageCategory}
	ErrCashSegmentDeactivationNotAllowed     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CASH-SEGMENT-DEACTIVATION-NOT-ALLOWED", Message: "Cash Segment cannot be deactivated", Category: ToastMessageCategory}
	ErrFnOAlreadyActive                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FNO-ALREADY-ACTIVE", Message: "FnO Segment is already active", Category: ToastMessageCategory}
	ErrCurrencyAlreadyActive                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CURRENCY-ALREADY-ACTIVE", Message: "Currency Segment is already active", Category: ToastMessageCategory}
	ErrCommodityAlreadyActive                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-COMMODITY-ALREADY-ACTIVE", Message: "Commodity Segment is already active", Category: ToastMessageCategory}
	ErrFnOAlreadyDeActivated                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FNO-ALREADY-DEACTIVATED", Message: "FnO Segment is already deactivated", Category: ToastMessageCategory}
	ErrCurrencyAlreadyDeActivated            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CURRENCY-ALREADY-DEACTIVATED", Message: "Currency Segment is already deactivated", Category: ToastMessageCategory}
	ErrCommodityAlreadyDeActivated           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-COMMODITY-ALREADY-DEACTIVATED", Message: "Commodity Segment is already deactivated", Category: ToastMessageCategory}
	ErrCashAlreadyDeActivated                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CASH-ALREADY-DEACTIVATED", Message: "Cash Segment is already deactivated", Category: ToastMessageCategory}
	ErrOpenOrders                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-OPEN-ORDERS", Message: "You have open orders. Please close them before deactivating the segment", Category: ToastMessageCategory}
	ErrOpenPositions                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-OPEN-POSITIONS", Message: "You have open positions. Please close them before deactivating the segment", Category: ToastMessageCategory}
	ErrNoBalance                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DETAILS IN LEDGER", Message: "Ledger Details not found for segments", Category: ToastMessageCategory}
	ErrLedgerBalanceIsNotZero                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-LEDGER-BALANCE-NOT-ZERO", Message: "You have a non-zero balance in your ledger", Category: ToastMessageCategory}
	ErrMissingBankAccountNumber              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-ACCOUNT-NUMBER", Message: "Account Number is missing", Category: ToastMessageCategory}
	ErrInValidBankAccountNumber              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-BANK-ACCOUNT-NUMBER", Message: "Account Number is invalid", Category: ToastMessageCategory}
	ErrMissingIfscCode                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-IFSC", Message: "ifsc code is missing", Category: ToastMessageCategory}
	ErrMissingIncomeRange                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-INCOME", Message: "income range is missing", Category: ToastMessageCategory}
	ErrInValidIfscCode                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-IFSC", Message: "ifsc code is invalid", Category: ToastMessageCategory}
	ErrExceededMaximumNumberOfBanks          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EXCEEDED-MAXIMUM-NUMBER-OF-BANKS", Message: "You have exceeded the maximum number of allowed banks for your account", Category: ToastMessageCategory}
	ErrMissingBankCheque                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-CHEQUE", Message: "bank cheque is missing in request", Category: ToastMessageCategory}
	ErrMissingBankChequeContentType          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-CHEQUE-CONTENT-TYPE", Message: "Content type is missing in emod bank cheque header", Category: ToastMessageCategory}
	ErrInvalidBankChequeContentType          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-BANK-CHEQUE-CONTENT-TYPE", Message: "this cheque content type is not valid"}
	ErrReadingBankData                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-READING-BANK-DATA", Message: "error reading bank data", Category: ToastMessageCategory}
	ErrMissingBankDetails                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-DETAILS", Message: "Account details or bank cheque is needed", Category: ToastMessageCategory}
	ErrUnAssociatedBankAccount               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNASSOCIATED-BANK-ACCOUNT", Message: "This account number is not registered with this client code", Category: ToastMessageCategory}
	ErrUnModifiedIFSC                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNMODIFIED-IFSC", Message: "The ifsc is not modified", Category: ToastMessageCategory}
	ErrConvertToInt                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CONVERT-TO-INT", Message: "Error converting to integer"}
	ErrDuplicateBankAccount                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-BANK-ACCOUNT", Message: "This bank account is already registered with another account. Please try with different bank", Category: ToastMessageCategory}
	ErrInvalidIntent                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-INTENT", Message: "intent is not supported"}
	ErrEmodJourneyNotFound                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMOD-JOURNEY-NOT-FOUND", Message: "No active emod application found", Category: ToastMessageCategory}
	ErrEmailMobileAlreadyExists              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMIAL-MOBILE-ALREADY-EXISTS", Message: "email/mobile sent for Emod already exists", Category: ToastMessageCategory}
	ErrMissingNominees                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEES", Message: "Nominee data is missing", Category: ToastMessageCategory}
	ErrExtraNominees                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EXTRA-NOMINEES", Message: "You can only add upto 3 nominees", Category: ToastMessageCategory}
	ErrMissingNomineeName                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-NAME", Message: "Nominee name missing", Category: ToastMessageCategory}
	ErrInvalidNomineeName                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-NAME", Message: "Nominee name invalid", Category: ToastMessageCategory}
	ErrInvalidNomineeShare                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-SHARE", Message: "Kindly provide proper share percentage to the nominees", Category: ToastMessageCategory}
	ErrMissingNomineeRelation                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-RELATION", Message: "Relationship with nominee missing", Category: ToastMessageCategory}
	ErrInvalidNomineeRelation                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-RELATION", Message: "Invalid relationship provided", Category: ToastMessageCategory}
	ErrInvalidGuardianNomineeRelation        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GUARDIAN-NOMINEE-RELATION", Message: "Invalid relationship with guardian provided", Category: ToastMessageCategory}
	ErrMissingNomineeAddress1                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-ADDRESS-1", Message: "Missing address line 1", Category: ToastMessageCategory}
	ErrMissingNomineePinCode                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-PINCODE", Message: "Missing pincode", Category: ToastMessageCategory}
	ErrMissingGuardianPan                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-PAN", Message: "Missing guardian's pan", Category: ToastMessageCategory}
	ErrInvalidGuardianPan                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GUARDIAN-PAN", Message: "Invalid guardian's pan", Category: ToastMessageCategory}
	ErrInvalidGuardianName                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GUARDIAN-NAME", Message: "Invalid guardian's name", Category: ToastMessageCategory}
	ErrSameNomineeGuardianPan                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-GUARDIAN-PAN", Message: "The same person cannot become both guardian and nominee. Please add different individuals.", Category: ToastMessageCategory}
	ErrNoPrimaryNominee                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NO-PRIMARY-NOMINEE-", Message: "Kindly mark any of the nominees as primary", Category: ToastMessageCategory}
	ErrMissingGuardianAddress1               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-ADDRESS-1", Message: "Missing guardian's address line 1", Category: ToastMessageCategory}
	ErrMissingGuardianPinCode                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-PINCODE", Message: "Missing guardian's pincode", Category: ToastMessageCategory}
	ErrMissingGuardianRelation               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-RELATION", Message: "Missing guardian's relation", Category: ToastMessageCategory}
	ErrMissingNomineeDocumentType            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-DOCUMENT-TYPE", Message: "Missing nominee's document type", Category: ToastMessageCategory}
	ErrMissingNomineeDocumentNumber          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-DOCUMENT-NUMBER", Message: "Missing nominee's document number", Category: ToastMessageCategory}
	ErrInvalidNomineeDocumentType            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-DOCUMENT-TYPE", Message: "Invalid nominee's document type", Category: ToastMessageCategory}
	ErrMinorGuardian                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MINOR-GUARDIAN", Message: "Guardian cannot be a minor", Category: ToastMessageCategory}
	ErrSubBrokerNominee                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SUBBROKER-NOMINEE", Message: "The PAN belongs to a SubBroker, SubBroker cannot be marked as a nominee", Category: ToastMessageCategory}
	ErrDDPiRequestIsAlreadyInProgress        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-ALREADY-IN-PROGRESS",
		Message:  "You have opt-in to activate DDPI during the account opening process. Your request for DDPI activation is currently being processed and is expected to be completed within 2 working days.",
		Category: ToastMessageCategory}
	ErrDDPINotAllowedForNonIndividual      = errors.Error{StatusCode: http.StatusForbidden, Code: "ERR-DDPI-NOT-ALLOWED-FOR-NON-INDIVIDUAL-ACCOUNT", Message: "DDPI not allowed for non individual accounts", Category: ToastMessageCategory}
	ErrBankNameMissing                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BANK-NAME-MISSING", Message: "bank code missing"}
	ErrNamesMisMatch                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NAMES-MIS-MATCH", Message: "Error names did not match", Category: ToastMessageCategory}
	ErrClientAndNomineeSamePAN             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CLIENT-AND-NOMINEE-SAME-PAN", Message: "Nominee PAN cannot be same as Client PAN", Category: ToastMessageCategory}
	ErrEmailAlreadyExists                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMAIL-MOBILE-ALREADY-EXISTS", Message: "Email already in use", Category: ToastMessageCategory}
	ErrMobileAlreadyExists                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MOBILE-ALREADY-EXISTS", Message: "Mobile already in use", Category: ToastMessageCategory}
	ErrHalfFilledLeadAlreadyExists         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-HALF-FILLED-LEAD-EXISTS", Message: "Mobile/Email already in use for some half-filled lead"}
	ErrDeleteBankNotAllowed                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DELETE-BANK-NOT-ALLOWED", Message: "Account Linking required. Please ensure that at least one bank account is linked.", Category: ToastMessageCategory}
	ErrDeletePrimaryBankNotAllowed         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DELETE-PRIMARY-BANK-NOT-ALLOWED", Message: "Primary bank cannot be deleted.", Category: ToastMessageCategory}
	ErrBindingDerivativeProof              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BINDING-DERIVATIVE-PROOF", Message: "Unable to bind derivative proof file"}
	ErrBindingDocumentProof                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BINDING-DOCUMENT-PROOF", Message: "Unable to bind document proof file"}
	ErrMissingDerivativeProof              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-DERIVATIVE-PROOF", Message: "No file uploaded for derivative proof", Category: ToastMessageCategory}
	ErrDerivativeProofSizeExceeded         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DERIVATIVE-PROOF-SIZE-EXCEEDED", Message: "File too large. Upload a file smaller than 10 MB", Category: ToastMessageCategory}
	ErrDocumentProofSizeExceeded           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DOCUMENT-PROOF-SIZE-EXCEEDED", Message: "File too large. Upload a file smaller than 10 MB", Category: ToastMessageCategory}
	ErrInvalidDerivativeProofType          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DERIVATIVE-PROOF-TYPE", Message: "Derivative proof type is not supported"}
	ErrInvalidDerivativeProof              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DOCUMENT-PROOF", Message: "Upload a valid Document proof!", Category: ToastMessageCategory}
	ErrEditedDerivativeProof               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EDITED-DOCUMENT-PROOF", Message: "Upload a valid and non modified Document proof!", Category: ToastMessageCategory}
	ErrInvalidDocumentType                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DOCUMENT-PROOF-TYPE", Message: "Document proof type is not supported", Category: ToastMessageCategory}
	ErrInvalidDerivativeProofContentType   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DERIVATIVE-PROOF-Content-TYPE", Message: "File type not supported. Only png/jpg/jpeg/pdf supported.", Category: ToastMessageCategory}
	ErrMatchingRegexForDocProofType        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MATCHING-REGEX-DOCUMENT-PROOF-TYPE", Message: "Error in matching regex for document proof type", Category: ToastMessageCategory}
	ErrInvalidDocumentProofContentType     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DERIVATIVE-PROOF-Content-TYPE", Message: "File type not supported. Only png/jpg/jpeg/pdf supported.", Category: ToastMessageCategory}
	ErrIncorrectFilePassword               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INCORRECT-FILE-PASSWORD", Message: "File password is incorrect", Category: ToastMessageCategory}
	ErrMissingFilePassword                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-FILE-PASSWORD", Message: "Please provide File Password", Category: ToastMessageCategory}
	ErrMissingBankChequeFile               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BANK-CHEQUE", Message: "bank cheque is missing in request"}
	ErrUnRegisteredIFSC                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNREGISTERED-IFSC", Message: "IFSC not found. Please contact support", Category: ToastMessageCategory}
	ErrCheckingIfPasswordProtected         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CHECKING-IF-PASSWORD-PROTECTED", Message: "Corrupted file uploaded. Try uploading another one.", Category: ToastMessageCategory}
	ErrInvalidNRISignatureProofContentType = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DERIVATIVE-PROOF-Content-TYPE", Message: "File type not supported. Only png/jpg/jpeg supported.", Category: ToastMessageCategory}

	ErrActiveCamsJourneyNotFound          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACTIVE-CAMS-JOURNEY-NOT-FOUND", Message: "No cams journey found"}
	ErrCamsUnsupportedPurpose             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CAMS-UNSUPPORTED-PURPOSE", Message: "cams purpose is not supported"}
	ErrCamsInvalidID                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CAMS-INVALID-ID", Message: "cams invalid id"}
	ErrMissingIncome                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-INCOME", Message: "It is mandatory to provide your income slab"}
	ErrInvalidIncome                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-INCOME", Message: "Choose valid income slab"}
	ErrMissingOccupation                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-OCCUPATION", Message: "It is mandatory to provide your employment type"}
	ErrInvalidOccupation                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-OCCUPATION", Message: "Choose valid employment type"}
	ErrInvalidOtherSpecifications         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-OTHER-SPECIFICATIONS", Message: "The occupation must be atleast 4 and not beyond 50 characters"}
	ErrMissingGender                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GENDER", Message: "It is mandatory to specify your gender."}
	ErrInvalidGender                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GENDER", Message: "Choose valid gender"}
	ErrMissingMaritalStatus               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-MARITAL-STATUS", Message: "It is mandatory to specify your marital status"}
	ErrInvalidMaritalStatus               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-MARITAL_STATUS", Message: "Choose valid marital status"}
	ErrMissingGuardianName                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-NAME", Message: "Missing guardian's name"}
	ErrBlockedIFSC                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BLOCKED-IFSC-CODE", Message: "This IFSC is not allowed", Category: ToastMessageCategory}
	ErrAccountClosureMissingReason        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-CLOSURE-MISSING-REASON", Message: "missing account closure reason"}
	ErrAccountClosureInvalidOthersComment = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-CLOSURE-INVALID-OTHERS-COMMENT", Message: "Enter a valid comment", Category: ToastMessageCategory}
	ErrAccountClosureInvalidReason        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-CLOSURE-INVALID-REASON", Message: "invalid account closure reason"}
	ErrAccountClosureNotEligible          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-CLOSURE-NOT-ELIGIBLE", Message: "Not eligible for account closure.", Category: ToastMessageCategory}
	ErrAccountClosureValidationFailed     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-CLOSURE-VALIDATION-FAILED", Message: "Account cannot be closed. User has ", Category: ToastMessageCategory}
	ErrAccountTransferNotEligible         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-TRANSFER-NOT-ELIGIBLE", Message: "Ineligible for account transfer.", Category: ToastMessageCategory}
	ErrMissingSettlementFrequency         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SETTLEMENT-FREQUENCY", Message: "Missing settlement-frequency"}
	ErrInvalidSettlementFrequency         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-SETTLEMENT-FREQUENCY", Message: "Invalid settlement-frequency"}
	ErrInvalidPageValue                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAGE-VALUE", Message: "Invalid page value"}
	ErrRequestedAndCurrentFrequencySame   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-REQUESTED-AND-CURRENT-FREQUENCY-SAME", Message: "Requested settlement frequency is same as existing one", Category: ToastMessageCategory}
	ErrMissingTradeToken                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-TRADETOKEN", Message: "Missing trade token"}
	ErrBlockedPaytmIFSC                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BLOCKED-PAYTM-IFSC", Message: "As per latest RBI guidelines, Paytm Payments Bank can not be added", Category: ToastMessageCategory}
	ErrBlockedNICBIFSC                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BLOCKED-NICB-IFSC", Message: "Funds transfer to or from New India Cooperative Bank Ltd. has been restricted as per RBI Directive", Category: ToastMessageCategory}
	ErrMissingCorrectionDIYFlows          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CORRECTION-DIY-FLOWS", Message: "diy flows missing", Category: ToastMessageCategory}
	ErrCorrectionDIYFlowNotSupported      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CORRECTION-DIY-FLOW-NOT-SUPPORTED", Message: "provided diy flow not supported", Category: ToastMessageCategory}
	ErrMaximumCommunicationsReached       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MAXIMUM-COMMUNICATIONS-REACHED", Message: "Reached Maximum number of allowed communications", Category: ToastMessageCategory}
	ErrFrequentCommunications             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FREQUENT-COMMUNICATIONS", Message: "Reached the limit of allowable communications at this time. Please try again later", Category: ToastMessageCategory}
	ErrPleaseLoginWithUpdatedNumber       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PLEASE-LOGIN-AGAIN", Message: "Please login with your newly updated phone number", Category: ToastMessageCategory}
	ErrActiveEsignJourneyNotFound         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACTIVE-ESIGN-JOURNEY-NOT-FOUND", Message: "No active esign journey found for given id"}
	ErrSetuAPIValidationFailed            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SETU-BAD-REQUEST", Message: "Bad request"}
	ErrHypervergeAPIValidationFailed      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-HYPERVERGE-BAD-REQUEST", Message: "Bad request"}

	ErrNotAllowedToSkipOnboardingSteps = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NOT-ALLOWED-TO-SKIP-ONBOARDING-STEPS", Message: "You are not allowed to skip onboarding steps for this mobile", Category: ToastMessageCategory}

	ErrMissingEmail                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-EMAIL", Message: "email is missing", Category: ToastMessageCategory}
	ErrTempDomain                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-TEMP-DOMAIN", Message: "Email with temporary domain is not allowed", Category: ToastMessageCategory}
	ErrDomainNotAllowed             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DOMAIN-NOT-ALLOWED", Message: "We do not support creating accounts with %s email IDs. Please use a different email.", Category: ToastMessageCategory}
	ErrDuplicateEmail               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-EMAIL", Message: "This email is already registered with an account", Category: ToastMessageCategory}
	ErrUnSupportedEmailSource       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNSUPPORTED-EMAIL-SOURCE", Message: "This email source is invalid", Category: ToastMessageCategory}
	ErrMissingEmailToken            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-EMAIL-TOKEN", Message: "Token is missing in request"}
	ErrBankChequeNotFound           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BANK-CHEQUE-NOT-FOUND", Message: "bank cheque not found"}
	ErrMissingTransactionID         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-TRANSACTION-ID", Message: "transaction ID is missing"}
	ErrInValidTransactionID         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-TRANSACTION-ID", Message: "transaction ID is in-valid"}
	ErrMissingAccountHolderName     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ACCOUNT-HOLDER-NAME", Message: "account holder name is missing"}
	ErrAccountNumberMisMatch        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-NUMBER-MISMATCH", Message: "account number mismatch"}
	ErrIFSCMisMatch                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-IFSC-MISMATCH", Message: "ifsc mismatch"}
	ErrAccountHolderNameMismatch    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ACCOUNT-HOLDER-NAME-MISMATCH", Message: "account holder name mismatch mismatch"}
	ErrOCRUpdateNotAllowed          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-OCR-UPDATE-NOT-ALLOWED", Message: "Please re-do the OCR."}
	ErrBankOCRNotPerformed          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-OCR-UPDATE-NOT-ALLOWED", Message: "Please try OCR before confirmation."}
	ErrAttestedFormFileSizeExceeded = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ATTESTED-FORM-FILE-SIZE-EXCEEDED", Message: "File size too big, kindly upload less then 100 MB file size", Category: ToastMessageCategory}

	ErrInvalidRelationship                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-RELATIONSHIP", Message: "Invalid Relationship", Category: ToastMessageCategory}
	ErrInValidNomineeData                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-DATA", Message: "One or more nominee/guardian PANs are in-valid", Category: ToastMessageCategory}
	ErrSameNomineeAndAccountHolderPAN                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-AND-ACCOUNT-HOLDER-PAN", Message: "Applicant and Nominee should be different persons", Category: ToastMessageCategory}
	ErrSameGuardianAndAccountHolderPAN                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-GUARDIAN-AND-ACCOUNT-HOLDER-PAN", Message: "Applicant and Guardian should be different persons", Category: ToastMessageCategory}
	ErrSameEmodNomineeAndAccountHolderPAN                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-AND-ACCOUNT-HOLDER-PAN", Message: "Account holder cannot be a nominee.", Category: ToastMessageCategory}
	ErrSameEmodGuardianAndAccountHolderPAN               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-GUARDIAN-AND-ACCOUNT-HOLDER-PAN", Message: "Account holder cannot be a guardian.", Category: ToastMessageCategory}
	ErrSameGuardianAndNomineePAN                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-GUARDIAN-AND-NOMINEE-PAN", Message: "Nominee and Guardian pan can't be same.", Category: ToastMessageCategory}
	ErrUniqueNomineePan                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNIQUE-NOMINEE-PAN", Message: "You've already added this person as a nominee. Please choose someone else to add multiple nominees to your demat account.", Category: ToastMessageCategory}
	ErrUniqueGuardianNomineePan                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNIQUE-GUARDIAN-NOMINEE-PAN", Message: "You must add a different guardian for each minor nominee", Category: ToastMessageCategory}
	ErrBindingAadhaarFrontImage                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BINDING-AADHAR-FRONT", Message: "Binding Aadhaar Front Image Failed"}
	ErrMissingAadhaarFrontImage                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AADHAR-FRONT", Message: "Aadhaar Front Image Missing", Category: ToastMessageCategory}
	ErrMissingAadhaarBackImage                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AADHAR-BACK", Message: "Aadhaar Back Image Missing", Category: ToastMessageCategory}
	ErrMissingAadhaarFrontImageContentType               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AADHAR-FRONT-CONTENT-TYPE", Message: "Aadhaar Front Image Content Type Missing"}
	ErrMissingAadhaarBackImageContentType                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AADHAR-BACK-CONTENT-TYPE", Message: "Aadhaar Back Image Content Type Missing"}
	ErrInvalidAadhaarFrontImageContentType               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-AADHAR-FRONT-CONTENT-TYPE", Message: "Invalid File, Please upload jpeg or png or jpg or pdf file", Category: ToastMessageCategory}
	ErrInvalidAadhaarBackImageContentType                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-AADHAR-BACK-CONTENT-TYPE", Message: "Invalid File, Please upload jpeg or jpg or png file", Category: ToastMessageCategory}
	ErrPasswordProtectedFileUploaded                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PASSWORD-PROTECTED-FILE-UPLOADED", Message: "Uploaded file is password protected, please upload new file", Category: ToastMessageCategory}
	ErrDifferentFrontAndBackAadhaar                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DIFFERENT-FRONT-AND-BACK-AADHAR", Message: "Uploaded Aadhaar Front and Back images are different", Category: ToastMessageCategory}
	ErrOCRNotPerformed                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-OCR-UPDATE-NOT-ALLOWED", Message: "Please try OCR before confirmation."}
	ErrBindingPanImage                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BINDING-PAN-IMAGE", Message: "Binding Pan Image Failed"}
	ErrMissingPanImage                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN-IMAGE", Message: "Missing Pan Image", Category: ToastMessageCategory}
	ErrMissingPANImageContentType                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN-IMAGE-CONTENT-TYPE", Message: "Pan Image Content Type Missing"}
	ErrInvalidPanImageContentType                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAN-IMAGE-CONTENT-TYPE", Message: "Please upload png or jpg or jpeg File", Category: ToastMessageCategory}
	ErrMissingAadhaar                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AADHAAR", Message: "Aadhaar Number is Missing", Category: ToastMessageCategory}
	ErrMissingAddressLine1                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ADDRESS-LINE-1", Message: "Address line 1 is missing", Category: ToastMessageCategory}
	ErrMissingPincode                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PINCODE", Message: "Missing Pincode", Category: ToastMessageCategory}
	ErrInvalidPincode                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PINCODE", Message: "Invalid Pincode", Category: ToastMessageCategory}
	ErrInvalidAadhaar                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-AADHAAR", Message: "Invalid Aadhaar Number", Category: ToastMessageCategory}
	ErrInvalidFullName                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-AADHAAR-NAME", Message: "Invalid Aadhaar Name", Category: ToastMessageCategory}
	ErrEmptyOtherSpecs                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMPTY-OTHER-SPECS", Message: "Please provide relationship with nominee", Category: ToastMessageCategory}
	ErrPanNameMisMatch                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-NAME-MISMATCH", Message: "Please provide the name as per PAN", Category: ToastMessageCategory}
	ErrPanDOBMisMatch                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-DOB-MISMATCH", Message: "Please provide the dob as per PAN", Category: ToastMessageCategory}
	ErrPanNameAndDOBMisMatch                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PAN-NAME-AND-DOB-MISMATCH", Message: "Please provide the name and dob as per PAN", Category: ToastMessageCategory}
	ErrMissingPanName                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN-NAME", Message: "pan name is mandatory", Category: ToastMessageCategory}
	ErrMissingOrderNo                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ORDER-NO", Message: "order no is mandatory", Category: ToastMessageCategory}
	ErrMissingSebiOrderDate                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SEBI-ORDER-DATE", Message: "sebi order date is mandatory", Category: ToastMessageCategory}
	ErrMissingStartDate                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-START-DATE", Message: "start date is mandatory", Category: ToastMessageCategory}
	ErrMissingBriefParticulars                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BRIEF-PARTICULAR", Message: "Brief Particular is mandatory", Category: ToastMessageCategory}
	ErrMissingPanDOB                                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PAN-DOB", Message: "pan dob is mandatory"}
	ErrInValidPanName                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PAN-NAME", Message: "pan name is invalid", Category: ToastMessageCategory}
	ErrMissingAuthType                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-AUTH-TYPE", Message: "auth type is mandatory"}
	ErrInvalidAuthType                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-AUTH-TYPE", Message: "auth type is invalid"}
	ErrMissingDOB                                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-DOB", Message: "dob is mandatory", Category: ToastMessageCategory}
	ErrDataDoesNotMatch                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DATA-DOES-NOT-MATCH", Message: "the entered data does not match with user's", Category: ToastMessageCategory}
	ErrMissingEmployeeID                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-EMPLOYEE-ID", Message: "Missing Employee Id"}
	ErrMissingFullName                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-FULL-NAME", Message: "name is missing"}
	ErrInValidName                                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NAME", Message: "Please enter valid name", Category: ToastMessageCategory}
	ErrPartnerNotLinkedToClient                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PARTNER-NOT-LINKED-TO-CLIENT", Message: "SubBroker is not linked to client", Category: ToastMessageCategory}
	ErrEmailTokenExpired                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMAIL-TOKEN-EXPIRED", Message: "Email token expired. Please retry", Category: ToastMessageCategory}
	ErrAsbaEnabledClient                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-ENABLED", Message: "Operation not allowed for UPI Mandate (ASBA) enabled users", Category: ToastMessageCategory}
	ErrAsbaEnabledClientSegments                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-ENABLED-SEGMENT-ACTIVATION", Message: "Please disable ASBA first in order to activate FnO/Commodity", Category: ToastMessageCategory}
	ErrAsbaEnabledClientMtf                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-ENABLED-MTF", Message: "Please disable ASBA first in order to activate MTF", Category: ToastMessageCategory}
	ErrBankNotSupportedForAsbaUser                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BANK-NOT-SUPPORTED-FOR-ASBA-USER", Message: "bank not supported as client is UPI Mandate (ASBA) enabled", Category: ToastMessageCategory}
	ErrAsbaDisabledClient                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-DISABLED", Message: "Operation not allowed for UPI Mandate (ASBA) disabled users", Category: ToastMessageCategory}
	ErrEsignAlreadyCompleted                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ESIGN-ALREADY-COMPLETED", Message: "esign is already completed", Category: ToastMessageCategory}
	ErrIncompleteFlow                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INCOMPLETE-FLOW", Message: "one/more flow is not completed", Category: ToastMessageWithRefreshCategory}
	ErrBankRegisteredWithSameUser                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BANK-REGISTERED-WITH-SAME-USER", Message: "This bank account is already registered with your account. You can update the details if needed", Category: ToastMessageCategory}
	ErrASBAActivationNotEligible                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-ACTIVATION-NOT-ELIGIBLE", Message: "Not eligible for asba activation.", Category: ToastMessageCategory}
	ErrUnSupportedAPIKey                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNSUPPORTED-API-KEY", Message: "This API Key is not supported.", Category: ToastMessageCategory}
	ErrFetchingJourney                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FETCHING-JOURNEY", Message: "journey does not exist"}
	ErrNoBrokerageDetailsFound                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-BROKERAGE-DETAILS-NOT-FOUND", Message: "error brokerage details not found"}
	ErrUnableToGetLocation                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-GETTING-USER-LOCATION", Message: "Unable to get location data"}
	ErrMissingScore                                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-SCORE", Message: "score is missing", Category: ToastMessageCategory}
	ErrInValidScore                                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-SCORE", Message: "Please provide valid score", Category: ToastMessageCategory}
	ErrAlreadyFrozenAccount                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ALREADY-FROZEN-ACCOUNT", Message: "Account is already in frozen state", Category: ToastMessageCategory}
	ErrAlreadyUnfrozenAccount                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ALREADY-UNFROZEN-ACCOUNT", Message: "Account is currently not frozen.", Category: ToastMessageCategory}
	ErrDuplicateCallback                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-CALL-BACK", Message: "duplicate call back call from setu"}
	ErrWhatsappNudgeNotSupported                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-WHATSAPP-NUDGE-NOT-SUPPORTED", Message: "whatsapp nudge is not supported for this application", Category: ToastMessageCategory}
	ErrPlanLimitExceeded                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PLAN-LIMIT-EXCEEDED", Message: "Exceeded supported number of plans"}
	ErrUnsupportedPlan                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-UNSUPPORTED-PLAN", Message: "This plan is not supported for this operation"}
	ErrMissingFile                                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-FILE", Message: "No file uploaded", Category: ToastMessageCategory}
	ErrFileSizeExceeded                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FILE-SIZE-EXCEEDED", Message: "File too large. Upload a file smaller than 10 MB", Category: ToastMessageCategory}
	ErrInvalidDocument                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-FILE", Message: "Invalid document key"}
	ErrFilePasswordProtected                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PASSWORD-PROTECTED", Message: "Please upload a non-password protected file", Category: ToastMessageCategory}
	ErrInvalidUserID                                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-CLIENT-CODE", Message: "Invalid client code"}
	ErrLocationAccessNotProvided                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-LOCATION-ACCESS-NOT-PROVIDED", Message: "Please provide location access", Category: ToastMessageCategory}
	ErrLocationOutsideIndia                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-LOCATION-OUTSIDE-INDIA", Message: "Provided location is outside India."}
	ErrMissingCustomerEmailID                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CUSTOMER-EMAIL", Message: "Customer's email ID is not available. Please add the email id before requesting selfie from customer.", Category: ToastMessageCategory}
	ErrNoPhyMfJourneyFound                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PHY-MF-JOURNEY", Message: "No Physical MF only journey found."}
	ErrNoPhyMfPlusDMATJourneyFound                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PHY-MF-JOURNEY", Message: "User already opted for DMAT."}
	ErrInvalidJourneyTypeFound                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-JOURNEY-TYPE", Message: "Invalid journey type."}
	ErrSameNomineePan                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-PAN", Message: "Account holder and nominee's PAN should not be the same", Category: ToastMessageWithRefreshCategory}
	ErrNomineeGuardianPanSame                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-GUARDIAN-PAN", Message: "Account holder and guardian's PAN should not be the same", Category: ToastMessageWithRefreshCategory}
	ErrClientCodeMissingInRequest                        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CLIENT-CODE", Message: "There was some error while fetching your data. Request you to logout and login again.", Category: ToastMessageCategory}
	ErrMissingESignDeleteAgentID                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ESIGN-DELETE-AGENT-ID", Message: "Missing Agent ID.", Category: ToastMessageCategory}
	ErrMissingESignDeleteReason                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ESIGN-DELETE-REASON", Message: "Esign Deletion Reason is mandatory.", Category: ToastMessageCategory}
	ErrESignDeleteReasonLengthExceeded                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ESIGN-DELETE-REASON-LENGTH_EXCEEDED", Message: "Esign Deletion Reason is exceeding the maximum allowed characters.", Category: ToastMessageCategory}
	ErrESignNotComplete                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ESIGN-NOT-COMPLETE", Message: "Esign is not complete.", Category: ToastMessageCategory}
	ErrMissingState                                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-STATE", Message: "Missing state", Category: ToastMessageCategory}
	ErrMissingCity                                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-CITY", Message: "Missing city", Category: ToastMessageCategory}
	ErrMissingCountry                                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-COUNTRY", Message: "Missing country", Category: ToastMessageCategory}
	ErrMissingType                                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-TYPE", Message: "Missing address type", Category: ToastMessageCategory}
	ErrMissingTradingExperience                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-TRADING-EXPERIENCE", Message: "Missing trading experience", Category: ToastMessageCategory}
	ErrMissingPassportNumber                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PASSPORT-NUMBER", Message: "Missing passport number", Category: ToastMessageCategory}
	ErrMissingBirthCountry                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-BIRTH-COUNTRY", Message: "Missing brith country", Category: ToastMessageCategory}
	ErrMissingNationality                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NATIONALITY", Message: "Missing nationality", Category: ToastMessageCategory}
	ErrMissingAddressType                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ADDRESS-TYPE", Message: "Missing address type", Category: ToastMessageCategory}
	ErrInvalidTradingExperience                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-TRADING-EXPERIENCE", Message: "Choose valid trading experience", Category: ToastMessageCategory}
	ErrASBADeactivationNotEligible                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ASBA-DEACTIVATION-NOT-ELIGIBLE", Message: "Not eligible for asba deactivation.", Category: ToastMessageCategory}
	ErrMissingResetFlow                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-RESET-FLOW", Message: "Reset Flow Name is mandatory.", Category: ToastMessageCategory}
	ErrInvalidResetFlow                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-RESET-FLOW", Message: "We currently don't support the reset of requested flow.", Category: ToastMessageCategory}
	ErrPhysicalEmodFileNotFound                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PHYSICAL-EMOD-FILE-NOT-FOUND", Message: "Physical emod file not found"}
	ErrMissingRoleID                                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ROLE-ID", Message: "missing role id", Category: ToastMessageCategory}
	ErrInvalidRoleID                                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-ROLE-ID", Message: "invalid role id", Category: ToastMessageCategory}
	ErrMissingMenuID                                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-MENU-ID", Message: "missing menu id", Category: ToastMessageCategory}
	ErrMissingGuardianType                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-TYPE", Message: "Missing guardian's type", Category: ToastMessageCategory}
	ErrInvalidOverseaCountry                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-OVERSEA-COUNTRY", Message: "India can not be Oversea country", Category: ToastMessageCategory}
	ErrInvalidIndianCountry                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-INDIAN-COUNTRY", Message: "Country needs to be india", Category: ToastMessageCategory}
	ErrInvalidNriAddressType                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NRI-ADDRESS-TYPE", Message: "Invalid address type", Category: ToastMessageCategory}
	ErrMissingAddressLine2                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-ADDRESS-LINE-2", Message: "Address line 2 is missing", Category: ToastMessageCategory}
	ErrAddressLine1isTooLong                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ADDRESS-LINE-1-TOO-LONG", Message: "Address line 1 is too long", Category: ToastMessageCategory}
	ErrAddressLine2isTooLong                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-ADDRESS-LINE-2-TOO-LONG", Message: "Address line 2 is too long", Category: ToastMessageCategory}
	ErrInvalidPassportNumber                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-PASSPORT-NUMBER", Message: "Invalid passport number", Category: ToastMessageCategory}
	ErrMissingTaxResidency                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-TAX-RESIDENCY", Message: "Missing tax residency country", Category: ToastMessageCategory}
	ErrMissingTinNumber                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-TIN-NUMBER", Message: "Invalid tin number", Category: ToastMessageCategory}
	ErrMissingPassportExpiry                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-PASSPORT-EXPIRY", Message: "Passport expiry is missing", Category: ToastMessageCategory}
	ErrNameDobAlreadyUpdatedInCLCM                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DETAILS-UPDATED-ALREADY", Message: "name and dob already updated in clcm", Category: ToastMessageCategory}
	ErrEmptyNameAndDob                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMPTY-NAME-AND-DOB", Message: "name and double both are empty", Category: ToastMessageCategory}
	ErrAccountReactivationNotEligible                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-REACTIVATION-NOT-ELIGIBLE", Message: "Account Status is not eligible for account reactivation.", Category: ToastMessageCategory}
	ErrAccountDeactivationNotEligible                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DEACTIVATION-NOT-ELIGIBLE", Message: "Account Status is not eligible for account deactivation.", Category: ToastMessageCategory}
	ErrInvalidFileFormat                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-FILE-FORMAT", Message: "File format is not supported", Category: ToastMessageCategory}
	ErrInvalidClientCode                                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-CLIENT-CODE", Message: "Invalid Client Code", Category: ToastMessageCategory}
	ErrInvalidAttestedFormContentType                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-ATTESTED-FORM-Content-TYPE", Message: "File type not supported. Only pdf supported.", Category: ToastMessageCategory}
	ErrMissingMobileInRequest                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-MOBILE", Message: "Mobile is Mandatory to fetch user-journey status"}
	ErrGettingSubBrokerCommDetails                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-SUBROKER-COMM-DETAILS", Message: "Error getting subbroker comm details"}
	ErrNriJourneyNotInProgress                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NRI-JOURNEY-NOT-IN-PROGRESS", Message: "Journey status is mismatch", Category: ToastMessageWithRefreshCategory}
	ErrMissingRBIApprovalNumber                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-RBI-APPROVAL-NUMBER", Message: "RBI Approval number is missing", Category: ToastMessageCategory}
	ErrMissingNRIBankAccountType                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NRI-BANK-ACCOUNT-TYPE", Message: "Bank account type is missing", Category: ToastMessageCategory}
	ErrSavingsAndInvestmentAccountBankDifferent          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAVINGS_INVESTMENT-BANK-DIFFERENT", Message: "Savings and PIS/Investment Bank account should be of same bank", Category: ToastMessageCategory}
	ErrInvalidNRIBankAccountType                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NRI-BANK-TYPE", Message: "Invalid bank type", Category: ToastMessageCategory}
	ErrDuplicateInvestmentBankAccount                    = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-INVESTMENT-BANK-ACCOUNT", Message: "This investment account bank account is already registered with another account. Please try with different bank", Category: ToastMessageCategory}
	ErrSameSavingsAndInvestmentBankAccount               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-SAVINGS-INVESTMENT-BANK-ACCOUNT", Message: "Savings and Investment account are same.", Category: ToastMessageCategory}
	ErrBankNameNotPartnerBank                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-BANK-NAME-PARTNER", Message: "selected bank doesn't belong to partner bank", Category: ToastMessageCategory}
	ErrBankAccountSelectedBankDifferent                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELECTED-BANK-SAVINGS-BANK-DIFFERENT", Message: "selected bank and savings bank are different", Category: ToastMessageCategory}
	ErrInvestmentBankAccountSelectedBankDifferent        = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SELECTED-BANK-INVESTMENT-BANK-DIFFERENT", Message: "selected bank and investment bank are different", Category: ToastMessageCategory}
	ErrCashSegmentNotOptedIn                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-CASH-SEGMENT-NOT-OPTED-IN", Message: "Please select cash segment", Category: ToastMessageCategory}
	ErrMissingDocumentFileProof                          = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-DOCUMENT-PROOF", Message: "No file uploaded for the selected proof", Category: ToastMessageCategory}
	ErrInvalidDocumentSubType                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DOCUMENT-PROOF-SUB-TYPE", Message: "Document proof sub type is not supported", Category: ToastMessageCategory}
	ErrInvalidDocumentKey                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-DOCUMENT-KEY", Message: "Document key is not supported", Category: ToastMessageCategory}
	ErrNoRecordsForPincode                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NO-RECORDS-FOR-PINCODE", Message: "No records against pincode", Category: ToastMessageCategory}
	ErrInvalidWealthSource                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-WEALTH-SOURCE", Message: "Please provide valid wealth source"}
	ErrMissingFieldsInRequest                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-FIELDS-IN-REQUEST", Message: "at least one field must be specified"}
	ErrNomineePincodeRecordsNotFound                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NO-RECORDS-FOR-NOMINEE-PINCODE", Message: "Please enter a valid pincode.", Category: ToastMessageCategory}
	ErrAadhaarNotMatch                                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-AADHAAR-NOT-MATCH", Message: "Aadhaar number is not matching"}
	ErrLeadNotFound                                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-LEAD-NOT-FOUND", Message: "Lead Not Found", Category: ToastMessageCategory}
	ErrMaxFetchExceeded                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MAX-FETCH-EXCEEDED", Message: "Exceeded maximum app numbers to be fetched", Category: ToastMessageCategory}
	ErrDddpiOptInNotAllowed                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DDPI-OPT-IN-NOT-ALLOWED", Message: "DDPI Opt-in is allowed after your account is opened ", Category: ToastMessageCategory}
	ErrInvalidAddressLine1                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-ADDRESS-LINE-1", Message: "Address line 1 is invalid", Category: ToastMessageCategory}
	ErrInvalidAddressLine2                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-ADDRESS-LINE-2", Message: "Address line 2 is invalid", Category: ToastMessageCategory}
	ErrFetchingAccountTypeFromCLCM                       = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-FETCHING-ACCOUNT-TYPE-FROM-CLCM", Message: "error in fetching account type from clcm"}
	ErrPincodeNotExistsInPincodeMaster                   = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PINCODE-NOT-EXIST-IN-PINCODE-MASTER", Message: "Pincode not exist in pincode master table", Category: ToastMessageCategory}
	ErrRequestedAndCurrentGenderSame                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-REQUESTED-AND-CURRENT-GENDER-SAME", Message: "Requested GENDER is same as existing one", Category: ToastMessageCategory}
	ErrDeleteAndUpdateOfSameBankNotAllowed               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DELETE-UPDATE-SAME-BANK-NOT-ALLOWED", Message: "deletion and updating of same bank is not allowed", Category: ToastMessageCategory}
	ErrSEBIBannedPanDetailsAlreadyExist                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SEBI-BANNED-PAN-ALREADY-EXISTS", Message: "Pan Details Already exists in sebi banned", Category: ToastMessageCategory}
	ErrInvalidSebiOrderDate                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-SEBI-ORDER-DATE", Message: "Invalid SEBI order date", Category: ToastMessageCategory}
	ErrInvalidStartDate                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-START-DATE", Message: "Invalid start date", Category: ToastMessageCategory}
	ErrEmptyAgentID                                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMPTY-AGENT-ID", Message: "empty admin id", Category: ToastMessageCategory}
	ErrPIIAccessAvailable                                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PII-ACCESS-AVAILABLE", Category: ToastMessageCategory}
	ErrNomineeGuardianPincodeRecordsNotFound             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NO-RECORDS-FOR-NOMINEE-GUARDIAN-PINCODE", Message: "Please enter a valid pincode for guardian.", Category: ToastMessageCategory}
	ErrInvalidFieldType                                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-FIELD-TYPE", Message: "Invalid field type while checking duplicate entries at nominee flow", Category: ToastMessageCategory}
	ErrInvalidGuardianMobile                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GUARDIAN-MOBILE", Message: "Invalid Guardian Mobile Number"}
	ErrMissingNomineeConsent1                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-CONSENT1", Message: "CONSENT1 data missing", Category: ToastMessageCategory}
	ErrInvalidGuardianEmail                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-GUARDIAN-Email", Message: "Invalid guardian's email", Category: ToastMessageCategory}
	ErrMissingGuardianMobile                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-GUARDIAN-MOBILE-NUMBER", Message: "Missing guardian's mobile number"}
	ErrSameNomineeAndAccountHolderEmail                  = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-AND-ACCOUNT-HOLDER-EMAIL", Message: "Applicant and Nominee should be different persons with different Email", Category: ToastMessageCategory}
	ErrSameGuardianAndAccountHolderEmail                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-GUARDIAN-AND-ACCOUNT-HOLDER-EMAIL", Message: "Applicant and Guardian should be different persons with different Email", Category: ToastMessageCategory}
	ErrSameNomineeAndAccountHolderMobile                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-NOMINEE-AND-ACCOUNT-HOLDER-MOBILE-NUMBER", Message: "Applicant and Nominee should be different persons with different mobile number", Category: ToastMessageCategory}
	ErrSameGuardianAndAccountHolderMobile                = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SAME-GUARDIAN-AND-ACCOUNT-HOLDER-MOBILE-NUMBER", Message: "Applicant and Guardian should be different persons with different mobile number", Category: ToastMessageCategory}
	ErrDuplicateNomineeEmail                             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-NOMINEE-EMAIL", Message: "This email is already used for another nominee. Please use a different number to add multiple nominees to your Demat account.", Category: ToastMessageCategory}
	ErrDuplicateNomineeMobileNumber                      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUPLICATE-NOMINEE-MOBILE", Message: "This mobile number is already used for another nominee. Please use a different number to add multiple nominees to your Demat account.", Category: ToastMessageCategory}
	ErrMissingEncashValueForOperator                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-NOMINEE-ACCOUNT-OPERATOR-ENCASH", Message: "NominatedAccountOperatorName encash details missing", Category: ToastMessageCategory}
	ErrInvalidEncashValueForOperator                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-ACCOUNT-OPERATOR-ENCASH", Message: "Both encash in percentage and rupee at same time not allowed", Category: ToastMessageCategory}
	ErrInvalidNomineeConsent1                            = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-CONSENT1", Message: "Invalid CONSENT1", Category: ToastMessageCategory}
	ErrInvalidNominatedAccountOperatorName               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-ACCOUNT-OPERATOR-NAME", Message: "NominatedAccountOperatorName does not match any of the nominee names", Category: ToastMessageCategory}
	ErrInValidNominatedAccountOperatorEncashInPercentage = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-ACCOUNT-OPERATOR-ENCASH-IN-PERCENTAGE", Message: "Encash in percentage must be greater than 0 and less than equal to 100", Category: ToastMessageCategory}
	ErrUnexpectedEncashValueWithoutOperator              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-ACCOUNT-OPERATOR-ENCASH-WITHOUT-NOMINEE", Message: "Encash not allowed with nominee selection", Category: ToastMessageCategory}
	ErrInValidNominatedAccountOperatorEncashInRupee      = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-NOMINEE-ACCOUNT-OPERATOR-ENCASH-IN-Rupee", Message: "Encash in rupee must be greater than 0", Category: ToastMessageCategory}
	ErrInvalidAddressLine3                               = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-INVALID-ADDRESS-LINE-3", Message: "Address line 3is invalid", Category: ToastMessageCategory}
	ErrEModNotAllowedForFNUser                           = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-EMOD-NOT-ALLOWED-FN-USER", Message: "Online profile modification is not allowed for foreign national account. Please contact support.", Category: ToastMessageCategory}
)

// 5xx.
var (
	ErrDatabase                          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DB", Message: "error with database"}
	ErrGettingRoleAccess                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-ROLE-ACCESS", Message: "error getting role access"}
	ErrCheckingAgentPermission           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CHECKING-AGENT-PERMISSION", Message: "error checking admin permission"}
	ErrActiveSTPRejectionsNotFound       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ACTIVE-STP-REJECTIONS-NOT-FOUND", Message: "Active STP Rejections not found"}
	ErrKYCDataNotFound                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-KYC-DATA-NOT-FOUND", Message: "KYC Lead not found"}
	ErrMappingFailed                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-MAPPING-FAILED"}
	ErrJourneyNotFound                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-JOURNEY-NOT-FOUND", Message: "journey not found"}
	ErrPanNotFound                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PAN-NOT-FOUND", Message: "pan not found"}
	ErrPoaNotFound                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-POA-NOT-FOUND", Message: "poa not found"}
	ErrOpeningFile                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-OPENING-FILE", Message: "Error opening file"}
	ErrReadingFile                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-READING-FILE", Message: "Error reading file"}
	ErrUploadingFile                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPLOADING-FILE", Message: "Error uploading file to S3"}
	ErrHyperVergeFaceMatch               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-HYPER-VERGE-FACE-MATCH", Message: "Error performing hyper verge face match"}
	ErrGettingFileBytes                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-READING-FILE", Reason: "Error getting file bytes"}
	ErrHyperVergeOCR                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-HYPER-VERGE-OCR", Category: ToastMessageCategory}
	ErrAINXTOCR                          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-AI-NXT-OCR", Category: ToastMessageCategory}
	ErrGettingImageBytes                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-READING-IMAGE", Reason: "Error getting image bytes"}
	ErrGettingSignedURL                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-SIGNED-URL", Reason: "Error getting signed URL"}
	ErrAWSRekognitionFaceMatch           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-AWS-FACE-MATCH", Message: "Error returned from AWS face match"}
	ErrAWSRekognitionEmpty               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-AWS-REKOGNITION-EMPTY", Message: "AWS rekognition response is empty"}
	ErrHyperVergeLogin                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-HYPER-VERGE-LOGIN", Message: "Error getting token from hyperVerge"}
	ErrClcmPanFetch                      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-PAN-FETCH", Message: "Error getting pan details from clcm"}
	ErrInvalidSignature                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-SIGNATURE", Message: "Error Signature is not Valid"}
	ErrSignatureInvalid                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SIGNATURE-INVALID", Message: "Invalid signature, Please try again", Category: ToastMessageCategory}
	ErrHypervergeSignature               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-HYPERVERGE-SIGNATURE", Message: "Error validating hyperverge signature"}
	ErrCheckingSEBIBanned                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CHECKING-IF-SEBI-BANNED", Message: "Error checking if pan is SEBI banned"}
	ErrFormingNSDLPayLoad                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FORMING-NSDL-PAYLOAD", Message: "Error creating NSDL payload"}
	ErrNSDLPanValidation                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NSDL-PAN-VALIDATION", Message: "Error validating pan from NSDL"}
	ErrProfileServiceToken               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PROFILE-SERVICE-TOKEN", Message: "Error creating token to get profile"}
	ErrGettingProfile                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-PROFILE", Message: "Error getting profiles"}
	ErrGettingPartnerProfile             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-PARTNER-PROFILE", Message: "Error getting partner profiles"}
	ErrNoObjectsProvided                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NO-OBJECTS-PROVIDED", Message: "No objects provided to round robin"}
	ErrInsertingPANData                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-PAN-DATA", Message: "Error inserting/updating pan data"}
	ErrUpdatingDeliveryBrokConsent       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATE-DELIVERY-BROKERAGE-CONSENT-DATA", Message: "Error updating delivery brokerage consent data"}
	ErrInsertingDeliveryBrokConsent      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERT-DELIVERY-BROKERAGE-CONSENT-DATA", Message: "Error inserting delivery brokerage consent data"}
	ErrGettingDeliveryBrokConsent        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-DELIVERY-BROKERAGE-CONSENT-DATA", Message: "Error getting delivery brokerage consent data"}
	ErrFetchingPANData                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-PAN-DATA", Message: "Error fetching pan data"}
	ErrInsertingLeadData                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERTING-LEAD-DATA", Message: "Error inserting lead data"}
	ErrFetchingApplicationData           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-APPLICATION-DATA", Message: "Error fetching application data"}
	ErrInsertingSelfieData               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERTING-SELFIE-DATA", Message: "Error inserting/updating selfie data"}
	ErrFetchingSelfieData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-SELFIE-DATA", Message: "Error fetching selfie data"}
	ErrAESEncryption                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-AES-ENCRYPTION", Message: "Error encrypting using AES"}
	ErrInitiateDigioRequest              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INITIATE-DIGIO", Message: "Error sending initiate digio request"}
	ErrInitiateDigilockerRequest         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INITIATE-DIGILOCKER", Message: "Error sending initiate digilocker request"}
	ErrAESDecryption                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-AES-DECRYPTION", Message: "Error decrypting using AES"}
	ErrGetDigioKYCStatus                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-DIGIO-KYC-STATUS", Message: "Error getting digio kyc status"}
	ErrGetMediaFromDigio                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-MEDIA-FROM-DIGIO", Message: "Error getting media from digio"}
	ErrEmptyDigioAction                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-EMPTY-DIGIO-ACTION"}
	ErrSEBICheck                         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SEBI-CHECK"}
	ErrNSDLCheck                         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NSDL-CHECK"}
	ErrPersistingAddressData             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-ADDRESS-DATA", Message: "Error inserting/updating address data"}
	ErrPersistingPOAData                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-POA-DATA", Message: "Error inserting/updating poa data"}
	ErrPersistingSEBIBannedPANData       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-SEBI-BANNED-PAN-DATA", Message: "Error inserting/updating sebi banned data"}
	ErrPersistingViewPartnerData         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-VIEW-PARTNER-AUDIT", Message: "Error inserting/updating view partner audit data"}
	ErrStartProcessInstance              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-START-PROCESS", Message: "Error start process instance failed"}
	ErrGetProcessInstance                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-PROCESS-INSTANCE", Message: "Error get process instance"}
	ErrDeleteProcessInstance             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DELETE-PROCESS-INSTANCE", Message: "Error delete process instance"}
	ErrLabelRekognition                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-LABEL-REKOGNITION", Message: "Invalid Signature. Please try again.", Category: ToastMessageCategory}
	ErrUpdatingKycJourney                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATE-KYCJOURNEY", Message: "Error updating kyc journey"}
	ErrIneligibleForCorrectionJourney    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INELIGIBLE-CORRECTION", Message: "Error ineligible for correction journey"}
	ErrClientCodeNotFoundInKYC           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLIENT-CODE-NOT-FOUND", Message: "Client code not found in kyc"}
	ErrFetchingDDPIStatus                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-DDPI-STATUS", Message: "Unable to fetch DDPI status from profile"}
	ErrSetuInitiation                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-INITIATION"}
	ErrSetuRedirection                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-REDIRECTION"}
	ErrPersistingDigioData               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DIGIO-DATA", Message: "Error inserting/updating digio data"}
	ErrPersistingDigilockerData          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DIGILOCKER-DATA", Message: "Error inserting/updating digilocker data"}
	ErrCreatingESignPDF                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CREATING-ESIGN-PDF"}
	ErrOnekycSync                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ONEKYC-SYNC"}
	ErrInEligibleForESign                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INELIGIBLE-FOR-ESIGN", Category: ToastMessageCategory}
	ErrGeneratingRandomNumber            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-RANDOM_NUMBER", Message: "GeneratingRandomNumberFailed"}
	ErrSendingSMS                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SMS-FAILURE", Message: "Sending SMS Failed"}
	ErrValidatingSendSMSMessageV2Request = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UNABLE-TO-CREATE-SMSREQUEST", Message: "invalid send smsv2 request"}
	ErrClcmBankFetch                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-BANK-FETCH", Message: "Error getting bank details from clcm"}
	ErrKYC1Token                         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-KYC1-TOKEN", Message: "Error creating token to perform imps"}
	ErrGettingIMPSInfo                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-IMPS-INFO", Message: "Failed verifying bank information"}
	ErrOpenSearch                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-OPEN-SEARCH", Message: "Error performing open search"}
	ErrOCRBankCheque                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-OCR-BANk-CHEQUE", Message: "Error performing ocr on bank cheque"}
	ErrSendingNotification               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SENDING-NOTIFICATION"}
	ErrFetchingPartnerDetails            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-PARTNER-DETAILS", Message: "Error fetching partner details from nxt"}
	ErrFetchingHolderDetails             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-HOLDER-DETAILS", Message: "Error getting holder details"}
	ErrPersistingDdpiData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DDPI-DATA", Message: "Error persisting ddpi data"}
	ErrInsertingDdpiData                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERTING-DDPI-DATA", Message: "Error inserting ddpi data"}
	ErrCheckingDormantStatus             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CHECKING-DORMANT-STATUS", Message: "Error checking dormant status"}
	ErrOnboardingEsignReset              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ONBOARDING-ESIGN-RESET", Message: "We are currently facing issues. Please try after some time."}
	ErrHoldingDetailsFetch               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-HOLDING-DETAILS-FETCH", Message: "Error getting holding details"}
	ErrClcmSegmentsFetch                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-SEGMENTS-FETCH", Message: "Error getting segment details from clcm"}
	ErrEmptyClcmSegmentsFetch            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-EMPTY-CLCM-SEGMENTS-FETCH", Message: "Empty segment details from clcm"}
	ErrParsingTIME                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PARSING-TIME", Message: "Error parsing time"}
	ErrSegmentsNotFound                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SEGMENTS-NOT-FOUND", Message: "segment preference not found"}
	ErrSendingEMail                      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SENDING-EMAIL", Message: "Error sending mail, please try again", Category: ToastMessageCategory}
	ErrInsertingEmailGenericData         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERTING-EMAIL-GENERIC-DATA", Message: "Error inserting email data"}
	ErrPersistingNomineeData             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NOMINEE-DATA", Message: "Error inserting/updating nominee data"}
	ErrPersistingNomineeMeta             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NOMINEE-META", Message: "Error inserting/updating nominee meta data"}
	ErrPersistingNomineeDetails          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NOMINEE-DETAILS", Message: "Error inserting/updating nominee details"}
	ErrFetchingSelfieIDPathData          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-SELFIE-ID-PATH-DATA", Message: "Error fetching selfie ID Path data"}
	ErrPerformingNameMatch               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERFORMING-NAME-MATCH", Message: "Error performing name match"}
	ErrClcmClientDetailsFetch            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-CLIENT-DETAILS-FETCH", Message: "Error getting client details from clcm"}
	ErrClcmClientInfoFetch               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-CLIENT-INFO-FETCH", Message: "Error getting client info"}
	ErrClcmBasicDetailsFetch             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-BASIC-DETAILS-FETCH", Message: "Error getting client basic details"}
	ErrClcmGetSebiBannedDetailsFetch     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-SEBI-BANNED-DETAILS-FETCH", Message: "Error getting sebi banned details"}
	ErrClcmGetClientProducts             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-GET-CLIENT-PRODUCTS", Message: "Error getting client products from clcm"}
	ErrReadingBankCheque                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-READING-BANk-CHEQUE", Message: "Unable to read bank cheque. Please provide a clear cheque", Category: ToastMessageCategory}
	ErrPersistingBankData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-BANK-DATA", Message: "Error inserting/updating bank data"}
	ErrDecryptingFile                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DECRYPTING-FILE", Message: "Corrupted file uploaded. Try uploading another one.", Category: ToastMessageCategory}
	ErrParsingTemplate                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PARSING-TEMPLATE", Message: "unable to parse template"}
	ErrExecutingTemplate                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-EXECUTING-TEMPLATE", Message: "unable to execute template"}
	ErrGeneratingPDF                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GENERATING-PDF", Message: "pdf generation failed"}
	ErrPersistingSegmentData             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-SEGMENT-DATA", Message: "Error persisting segment data"}
	ErrPersistingAccountFreezeData       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-ACCOUNT-FREEZE-DATA", Message: "Error persisting a/c freeze data"}
	ErrGettingCamsBankList               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-CAMS-BANK-LIST", Message: "unable to get available banks for cams"}
	ErrCamsAuthentication                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CAMS-AUTH", Message: "error in cams authentication request"}
	ErrCamsCreateConsentRequest          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CAMS-CREATE-CONSENT-REQUEST", Message: "Error in cams create consent external request"}
	ErrGettingBankFromIFSC               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-BANK-FROM-IFSC", Message: "Error in getting bank details from ifsc"}
	ErrInvalidRedirectionForCams         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-REDIRECTION-FOR-CAMS", Message: "Invalid redirection url for cams"}
	ErrPersistingCamsData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-CAMS-DATA", Message: "Error inserting/updating cams data"}
	ErrEmptyPDFFile                      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-EMPTY-PDF-FILE", Message: "Empty PDF File"}
	ErrCheckingNetBankingSupport         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CHECKING-NB-SUPPORT", Message: "Error checking net banking support"}
	ErrPersistingEmailData               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-EMAIL-DATA", Message: "Error inserting/updating email data"}
	ErrPersistingPanData                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-PAN-DATA", Message: "Error inserting/updating pan data"}
	ErrVerifyingGoogleToken              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-VERIFYING-GOOGLE-TOKEN", Message: "Error verifying google token"}
	ErrMissingIntent                     = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-MISSING-INTENT", Message: "Intent is missing"}

	ErrUpdatingAccountClosureDetails     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATING-ACCOUNT-CLOSURE-DETAILS", Message: "Couldn't update your details. Please retry later", Category: ToastMessageCategory}
	ErrCancellingAccountClosureRequest   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CANCELLING-ACCOUNT-CLOSURE-REQUEST", Message: "Couldn't cancel your request. Please retry later", Category: ToastMessageCategory}
	ErrFetchingAccountClosureWorkflows   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-ACCOUNT-CLOSURE-WORKFLOWS", Message: "Error fetching account closure workflows"}
	ErrFetchingSuperPortfolio            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-SUPERPORTFOLIO", Message: "Error fetching super portfolio"}
	ErrFetchingAccountHoldingBreakup     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-ACCOUNT-HOLDING-BREAKUP", Message: "Error fetching account holding breakup"}
	ErrStartingAccountClosure            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-STARTING-ACCOUNT-CLOSURE", Message: "Error starting a/c closure"}
	ErrUpdatingIntentForAccountClosure   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATING-CLOSURE-AND-INTENT-ACCOUNT-CLOSURE", Message: "Error updating intent and reason a/c closure"}
	ErrGettingRMSLimit                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-RMS-LIMIT", Message: "Error getting RMS Limit response"}
	ErrGettingIPOOrders                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-IPO-ORDERS", Message: "Error getting IPO orders response"}
	ErrGettingMFOrders                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-MF-ORDERS", Message: "Error getting MF orders response"}
	ErrGettingMFSIPs                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-MF-SIPS", Message: "Error getting MF SIPs response"}
	ErrGettingMFSWPs                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-MF-SWPS", Message: "Error getting MF SWPs response"}
	ErrGettingOrderbook                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-ORDERBOOK", Message: "Error getting Orderbook response"}
	ErrGettingPositions                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-POSITIONS", Message: "Error getting Positions response"}
	ErrPersistingClientDetailsData       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-CLIENT-DETAILS-DATA", Message: "Error inserting/updating client details data"}
	ErrPersistingIncomeData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-INCOME-DATA", Message: "Error inserting/updating income data"}
	ErrPersistingOccupationData          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-OCCUPATION-DATA", Message: "Error inserting/updating occupation data"}
	ErrPersistingPersonalData            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-PERSONAL-DATA", Message: "Error inserting/updating personal data"}
	ErrCreatingRPDDeepLink               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CREATING-DEEP-LINK", Message: "Error from create deep link API"}
	ErrGettingRPDStatus                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-RPD-STATUS", Message: "Error from rpd get status API"}
	ErrGettingTxnStatus                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-TXN-STATUS", Message: "Error from transaction get status API"}
	ErrInitiatingPayout                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INITIATING-PAYOUT", Message: "Error from Initiate Payout API", Category: ToastMessageCategory}
	ErrInitiatingUpiPayment              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INITIATING-UPI-PAYMENT", Message: "Error while initiating upi payment", Category: ToastMessageCategory}
	ErrInitiatingNetBankingPayment       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INITIATING-NETBANKING-PAYMENT", Message: "Error while initiating net banking payment", Category: ToastMessageCategory}
	ErrPersistingRPDData                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-RPD-DATA", Message: "Error inserting/updating rpd data"}
	ErrInValidEmailToken                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-TOKEN", Message: "Unable to read email from token"}
	ErrGettingReferralDetails            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-REFERRAL-DETAILS", Message: "Error getting referral details"}
	ErrSearchingLSQLead                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SEARCHING-LSQ-LEAD", Message: "Error searching for lsq lead"}
	ErrDeletingLSQLead                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DELETING-LSQ-LEAD", Message: "Error deleting lsq lead"}
	ErrClcmUpsertSettlementFrequency     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-UPSERT-SETTLEMENT-FREQUENCY", Message: "Error processing your request, please try again", Category: ToastMessageCategory}
	ErrFetchingSettlementFreqFromCLCM    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-SETTLEMENT-FREQUENCY-FROM-CLCM", Message: "Error processing your request, please try again", Category: ToastMessageCategory}
	ErrPersistingSettlementFrequencyData = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-SETTLEMENT-FREQUENCY-DATA", Message: "Error processing your request, please try again", Category: ToastMessageCategory}
	ErrActiveDIYReasonsNotFound          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ACTIVE-DIY-REASONS-NOT-FOUND", Message: "Active DIY Reasons not found"}
	ErrInsertingDIYFlowData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DIY-FLOW-DATA", Message: "Error inserting/updating diy flow data"}
	ErrUpdatingDIYFlows                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATE-DIY-FLOWS", Message: "Error updating diy flows"}
	ErrCapturingLead                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CAPTURING-LEAD", Message: "Error capturing lead"}
	ErrGettingGeolocationDetails         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-GEOLOCATION-DETAILS", Message: "Error getting geolocation details"}
	ErrGettingCVLKRAPassword             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-CVLKRA-PASSWORD", Message: "Error getting cvl kra password"}
	ErrGettingCVLKRAPanStatus            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-CVLKRA-PAN-STATUS", Message: "Error getting cvl kra pan status"}
	ErrGettingCVLKRAPanDetails           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GET-CVLKRA-PAN-DETAILS", Message: "Error getting cvl kra pan details"}
	ErrUpdatingAccountClosureNudge       = errors.Error{StatusCode: http.StatusInternalServerError,
		Code: "ERR-UPDATING-ACCOUNT-CLOSURE-NUDGE", Message: "Error processing your request, please try again", Category: ToastMessageCategory}
	ErrSendingWhatsAppCommunication             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SENDING-WHATSAPP-COMMUNICATION", Message: "Error sending info-bip whatsapp communication"}
	ErrInsertingEmodCorrectionSignatureData     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INSERTING-EMOD_CORR_SIG-DATA", Message: "Error inserting/updating data"}
	ErrUploadingDocumentToSetu                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-DOCUMENT-UPLOAD", Message: "Error uploading document to setu"}
	ErrSetuSignatureInitiate                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-SIGNATURE-INITIATE", Message: "Error initiating setu signature request"}
	ErrSetuEmptyRedirectResponse                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-EMPTY-REDIRECT-RESPONSE", Message: "Signer data or signer url not available in setu response"}
	ErrSetuSignatureStatus                      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-SETU-SIGNATURE-STATUS", Message: "Error getting setu signature status"}
	ErrDownloadingSignedDocumentSetu            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DOWNLOADING-SIGNED-DOCUMENT-SETU", Message: "Error downloading signed document from setu"}
	ErrGettingGeolocation                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-GEOLOCATION-EXT", Message: "Error getting geolocation"}
	ErrInsertingEsignData                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-ESIGN-DATA", Message: "Error inserting/updating esign data"}
	ErrEsignDocumentTypeMapping                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-MISSING-TYPE-MAPPING", Message: "esign document type mapping not found"}
	ErrClientDetailsMapping                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-MISSING-CLIENT-DETAILS-CLCM", Message: "Client details are missing from clcm"}
	ErrInvalidCLCMDOB                           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-CLCM-DOB", Message: "Invalid clcm dob"}
	ErrGeneratingJWTToken                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GENERATING-JWT-TOKEN", Message: "Error generating JWT token"}
	ErrNoUserFound                              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NO-USER-FOUND", Message: "No user found"}
	ErrClcmMetadataFetch                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-CLIENT-METADATA-FETCH", Message: "Error getting client metadata"}
	ErrFetchingActiveStockSIP                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-ACTIVE-STOCK-SIP", Message: "Error fetching Active Stock SIPs"}
	ErrFetchingBrokerageDetailsFromRTB          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-BROKERAGE-DETAILS-FROM-RTB", Message: "Error fetching brokerage details from rtb"}
	ErrFetchingPOAData                          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-POA-DATA", Message: "Error fetching poa data"}
	ErrGettingGeoCoding                         = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-GEOCODING", Message: "Error getting geocoding"}
	ErrPersistingSelfieData                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-SELFIE-DATA", Message: "Error inserting/updating selfie data"}
	ErrPersistingGeoLocationData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-GEOLOCATION-DATA", Message: "Error inserting/updating geolocation data"}
	ErrPersistingLeadAttributesData             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-LEAD-ATTRIBUTES-DATA", Message: "Error inserting/updating lead attributes data"}
	ErrFetchingLeadAttributesData               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-LEAD-ATTRIBUTES--DATA", Message: "Error fetching lead attributes data"}
	ErrGettingProcessInstanceVariables          = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-PROCESS-INSTANCE-VARIABLES", Message: "Error getting process instance variables"}
	ErrParsingStructure                         = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PARSING-CML-STRUCTURE", Message: "Error while parsing cml report structure"}
	ErrStartEntryNotFound                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-START-ENTRY-NOT-FOUND", Message: "Error start entry not found"}
	ErrBlobNotFound                             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-BLOB-NOT-FOUND", Message: "Error blob not found"}
	ErrESignNotFound                            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ESIGN-NOT-FOUND", Message: "Error eSign not found", Category: ToastMessageCategory}
	ErrClcmPanUpsert                            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-PAN-UPSERT", Message: "Error while updating pan in clcm"}
	ErrHTMLParsing                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-PARSING-HTML-TEMPLATE", Message: "Error while parsing html template"}
	ErrGettingExperianAuthToken                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-EXPERIAN-AUTH-TOKEN", Message: "Error while getting token from experian"}
	ErrGettingExperianPanMatchData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-GETTING-EXPERIAN-PAN-MATCH-DATA", Message: "Error while getting pan match data from experian"}
	ErrUpdatingCamundaVariables                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATING-CAMUNDA-VARIABLES", Message: "Error updating camunda variables"}
	ErrMultipleWorkflowInstances                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-MULTIPLE-WORKFLOW-INSTANCES", Message: "Multiple workflow instances found"}
	ErrUpdatingJourneyTypeData                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-UPDATING-START-DATA", Message: "Error updating start data"}
	ErrPOANotFound                              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-POA-NOT-FOUND", Message: "poa not found"}
	ErrPersistingDaeAsssitanceData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DAE-ASSISTANCE-DATA", Message: "Error inserting/updating dae assistance data"}
	ErrFetchingAadhaarFromDigio                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FETCHING-AADHAAR-FROM-DIGIO", Message: "could not get aadhaar from digio"}
	ErrClcmKycNomineeData                       = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-NOMINEE-DATA", Message: "Error while fetchNomineeData pan in clcm"}
	ErrClcmClientDetailsNotExist                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-CLIENT-DETAILS-NOT-FOUND", Message: "Error client details not found in clcm"}
	ErrClcmBankDetailsNotFound                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-BANK-DETAILS-NOT-FOUND", Message: "Error bank details not found in clcm"}
	ErrNRIAddressTypeValidation                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NRI-ADDRESS-TYPE", Message: "Error address type need to be differnt", Category: ToastMessageCategory}
	ErrPersistingNRIBankData                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NRI-BANK-DATA", Message: "Error inserting/updating NRI bank data"}
	ErrPersistingNRIDepositoryData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NRI-DEPOSITORY-DATA", Message: "Error inserting/updating NRI depository data"}
	ErrPersistingDocumentData                   = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-DOCUMEMT-DATA", Message: "Error inserting/updating document data"}
	ErrPersistingNRIDocumentData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NRI-DOCUMEMT-DATA", Message: "Error inserting/updating nri document data"}
	ErrValidatePanWithNSDL                      = errors.Error{StatusCode: http.StatusInternalServerError, Code: "500-98", Reason: "ValidatePanWithNSDLFailed", Category: ExternalCategory, SubCategory: NSDL}
	ErrActivationTimeStampNotFound              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ACTIVATION-TIMESTAMP-NOT-FOUND", Message: "no valid activation ts found"}
	ErrFacingIssues                             = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-FACING-ISSUES", Message: "We are experiencing issues. Please contact customer care"}
	ErrDeletingEsignData                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DELETING_ESIGN_DATA", Message: "Error deleting esign data"}
	ErrNSDLPanNameValidation                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NSDL-PAN-NAME-VALIDATION", Message: "Error validating PAN name from NSDL", Category: ToastMessageCategory}
	ErrNSDLPanDOBValidation                     = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-NSDL-PAN-DOB-VALIDATION", Message: "Error validating PAN DOB from NSDL", Category: ToastMessageCategory}
	ErrClcmGetProfileDetails                    = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLCM-GET-PROFILE-DETAILS", Message: "Error getting profile details from CLCM"}
	ErrPIIKeyNotFound                           = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PII-KEY-NOT-FOUND", Message: "pii key not found"}
	ErrInValidAES256GCMKeySize                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-AES-256-GCM-KEY-SIZE", Message: "key size is in valid"}
	ErrInValidAES256GCMCipherText               = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-AES-256-GCM-CIPHER-TEXT", Message: "invalid AES 256 GCM cipher text"}
	ErrInValidSecret                            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-SECRET", Message: "invalid Secret"}
	ErrEmptyMetadata                            = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-INVALID-METADATA", Message: "empty metadata received from digilocker service"}
	ErrClientCodeNotFoundInCLCM                 = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-CLIENT-CODE-NOT-FOUND--IN-CLCM", Message: "Client code not found in clcm", Category: ToastMessageCategory}
	ErrPersistingAgentAccessData                = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-AGENT-ACCESS-DATA", Message: "Error inserting/updating admin access data"}
	ErrAllowingPIIAccess                        = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-ALLOWING-PII-ACCESS", Message: "Error allowing pii access"}
	ErrInsertingNseDeclarationData              = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-PERSISTING-NSE-DECLARATION-DATA", Message: "Error inserting/updating Nse declaration data"}
	ErrKycDetailsEntryNotFound                  = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-KYC-DETAILS-ENTRY-NOT-FOUND", Message: "Error KYC detail entries not found"}
	ErrNilResponse                              = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-NIL-RESPONSE", Message: "error nil response"}
	ErrDuplicationPaymentEntryFound             = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-DUP-PAYMENT", Message: "Payment already pending for the request", Category: ToastMessageCategory}
	ErrSuccessPaymentEntryFound                 = errors.Error{StatusCode: http.StatusBadRequest, Code: "ERR-SUCCESS-PAYMENT", Message: "Payment already successful", Category: ToastMessageCategory}
	ErrDownloadingSignedDocumentHypervergeESign = errors.Error{StatusCode: http.StatusInternalServerError, Code: "ERR-DOWNLOADING-SIGNED-DOCUMENT-HYPERVERGE-ESIGN", Message: "Error downloading signed document from Hyperverge eSign"}
)
