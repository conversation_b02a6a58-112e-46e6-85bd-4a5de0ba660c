package constants

// api status.
const (
	SuccessStatus = "success"
	ErrorStatus   = "error"
	FailureStatus = "failure"
	PendingStatus = "pending"
)

// api Param keys.
const (
	SelfieFormKey                 = "selfie"
	SelfieLivelinessSourceFormKey = "liveliness_source"
	SelfieLivelinessScoreFormKey  = "liveliness_score"
	BankChequeFormKey             = "bankCheque"
	BankDataFormKey               = "data"
	ActionFormKey                 = "action"
	ClientCode                    = "clientCode"
)

// workflow constants.
const (
	WorkflowVariableStringType               = "string"
	WorkflowVariableBooleanType              = "boolean"
	AppNumberWorkflowKey                     = "appNumber"
	MobileWorkflowKey                        = "mobile"
	IntentWorkflowKey                        = "intent"
	ClientCodeWorkflowKey                    = "clientCode"
	CorrelationIDWorkflowKey                 = "correlationId"
	SourceWorkflowKey                        = "source"
	IPAddressWorkflowKey                     = "ipAddress"
	IsFnoProcessVariableKey                  = "isFno"
	AppVersionWorkflowKey                    = "appVersion"
	PlatformWorkflowKey                      = "platform"
	IsSelfieFlowWorklflowKey                 = "isSelfieFlow"
	ScrutinyStatusWorkflowKey                = "scrutinyStatus"
	JourneyTypeWorkflowKey                   = "journeyType"
	KycTypeWorkflowKey                       = "kycType"
	DeviceWorkflowKey                        = "device"
	DeviceOSWorkflowKey                      = "deviceOS"
	NameWorkflowKey                          = "name"
	DobWorkflowKey                           = "dob"
	PanWorkflowKey                           = "pan"
	IsNSDLSeededWorkflowKey                  = "isNSDLSeeded"
	CurrentSettlementFreqWorkflowKey         = "currentSettlementFreq"
	RequestedSettlementFreqWorkflowKey       = "requestedSettlementFreq"
	IsEmodApplicationTypePhysicalWorkflowKey = "isEmodApplicationTypePhysical"
	TransactionIDWorkflowKey                 = "transactionId"

	// nri constants.
	CheckMobileAgainstSBWorkflowKey          = "checkMobileAgainstSB"
	CheckEmailAgainstSBWorkflowKey           = "checkEmailAgainstSB"
	CheckIfMobileExistsInBOWorkflowKey       = "checkIfMobileExistsInBO"
	CheckIfEmailExistsInBOWorkflowKey        = "checkIfEmailExistsInBO"
	CheckIfPANExistsInBOWorkflowKey          = "checkIfPANExistsInBO"
	CheckIfSEBIBannedWorkflowKey             = "checkIfSEBIBanned"
	CheckIfValidateAadhaarWithKRAWorkflowKey = "checkIfValidateAadhaarWithKRA"
	BoPushEnabledWorkflowKey                 = "boPushEnabled"
	MfssPushEnabledWorkflowKey               = "mfssPushEnabled"
	AmxPushEnabledWorkflowKey                = "amxPushEnabled"
	ProfilePushEnabledWorkflowKey            = "profilePushEnabled"
	MFKycUCCPushEnabledWorkflowKey           = "mfKycUCCPushEnabled"
	IsScrutiny3AllowedWorkflowKey            = "isScrutiny3Allowed"
)

// workflow name.
const (
	ReKycFulfilmentWorkflow           = "kyc-emod-fulfilment-rekyc"
	TriggerDIYCommsWorkflow           = "trigger-diy-comms"
	OneKYCWorkflow                    = "onekyc-admin"
	KycFulfilmentWorkflow             = "kyc-fulfilment"
	EmodIncomeFulfilmentWorkflow      = "kyc-emod-fulfilment-income"
	AccountFreezeWorkflow             = "kyc-emod-fulfilment-account-freeze"
	ClientCommunicationWorkflow       = "client-communication"
	PhysicalMFFulfilmentWorkflow      = "kyc-phymf-fulfilment"
	WelcomeEmailResendWOrkflow        = "kyc-resend-welcome-mail"
	EmodNameDoBChangeWorkflow         = "emod-name-dob-change"
	EmodSettlementFrequencyWorkflow   = "kyc-emod-settlement-frequency"
	ReactivationFulfilmentWorkflow    = "kyc-emod-fulfilment-reactivation"
	DeactivationFulfilmentWorkflow    = "kyc-emod-fulfilment-deactivation"
	TRSPanAadhaarSeedingCheckWorkflow = "trs-pan-aadhaar-seeding-check"
	DdpiPaymentsWorkflow              = "kyc-payment-fulfilment"
)

// Setu Query Params.
const (
	SetuRequestDataQueryParamKey      = "requestData"
	SetuSuccessQueryParamKey          = "success"
	SetuESignRequestIDQueryParamKey   = "id"
	SetuSignerIdentifierQueryParamKey = "signerIdentifier"
	SetuErrorCodeQueryParamKey        = "errCode"
	SetuErrorMessageQueryParamKey     = "errorMessage"
	SetuSourceQueryParamKey           = "source"
	SetuEsignProviderQueryParamKey    = "esp"
)

// Hyperverge Query Params
const (
	HypervergeRequestDataQueryParamKey      = "requestData"
	HypervergeSuccessQueryParamKey          = "success"
	HypervergeESignRequestIDQueryParamKey   = "id"
	HypervergeSignerIdentifierQueryParamKey = "signerIdentifier"
	HypervergeErrorCodeQueryParamKey        = "errCode"
	HypervergeErrorMessageQueryParamKey     = "errorMessage"
	HypervergeSourceQueryParamKey           = "source"
	HypervergeEsignProviderQueryParamKey    = "esp"
)

// Digio query params.
const (
	DigioEncryptedRedirectDataStringQueryParamKey = "requestData"
	DigioSourceQueryParamKey                      = "source"
	DigioRedirectURLQueryParamKey                 = "redirect_url"
	DigioTokenIDQueryParamKey                     = "token_id"
	DigioDocIDQueryParamKey                       = "digio_doc_id"
	DigioMessageParamKey                          = "message"
	DigioStatusParamKey                           = "status"
	DigioRequestDataParamKey                      = "requestData"
	DigioIntentQueryParamKey                      = "intent"
)

// Digilocker query params.
const (
	CodeQueryParamKey   = "code"
	StatusQueryParamKey = "status"

	MobileQueryParamKey  = "mobile"
	AgentIDQueryParamKey = "agentId"
)

// Query Params.
const (
	SearchQuery          = "search"
	FromQuery            = "from"
	SizeQuery            = "size"
	BankQueryParam       = "bank"
	DefaultFromQuery     = 0
	DefaultSizeQuery     = 10
	PinCodeQueryParamKey = "pincode"
	BankTypeQueryParam   = "bankType"
)

// path params.
const (
	BankAccountNumber = "accountNumber"
)

const (
	KYCMobileOTPPrefix = "KYCMobileOTP"
)

const (
	Default = "default"
)

const (
	CLCMTimeFormat            = "2006-01-02T15:04:05Z"
	HoldingsPDFDateFormat     = "2006-01-02"
	ProfileDateFormat         = "02/01/2006 15:04:05"
	CLCMSeededTimeFormat      = "02/01/2006 15:04:05"
	NomineeDobDateFormat      = "********"
	NomineeResponseDateFormat = "02/01/2006"
)

const (
	DerivativeProofFormKey = "derivativeProof"
)

const (
	CMLProofFormKey = "cmlFile"
)

const (
	CamsIntentQueryParamsKey         = "intent"
	CamsWebhookPurposeConsentStatus  = "ConsentStatusNotification"
	CamsWebhookPurposeBankStatement  = "Push_Data"
	CamsWebhookConsentStatusApproved = "ACTIVE"
	CamsWebhookConsentStatusRejected = "REJECTED"
	CamsAppNumberQueryParamsKey      = "app_number"
	CamsSourceQueryParamsKey         = "source"
)

const (
	EmodStatusTrackQueryValue = "track"
)

const (
	EmodPersonalDetailsSource    = "spark-emod"
	EmodFulfilmentBackfillSource = "bo"
)

// referral fields.
const (
	SBTagReferralField         = "SbTag"
	BrokerageTypeReferralField = "btype"
	ReferralCodeField          = "referrer"
	DeeplinkValueField         = "deep_link_value"
	ReferralPromoCode          = "refferalPromoCode"
	PageURLField               = "page_url"
	UTMSource                  = "utm_source"
	UTMMedium                  = "utm_medium"
	UTMCampaign                = "utm_campaign"
	CityField                  = "city"
	Name                       = "name"
)

// start API error pages.
const (
	ThankYouPage                  = "onboarding-complete"
	InConveniencePage             = "inconvenience"
	ForbiddenPage                 = "forbidden"
	ErrorPage                     = "retry-later"
	ContinueInExistingMediumPage  = "continue-in-existing-medium"
	BlackListedReferralPage       = "referral-blacklisted"
	ModificationInProgressPage    = "modification-in-progress"
	RecentClosedAccountPage       = "recent-closed-account"
	ReferralBlockLeadPage         = "referral-block-lead"
	PhyMFOnlyJourneyChangedPage   = "phy-mf-only-journey-change"
	PhysicalMfToEquityLandingPage = "phy-mf-to-equity-landing"
	NRIApplicationInReviewPage    = "nri-application-in-review"
	NRIScrutiny1RejectedPage      = "nri-scrutiny1-reject"
	NRIESignDocumentUploadPage    = "nri-esign-document-upload"
	NRIDocumentUploadPage         = "nri-document-upload"
	NRIScrutiny2RejectedPage      = "nri-scrutiny2-reject"
	NRIProductExistPage           = "product-exist"
	NRIAwaitingCourierPage        = "nri-awaiting-courier"
	NRIAwaitingReCourierPage      = "nri-awaiting-re-courier"
	NRIThanksPage                 = "nri-thanks"
	RIToNRIConflictPage           = "ri-nri-conflict"
	NRIToRIConflict               = "nri-ri-conflict"
	NRIUnsupportedOnAdmin         = "nri-journey-unsupported-on-admin"
)

const (
	EmodSettlementFrequencyApprovalNotificationTemplate = "emod_settlement_frequency_approval"
	DataPlaceHolderFullNameKey                          = "full_name"
	DataPlaceHolderAppNumberKey                         = "app_number"
	DataPlaceHolderSettlementFrequencyKey               = "settlement_frequency"
	DataPlaceHolderSubmissionDateKey                    = "submission_date"
)

const (
	DIYSelfieNotificationTemplate     = "diy_selfie"
	DataPlaceHolderNameKey            = "client_name"
	DIYCorrectionNotificationTemplate = "diy_correction"
	DataPlaceHolderLinkKey            = "link"
)

const (
	PanAuthType = "pan"
	DOBAuthType = "dob"
)

const (
	IsChangedKey         = "isChanged"
	AadhaarFrontFormKey  = "aadhaarFront"
	AadhaarBackFormKey   = "aadhaarBack"
	PanFormKey           = "pan"
	DocumentFormKey      = "document"
	DocumentTypeQueryKey = "documentType"
	NriAttestedForm      = "attestedForm"
)

const (
	IndianTimeZone = "Asia/Calcutta"
)

// External query or use case names for data fetch.
const (
	UserProfilingRetention = "user-profiling-retention"
)

const (
	AzureBlobESignContainerName = "esigneqn"
	AzureBlobKycContainerName   = "kyc"
	AzureBlobMFKycContainerName = "mfkyc"
)

// status query params.
const (
	StatusIgnoreRejectionsQueryParamKey   = "ignoreRejections"
	StatusIgnoreOneKYCStatusQueryParamKey = "ignoreOneKYCStatus"
)

const (
	ForbiddenMessage = "This action is forbidden."
)

// pan get query params.
const (
	PanPrefetchedQueryParamKey = "prefetched"
)
const MfToEquityFulfilmentProcessDefinitionDefaultValue = "mf-to-eq-fulfilment"
const NRIFulfilmentProcessDefinition = "physical-fulfillment"

const (
	NRIBankTypeNRE       = "NRE"
	NRIBankTypeNRO       = "NRO"
	NRIBankTypeNRONonPIS = "NRE-NON-PIS"
)

const (
	DocumentBackFileNameSuffix = "_back"
)

const (
	DocumentErrMissingFilePasswordMessageKey   = "missingFilePassword"
	DocumentErrUploadingFileMessageKey         = "uploading"
	DocumentErrIncorrectFilePasswordMessageKey = "incorrectPassword"
)

// trade ready status query params.
const (
	IsDormantQueryParam           = "is_dormant"
	KRAActionEnabledQueryParam    = "kra_action_enabled"
	NotPermittedToTradeQueryParam = "not_permitted_trade"
	KraTRS02QueryParam            = "kra_trs_02"
)

const (
	DefaultProduct           = "ddpi"
	AppNumberParamKey        = "app_number"
	DefaultNbPaymentValidity = 10
)

const (
	CLCMActivateSegment = "active"
)
