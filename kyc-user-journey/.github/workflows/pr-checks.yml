---
name: PR Flow

on:
  pull_request:
    branches:
      - master
      - hotfix-*

jobs:
  cilint:
    name: Lint
    strategy:
      matrix:
        go-version: [1.21.x]
        platform: [ubuntu-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/setup-go@v4
        with:
          go-version: ${{ matrix.go-version }}
      - name: Check out code into the Go module directory
        uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.x
      - name: Install goimports
        run: go install golang.org/x/tools/cmd/goimports@latest
      - name: Install pre-commit
        run: pip install pre-commit
        env:
          PRE_COMMIT_HOME: ${{ github.workspace }}
      - name: Run pre-commit
        run: |
          pre-commit run --files $(curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" -X GET "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}/files" | jq -r '.[].filename' | tr '\n' ' ')
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          args: --timeout=10m --config=.golangci.yml
  test:
    name: Test
    strategy:
      matrix:
        go-version: [1.21.x]
        platform: [ubuntu-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Install Go
        uses: actions/setup-go@v3
        with:
          go-version: ${{ matrix.go-version }}
        id: go
      - name: Check out code into the Go module directory
        uses: actions/checkout@v3
      - name: Run tests
        run: go test -coverprofile Coverage-${{ matrix.go-version }}-${{ matrix.platform }}.txt -covermode atomic -json ./... > TestResults-${{ matrix.go-version
          }}-${{ matrix.platform }}.json
      - name: Upload Go test results
        uses: actions/upload-artifact@v4
        with:
          name: Go-results-${{ matrix.go-version }}
          path: TestResults-${{ matrix.go-version }}.json
      - name: Annotate tests
        if: always()
        uses: guyarb/golang-test-annotations@v0.6.0
        with:
          test-results: TestResults-${{ matrix.go-version }}-${{ matrix.platform }}.json
  pr-checks:
    needs: [cilint, test]
    name: PR Build
    runs-on: aws-ec2
    steps:
      - name: Clean the workspace
        uses: docker://alpine:3
        with:
          args: /bin/sh -c "rm -rf /github/workspace/.* || rm -rf /github/workspace/*"

      - name: Checkout
        uses: actions/checkout@v3.1.0
        with:
          fetch-depth: 0

      - name: Checkout
        uses: actions/checkout@v3.1.0
        with:
          repository: angel-one/build-ecs-action
          ref: 2.8.0
          token: ${{ secrets.SRE_GIT_READ_TOKEN }}
          path: ./.github/workflows/
          clean: 'false'

      - name: Copy custom action
        shell: bash
        run: |
          cp ./.github/workflows/pr-flow/action.yml ./.github/workflows/

      - name: Run Custom Build action
        uses: ./.github/workflows # Uses an action in the directory
        id: image
        with:
          MAJOR_VERSION: 0
          RUN_CODE_UNIT_TEST: 0 #Use flag 1 to enable and 0 to disable
          REGISTRY: ghcr.io
          # github.repository as <account>/<repo>
          IMAGE_NAME: ${{ github.event.repository.name }}
          ECR_REGISTRY: ************.dkr.ecr.ap-south-1.amazonaws.com
          # ECR_REPO_NAME: go-web-app #test ecr repo name
          ECR_REPO_NAME: ${{ github.event.repository.name }}
          NEXUS_REGISTRY: docker.prod.angelcloud.in
          # ECR_REPO_NAME: go-web-app #test ecr repo name
          NEXUS_REPO_NAME: ${{ github.event.repository.name }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          AWS_ACCESS_KEY_ID: ${{ secrets.SRE_ECR_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.SRE_ECR_AWS_SECRET_ACCESS_KEY }}
          SRE_NEXUS_USERNAME: ${{ secrets.SRE_NEXUS_USERNAME }}
          SRE_NEXUS_PASSWORD: ${{ secrets.SRE_NEXUS_PASSWORD }}
          RELEASE_TOKEN: ${{ secrets.SRE_GIT_READ_TOKEN }} #Personal access token to create releases

      - run: |
          echo "ECR Image URI for CD ${{ steps.image.outputs.ecr_uri }} "
          echo "Nexus Image URI for CD ${{ steps.image.outputs.nexus_uri }} "
        shell: bash

      - name: Update PR
        uses: actions/github-script@v5
        env:
          ASSETS: ${{ steps.image.outputs.ecr_uri }}
        with:
          github-token: ${{ secrets.SRE_GITHUB_BOT_TOKEN }}
          script: |
            const output = `## SNAPSHOT BUILD
            ECR URI : \`${process.env.ASSETS}\`
            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`*`;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: output
            })
      - uses: SonarSource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
