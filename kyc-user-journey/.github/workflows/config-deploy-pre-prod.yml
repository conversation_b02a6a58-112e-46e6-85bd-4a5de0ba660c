---
name: Pre-Prod Config Deploy

on:
  workflow_dispatch:
  push:
    branches: [master]
    paths:
      - resources/configs/pre-prod02/**.yml
      - resources/configs/pre-prod02/**.yaml

jobs:
  deploy:
    name: Deploy
    runs-on: aws-ec2
    steps:
      - name: Download artifact
        uses: robinraju/release-downloader@v1.3
        with:
          repository: angel-one/aws-config-deploy
          latest: true
          fileName: aws-config-deploy-linux-amd64
          out-file-path: deploy
          token: ${{ secrets.SRE_GITHUB_BOT_TOKEN }}
      - name: Give permissions to binary
        run: chmod +x ./deploy/aws-config-deploy-linux-amd64
      - name: Run the binary
        run: ./deploy/aws-config-deploy-linux-amd64
        env:
          NAME: kyc-user-journey
          APP_NAME: kyc-user-journey
          DELETE_CONFIG_IF_NOT_FOUND: false
          CONFIGS_DIR_PATH: resources/configs
          IGNORE_ENVS: qa,local,dev,prod
          BRANCH: master
          REGION: ap-south-1
          ACCESS_KEY_ID: ${{secrets.AWS_ACCESS_KEY_ID_APPCONFIGDEPLOY_HUB}}
          SECRET_KEY: ${{secrets.AWS_SECRET_ACCESS_KEY_APPCONFIGDEPLOY_HUB}}
          PASSWORD: ${{secrets.SRE_GITHUB_BOT_TOKEN}}
          ASSUMED_ROLE_ARN: arn:aws:iam::443370716759:role/AppconfigDeploySpokeRole
