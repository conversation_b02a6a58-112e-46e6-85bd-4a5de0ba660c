---
name: Prod Config Deploy

on:
  workflow_dispatch:
  push:
    branches: [master]
    paths:
      - resources/configs/prod/**.yml
      - resources/configs/prod/**.yaml

jobs:
  deploy:
    name: Deploy
    runs-on: aws-ec2
    steps:
      - name: Download artifact
        uses: robinraju/release-downloader@v1.3
        with:
          repository: angel-one/aws-config-deploy
          latest: true
          fileName: aws-config-deploy-linux-amd64
          out-file-path: deploy
          token: ${{ secrets.AWS_APP_CONFIG_REPO_READ_TOKEN }}
      - name: Give permissions to binary
        run: chmod +x ./deploy/aws-config-deploy-linux-amd64
      - name: Run the binary
        run: NAME=kyc-user-journey APP_NAME=kyc-user-journey DELETE_CONFIG_IF_NOT_FOUND=false CONFIGS_DIR_PATH=resources/configs IGNORE_ENVS=qa,local,dev,pre-prod02
          BRANCH=master REGION=ap-south-1 ACCESS_KEY_ID=${{secrets.AWS_APP_CONFIG_ACCESS_KEY_ID}} SECRET_KEY=${{secrets.AWS_APP_CONFIG_SECRET_KEY}} PASSWORD=${{secrets.AWS_APP_CONFIG_REPO_READ_TOKEN}}
          ./deploy/aws-config-deploy-linux-amd64
