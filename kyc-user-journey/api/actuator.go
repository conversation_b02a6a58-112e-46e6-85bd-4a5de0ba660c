package api

import (
	goActuator "github.com/angel-one/go-actuator"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/gin-gonic/gin"
)

var actuatorHandler = goActuator.GetActuatorHandler(&goActuator.Config{
	Env:  flags.Env(),
	Name: constants.ApplicationName,
	Port: flags.Port(),
})

// Actuator is used to handle the Actuator requests.
func Actuator(ctx *gin.Context) {
	actuatorHandler(ctx.Writer, ctx.Request)
}
