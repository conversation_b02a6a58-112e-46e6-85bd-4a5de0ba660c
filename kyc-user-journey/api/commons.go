package api

import (
	"encoding/json"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func (c *CommonAPILogic) GetHeaders(ctx *gin.Context) models.Headers {
	analyticsParamsString := strings.TrimSpace(ctx.GetHeader(constants.AnalyticsParamsHeader))
	analyticsParams := getAnalyticsParams(ctx, analyticsParamsString)
	return models.Headers{
		Source:     strings.TrimSpace(strings.ToLower(ctx.GetHeader(constants.SourceHeader))),
		AppVersion: strings.TrimSpace(ctx.GetHeader(constants.AppVersionHeader)),
		Platform:   strings.TrimSpace(strings.ToLower(ctx.GetHeader(constants.PlatformHeader))),

		Device:   strings.TrimSpace(ctx.GetHeader(constants.DeviceHeader)),
		DeviceID: strings.TrimSpace(ctx.GetHeader(constants.DeviceIDHeader)),
		DeviceOS: strings.TrimSpace(ctx.GetHeader(constants.DeviceOSHeader)),

		IPAddress:     ctx.ClientIP(),
		MACAddress:    strings.TrimSpace(ctx.GetHeader(constants.MACAddressHeader)),
		Latitude:      strings.TrimSpace(ctx.GetHeader(constants.LatitudeHeader)),
		Longitude:     strings.TrimSpace(ctx.GetHeader(constants.LongitudeHeader)),
		Authorization: getAuthorization(ctx),
		TradeToken:    strings.TrimSpace(ctx.GetHeader(constants.TradeTokenHeader)),
		UserAgent:     strings.TrimSpace(ctx.GetHeader(constants.UserAgent)),
		AgentID:       strings.TrimSpace(ctx.GetHeader(constants.UserIDHeader)),
		APIKey:        strings.TrimSpace(ctx.GetHeader(constants.APIKey)),
		AppsFlyerID:   strings.TrimSpace(ctx.GetHeader(constants.AppsFlyerIDHeader)),
		AppID:         strings.TrimSpace(ctx.GetHeader(constants.AppIDHeader)),
		AppInstanceID: strings.TrimSpace(ctx.GetHeader(constants.AppInstanceIDHeader)),
		GAID:          strings.TrimSpace(analyticsParams.GAID),
		IDFA:          strings.TrimSpace(analyticsParams.IDFA),
		IDFV:          strings.TrimSpace(analyticsParams.IDFV),
		RawURL:        strings.TrimSpace(ctx.GetHeader(constants.RawURLHeader)),

		JourneyType: strings.TrimSpace(ctx.GetHeader(constants.EModJourneyTypeHeader)),
	}
}

func getAnalyticsParams(ctx *gin.Context, analyticsParamsString string) modelsAPIv1.AnalyticsParams {
	var analyticsParams modelsAPIv1.AnalyticsParams
	if analyticsParamsString != constants.Empty {
		err := json.Unmarshal([]byte(analyticsParamsString), &analyticsParams)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error while json unmarshalling for analyticsParams")
		}
	}

	return analyticsParams
}

func getAuthorization(ctx *gin.Context) string {
	authHeader := strings.TrimSpace(ctx.GetHeader(constants.AuthorizationHeader))
	if authHeader != constants.Empty {
		return authHeader
	}
	return constants.Bearer + constants.Space + ctx.GetString(constants.AuthTokenContextKey)
}

func (c *CommonAPILogic) GetMeta(ctx *gin.Context) models.Meta {
	return models.Meta{
		Mobile:                ctx.GetString(constants.MobileContextKey),
		AppNumber:             ctx.GetString(constants.AppNumberContextKey),
		Intent:                ctx.GetString(constants.IntentContextKey),
		ClientCode:            ctx.GetString(constants.ClientCodeContextKey),
		CorrectionJourneyType: ctx.GetString(constants.CorrectionJourneyTypeContextKey),
		KycJourneyType:        ctx.GetString(constants.KycJourneyTypeContextKey),
		KycType:               ctx.GetString(constants.KycTypeContextKey),
		ClientAccountType:     ctx.GetString(constants.AccountTypeContextKey),
	}
}
