package startfulfilment

import (
	"context"

	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
)

type StartFulfilmentBusinessLogic interface {
	StartFulFilmentViaSQSUsingAppnumberList(ctx context.Context, request *modelsApiV1.StartFulfilmentRequest) (*modelsApiV1.StartFulfilmentResponse, error)
}

var startFulfilmentBusinessLogic StartFulfilmentBusinessLogic

func SetStartFulfilmentBusinessLogic(businessLogic StartFulfilmentBusinessLogic) {
	startFulfilmentBusinessLogic = businessLogic
}
