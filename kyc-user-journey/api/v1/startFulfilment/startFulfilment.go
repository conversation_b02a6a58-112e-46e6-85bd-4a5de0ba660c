package startfulfilment

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleStartFulfilmentRoute(ctx *gin.Context) {
	request := modelsV1.StartFulfilmentRequest{}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleStartFulfilmentRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleStartFulfilmentRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := startFulfilmentBusinessLogic.StartFulFilmentViaSQSUsingAppnumberList(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleStartFulfilmentRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
