package email

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingEmailOTPRoute godoc
// @Summary Email Route
// @Description API to post email in onboarding flow
// @ID HandleOnboardingEmailOTPRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.EmailAPIRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/email/otp [POST].
func HandleOnboardingEmailOTPRoute(ctx *gin.Context) {
	handleGenericEmailOTPRoute(ctx)
}

// HandleOnboardingEmailGoogleSignInRoute godoc
// @Summary Email token Route
// @Description API to post email in onboarding flow
// @ID HandleOnboardingEmailGoogleSignInRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.EmailGoogleSignInAPIRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/email/google-sign-in [POST].
func HandleOnboardingEmailGoogleSignInRoute(ctx *gin.Context) {
	handleGenericEmailGoogleSignInRoute(ctx)
}

func handleGenericEmailGoogleSignInRoute(ctx *gin.Context) {
	request := modelsV1.EmailGoogleSignInAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating email google sign in request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := emailBusinessLogic.EmailGoogleSignIn(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing email google sign in request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleGenericEmailOTPRoute(ctx *gin.Context) {
	request := modelsV1.EmailAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating email otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := emailBusinessLogic.EmailOTP(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing email otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
