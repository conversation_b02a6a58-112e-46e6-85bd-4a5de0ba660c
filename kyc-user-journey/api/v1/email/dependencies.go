package email

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmailBusinessLogic interface {
	EmailOTP(ctx context.Context, request *modelsV1.EmailAPIRequest) (*modelsV1.EmailAPIResponse, error)
	EmailGoogleSignIn(ctx context.Context, request *modelsV1.EmailGoogleSignInAPIRequest) (*modelsV1.EmailGoogleSignInAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emailBusinessLogic EmailBusinessLogic

func SetEmailBusinessLogic(apiLogic CommonAPILogic, businessLogic EmailBusinessLogic) {
	commonAPILogic = apiLogic
	emailBusinessLogic = businessLogic
}
