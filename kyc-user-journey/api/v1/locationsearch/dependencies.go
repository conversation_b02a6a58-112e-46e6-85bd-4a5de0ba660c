package locationsearch

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type LocationSearchBusinessLogic interface {
	LocationSearchByPinCode(ctx context.Context, pinCode string) (*api.LocationSearchAPIResponse, error)
	GetCityAndStateByPinCode(ctx context.Context, pinCode string) (*api.LocationSearchByPincodeAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var locationSearchBusinessLogic LocationSearchBusinessLogic

func SetLocationSearchBusinessLogic(businessLogic LocationSearchBusinessLogic) {
	locationSearchBusinessLogic = businessLogic
}
