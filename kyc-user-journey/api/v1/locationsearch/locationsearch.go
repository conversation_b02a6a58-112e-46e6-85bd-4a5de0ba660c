package locationsearch

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/gin-gonic/gin"
)

// HandleLocationSearchRoute godoc
// @Summary Handle Location Search
// @Description Gives Location Area based on pincode
// @ID HandleLocationSearchRoute
// @Tags search
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/location [GET].
func HandleLocationSearchRoute(ctx *gin.Context) {
	pinCode := ctx.Query(constants.PinCodeQueryParamKey)
	if !utils.IsValidAgainstRegex(config.Application().Validations.PincodeRegex, pinCode) {
		err := constants.ErrInvalidPincode.Value()
		log.ErrorWarn(ctx, err).Msg("HandleLocationSearchRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	res, err := locationSearchBusinessLogic.LocationSearchByPinCode(ctx, pinCode)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleLocationSearchRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, res))
}

// HandleLocationSearchRouteV1 godoc
// @Summary Handle Location Search
// @Description Gives Location Area based on pincode
// @ID HandleLocationSearchRouteV1
// @Tags search
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/location/location-new [GET].
func HandleLocationSearchRouteV1(ctx *gin.Context) {
	pinCode := ctx.Query(constants.PinCodeQueryParamKey)
	if !utils.IsValidAgainstRegex(config.Application().Validations.PincodeRegex, pinCode) {
		err := constants.ErrInvalidPincode.Value()
		log.ErrorWarn(ctx, err).Msg("HandleLocationSearchRouteV1: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	res, err := locationSearchBusinessLogic.GetCityAndStateByPinCode(ctx, pinCode)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleLocationSearchRouteV1: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, res))
}
