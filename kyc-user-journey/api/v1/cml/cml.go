package cml

import (
	"fmt"
	"net/http"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleGenerateCmlRoute(ctx *gin.Context) {
	var request modelsAPIV1.CMLReportPdfRequest
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenerateCmlRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// now validate the request
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenerateCmlRoute: error validating request body for cml report generation")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// do the request
	response, err := cmlBusinessLogic.GenerateAndSignCMLReportPdf(ctx, request.ClientID)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// Set the headers to indicate a file attachment
	filename := fmt.Sprintf("%s_cmlReport.pdf", request.ClientID)
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	ctx.Data(http.StatusOK, "application/pdf", response)
}
