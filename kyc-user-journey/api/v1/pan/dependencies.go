package pan

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PanBusinessLogic interface {
	PanDetails(ctx context.Context, request *modelsV1.PanAPIRequest) (*modelsV1.PanAPIResponse, error)
	PanValidate(ctx context.Context, request *modelsV1.PanValidateAPIRequest) error
	EmodPanValidate(ctx context.Context, request *modelsV1.PanValidateAPIRequest) error
	PanOCR(ctx context.Context, request *modelsV1.PanOCRRequest) (*modelsV1.PanOCRResponse, error)
	PanConfirm(ctx context.Context, request *modelsV1.PanConfirmRequest) (*modelsV1.PanConfirmResponse, error)
	GetPanParticulars(ctx context.Context, request *modelsV1.PanParticularsAPIRequest) (*modelsV1.PanParticularsAPIResponse, error)
	GetPan(ctx context.Context, request *modelsV1.GetPanAPIRequest) (*modelsV1.GetPanAPIResponse, error)
	PanVerification(ctx context.Context, req *modelsV1.PanVerificationAPIRequest) (*modelsV1.PanVerificationAPIResponseData, error)
	AddSEBIBannedPan(ctx context.Context, req *modelsV1.SEBIBannedRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var panBusinessLogic PanBusinessLogic

func SetPanBusinessLogic(apiLogic CommonAPILogic, businessLogic PanBusinessLogic) {
	commonAPILogic = apiLogic
	panBusinessLogic = businessLogic
}
