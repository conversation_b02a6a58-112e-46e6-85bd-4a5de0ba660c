package pan

import (
	"strconv"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionPanDetailsRoute godoc
// @Summary Correction PanDetails Route
// @Description API to post pan of Correction Journey
// @ID HandleCorrectionPanDetailsRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanAPIRequest true "pan details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/pan/details [POST].
func HandleCorrectionPanDetailsRoute(ctx *gin.Context) {
	handleGenericPanDetailsRoute(ctx)
}

// HandleOnboardingPanDetailsRoute godoc
// @Summary PanDetails Route
// @Description API to post pan in onboarding flow
// @ID HandleOnboardingPanDetailsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanAPIRequest true "pan details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/pan/details [POST].
func HandleOnboardingPanDetailsRoute(ctx *gin.Context) {
	handleGenericPanDetailsRoute(ctx)
}

func handleGenericPanDetailsRoute(ctx *gin.Context) {
	request := modelsV1.PanAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := panBusinessLogic.PanDetails(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleEmodPanValidationRoute godoc
// @Summary PanValidation Route
// @Description API to validate pan details in emod flow
// @ID HandleEmodPanValidationRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param X-intent header string true "intent"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanValidationAPIRequest true "pan validation request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/pan/validate [POST].
func HandleEmodPanValidationRoute(ctx *gin.Context) {
	request := modelsV1.PanValidateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating pan validation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = panBusinessLogic.EmodPanValidate(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing pan validation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleOnboardingPanValidationRoute godoc
// @Summary PanValidation Route
// @Description API to validate  in onboarding flow
// @ID HandleOnboardingPanValidationRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanValidationAPIRequest true "pan validation request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/pan/validate [POST].
func HandleOnboardingPanValidationRoute(ctx *gin.Context) {
	handleGenericPanValidateRoute(ctx)
}

// HandleCorrectionPanValidationRoute godoc
// @Summary PanValidation Route
// @Description API to validate  pan particulars in correction flow
// @ID HandleCorrectionPanValidationRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanValidationAPIRequest true "pan validation request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/pan/validate [POST].
func HandleCorrectionPanValidationRoute(ctx *gin.Context) {
	handleGenericPanValidateRoute(ctx)
}

func handleGenericPanValidateRoute(ctx *gin.Context) {
	request := modelsV1.PanValidateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating pan validation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = panBusinessLogic.PanValidate(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing pan validation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// POST v1/onboarding/pan/ocr.
func HandlePanOcrRoute(ctx *gin.Context) {
	var request modelsV1.PanOCRRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	var err error
	request.Data.File, err = ctx.FormFile(constants.PanFormKey)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandlePanOcrRoute: error fetching the pan file")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingPanImage.WithDetails(err.Error())))
		return
	}

	// validate request
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandlePanOcrRoute: error while validating pan ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	resp, err := panBusinessLogic.PanOCR(ctx, &request)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, resp))
}

// POST  v1/onboarding/pan/ocr/confirm.
func HandlePanConfirmRoute(ctx *gin.Context) {
	var request = modelsV1.PanConfirmRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandlePanConfirmRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandlePanConfirmRoute: error while validating pan ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	resp, err := panBusinessLogic.PanConfirm(ctx, &request)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, *resp))
}

// HandleOnboardingPanParticularsRoute godoc
// @Summary PanParticulars Route
// @Description API to get pan particulars in onboarding flow.
// @ID HandleOnboardingPanParticularsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanParticularsAPIRequest true "pan particulars request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/pan-particulars [GET].
func HandleOnboardingPanParticularsRoute(ctx *gin.Context) {
	handleGenericPanParticularsRoute(ctx)
}

func handleGenericPanParticularsRoute(ctx *gin.Context) {
	request := modelsV1.PanParticularsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandlePanParticularsRoute- error validating pan particulars request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	response, err := panBusinessLogic.GetPanParticulars(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandlePanParticularsRoute- error processing pan particulars request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingPanRoute godoc
// @Summary Pan Route
// @Description API to get pan in onboarding flow.
// @ID HandleOnboardingPanRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanAPIRequest true "pan request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/pan [GET].
func HandleOnboardingPanRoute(ctx *gin.Context) {
	handleGenericPanGetRoute(ctx)
}

func handleGenericPanGetRoute(ctx *gin.Context) {
	request := modelsV1.GetPanAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.Prefetched, _ = strconv.ParseBool(ctx.Query(constants.PanPrefetchedQueryParamKey))

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericPanGetRoute- error validating pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	response, err := panBusinessLogic.GetPan(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericPanGetRoute- error processing pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func HandleOnboardingPanVerificationRoute(ctx *gin.Context) {
	handleGenericPanVerificationRoute(ctx, constants.Onboarding)
}

// HandleNriPanRoute godoc
// @Summary Pan Route
// @Description API to get pan in nri onboarding flow.
// @ID HandleNriPanRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanNriRequest true "pan request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/pan [POST].
func HandleOnboardingNriPanVerificationRoute(ctx *gin.Context) {
	handleGenericPanVerificationRoute(ctx, constants.NRI)
}

func handleGenericPanVerificationRoute(ctx *gin.Context, journeyType string) {
	request := modelsV1.PanVerificationAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.JourneyType = journeyType

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericPanVerificationRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericPanVerificationRoute: error validating pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := panBusinessLogic.PanVerification(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericPanVerificationRoute: error processing pan request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleSEBIBannedPanDetails godoc
// @Summary Pan Route
// @Description API to add new sebi banned pan details
// @ID HandleSEBIBannedPanRoute
// @Tags sebi_banned
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PanAPIRequest true "pan request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /internal/v1/pan/sebi-banned [POST].
func HandleSEBIBannedPanDetails(ctx *gin.Context) {
	request := modelsV1.SEBIBannedRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSEBIBannedPanDetails - Invalid request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate the request
	err = request.Validate() // now process the request.
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating HandleSEBIBannedPanDetails")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	err = panBusinessLogic.AddSEBIBannedPan(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleSEBIBannedPanDetails: error while processing HandleSEBIBannedPanDetails")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
