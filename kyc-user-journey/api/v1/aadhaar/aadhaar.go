package aadhaar

import (
	"strconv"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPI "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// POST v1/onboarding/aadhaar/ocr.
func HandleAadhaarOcrRoute(ctx *gin.Context) {
	var request modelsAPI.AadhaarOcrRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	err := parseAadhaarOCRRequestData(ctx, &request)
	if err != nil {
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating aadhaar ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	resp, err := aadhaarBusinessLogic.AadhaarOcr(ctx, &request)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, *resp))
}

// POST  v1/onboarding/aadhaar/ocr/confirm.
func HandleAadhaarConfirmRoute(ctx *gin.Context) {
	var request = modelsAPI.AadhaarConfirmRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleAadhaarConfirmRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate request
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating aadhaar ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	resp, err := aadhaarBusinessLogic.AadhaarConfirm(ctx, &request)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, *resp))
}

// HandleOnboardingSkipPoaRoute godoc
// @Summary PoaSkip Route
// @Description API to check if poa to be skipped in onboarding flow.
// @ID HandleOnboardingSkipPoaRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.SkipPoaAPIRequest true "skip poa request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/poa [GET].
func HandleOnboardingPOARoute(ctx *gin.Context) {
	request := modelsAPI.GetPOARequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating skip poa request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := aadhaarBusinessLogic.GetPOAData(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing skip poa request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func parseAadhaarOCRRequestData(ctx *gin.Context, request *modelsAPI.AadhaarOcrRequest) error {
	// get the is changed flag
	request.Data.IsChanged = true
	isChanged, err := strconv.ParseBool(ctx.PostForm(constants.IsChangedKey))
	if err == nil && !isChanged {
		request.Data.IsChanged = false
	}

	// assuming we have two sides of Aadhaar.
	request.Data.IsSingleSide = false

	// process front page of Aadhaar.
	request.Data.FrontFile, err = ctx.FormFile(constants.AadhaarFrontFormKey)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error fetching the aadhaar front file")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingAadhaarFrontImage.WithDetails(err.Error())))
		return err
	}

	log.Debug(ctx).Str(constants.LogFileKey, request.Data.FrontFile.Filename).Msg("aadhaar front input file")

	// process back page of Aadhaar.
	request.Data.BackFile, err = ctx.FormFile(constants.AadhaarBackFormKey)
	if err != nil {
		request.Data.IsSingleSide = true
	}

	return nil
}
