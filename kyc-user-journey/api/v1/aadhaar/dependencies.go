package aadhaar

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type AadharBusinessLogic interface {
	AadhaarOcr(ctx context.Context, request *api.AadhaarOcrRequest) (*api.AadhaarResponseData, error)
	AadhaarConfirm(ctx context.Context, request *api.AadhaarConfirmRequest) (*api.AadhaarResponseData, error)
	GetPOAData(ctx context.Context, request *api.GetPOARequest) (*api.GetPOAResponse, error)
}

var commonAPILogic CommonAPILogic
var aadhaarBusinessLogic AadharBusinessLogic

func SetAadharBusinessLogic(commonLogic CommonAPILogic, ocrBusinessLogic AadharBusinessLogic) {
	commonAPILogic = commonLogic
	aadhaarBusinessLogic = ocrBusinessLogic
}
