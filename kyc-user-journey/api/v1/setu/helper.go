package setu

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"
	goUtils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/security"
)

func decryptSetuRequestData(ctx context.Context, request *modelsV1.ESignSetuRedirectAPIRequestData) error {
	key := config.Security().AESEncryptionKey
	setuRedirectURLDataString, err := security.DecryptAES([]byte(key), request.RequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptSetuRequestData: error decrypting request data string")
		return constants.ErrAESDecryption.WithDetails(err.Error())
	}

	err = goUtils.UnmarshalJSON([]byte(setuRedirectURLDataString), &request.DecodedRequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptSetuRequestData: error unmarshalling setu redirect URL data string")
		return err
	}
	return nil
}
