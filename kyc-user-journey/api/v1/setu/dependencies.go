package setu

import (
	"context"

	models2 "github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/task"
	"github.com/gin-gonic/gin"
)

type SetuBusinessLogic interface {
	InitiateESign(ctx context.Context, request *modelsV1.ESignSetuAPIRequest) (*modelsV1.ESignSetuAPIResponse, error)
	RedirectFromSetu(ctx context.Context, request *modelsV1.ESignSetuRedirectAPIRequest) *modelsV1.SetuRedirectAPIResponse
	CreateEsignPDFGeneric(ctx context.Context, request *modelsV1.ESignCreatePDFAPIRequest, esignTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) (*modelsV1.ESignCreatePDFAPIResponse, error)
	InitiateEsignGeneric(ctx context.Context, request *modelsV1.ESignSetuAPIRequest, esignTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) (*modelsV1.ESignSetuAPIResponse, error)
	RedirectFromSetuGeneric(ctx context.Context, request *modelsV1.ESignSetuRedirectAPIRequest, esignRedirectionTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) *modelsV1.SetuRedirectAPIResponse
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var setuBusinessLogic SetuBusinessLogic
var esignOnboardingTask task.SetuEsignTask
var eSignNRIOnboardingTask task.SetuEsignTask

func SetSetuBusinessLogic(apiLogic CommonAPILogic, businessLogic SetuBusinessLogic, esignOnboardingTaskLogic, eSignNRIOnboardingTaskLogic task.SetuEsignTask) {
	commonAPILogic = apiLogic
	setuBusinessLogic = businessLogic
	esignOnboardingTask = esignOnboardingTaskLogic
	eSignNRIOnboardingTask = eSignNRIOnboardingTaskLogic
}
