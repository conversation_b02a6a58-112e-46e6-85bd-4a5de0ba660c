package setu

import (
	"context"
	"net/http"

	"github.com/angel-one/kyc-user-journey/config"
	models2 "github.com/angel-one/kyc-user-journey/config/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/task"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/api/v1/esign"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionSetuESignRoute godoc
// @Summary Correction Esign Route
// @Description API to get esign of Correction Journey
// @ID HandleCorrectionEsignRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/setu/esign [GET].
func HandleCorrectionSetuESignRoute(ctx *gin.Context) {
	request := modelsV1.ESignSetuAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	response, err := setuBusinessLogic.InitiateESign(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting setu esign response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingSetuESignRoute godoc
// @Summary Setu ESign Route
// @Description API to get setu eSign in onboarding flow
// @ID HandleOnboardingSetuESignRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/setu/esign [GET].
func HandleOnboardingSetuESignRoute(ctx *gin.Context) {
	onboardingConfig := config.Setu().Flow.Onboarding
	handleGenericSetuESignRoute(ctx, esignOnboardingTask, &onboardingConfig)
}

// HandleNRIOnboardingSetuESignRoute godoc
// @Summary Setu NRI ESign Route
// @Description API to get setu eSign in nri onboarding flow
// @ID HandleNRIOnboardingSetuESignRoute
// @Tags nri
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/setu/esign [GET].
func HandleNRIOnboardingSetuESignRoute(ctx *gin.Context) {
	nriConfig := config.Setu().Flow.NRI
	handleGenericSetuESignRoute(ctx, eSignNRIOnboardingTask, &nriConfig)
}

func handleGenericSetuESignRoute(ctx *gin.Context, esignTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) {
	request := modelsV1.ESignSetuAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	response, err := setuBusinessLogic.InitiateEsignGeneric(ctx, &request, esignTask, flowConfig)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting setu esign response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionRedirectFromSetu godoc
// @Summary Handle Setu callback and redirect the user
// @Message Handle Setu callback and redirect the user
// @ID HandleCorrectionRedirectFromSetu
// @Tags kyc
// @Param requestData query string true "encrypted request data sent to setu in the redirect URl"
// @Param status query string true "status from setu. It can be either true or false"
// @Param id query string true "Setu request ID"
// @Param esp query string true "e-sign provider, i.e. NSDL, emudhra, etc"
// @Param signerIdentifier query string false "Identifier assigned by setu for a document signer. It is also received in the setu initiate request response "
// @Param errCode query string false "Error code from setu. Eg: signer_declined etc. Refer setu documentation for the possible values"
// @Param errorMessage query string false "Message returned by setu for errors. eg: Document+signing+was+declined"
// @Param source query string true "source sent to setu in redirect URL"
// @Success 303
// @Failure 303
// @Router /v1/correction/setu/redirect [get].
func HandleCorrectionRedirectFromSetu(ctx *gin.Context) {
	request := modelsV1.ESignSetuRedirectAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	parseRedirectionQueryParams(ctx, &request)
	response := setuBusinessLogic.RedirectFromSetu(ctx, &request)
	ctx.Redirect(http.StatusSeeOther, response.RedirectURL)
}

// HandleOnboardingRedirectFromSetu godoc
// @Summary Handle Setu callback and redirect the user
// @Message Handle Setu callback and redirect the user
// @ID HandleOnboardingRedirectFromSetu
// @Tags kyc
// @Param requestData query string true "encrypted request data sent to setu in the redirect URl"
// @Param status query string true "status from setu. It can be either true or false"
// @Param id query string true "Setu request ID"
// @Param esp query string true "e-sign provider, i.e. NSDL, emudhra, etc"
// @Param signerIdentifier query string false "Identifier assigned by setu for a document signer. It is also received in the setu initiate request response "
// @Param errCode query string false "Error code from setu. Eg: signer_declined etc. Refer setu documentation for the possible values"
// @Param errorMessage query string false "Message returned by setu for errors. eg: Document+signing+was+declined"
// @Param source query string true "source sent to setu in redirect URL"
// @Success 303
// @Failure 303
// @Router /v1/onboarding/setu/redirect [get].
func HandleOnboardingRedirectFromSetu(ctx *gin.Context) {
	request := modelsV1.ESignSetuRedirectAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	parseRedirectionQueryParams(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromSetu: error validating req")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = decryptSetuRequestData(ctx, &request.Data)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromSetu: error decrypting data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = validateDecodedRequestDataOnboarding(ctx, &request.Data.DecodedRequestData)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromSetu: error validating data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	onboardingFlowConfig := config.Setu().Flow.Onboarding
	handleGenericRedirectFromSetu(ctx, &request, esignOnboardingTask, &onboardingFlowConfig)
}

// HandleNRIOnboardingRedirectFromSetu godoc
// @Summary Handle Setu callback and redirect the user
// @Message Handle Setu callback and redirect the user
// @ID HandleNRIOnboardingRedirectFromSetu
// @Tags nri
// @Param requestData query string true "encrypted request data sent to setu in the redirect URl"
// @Param status query string true "status from setu. It can be either true or false"
// @Param id query string true "Setu request ID"
// @Param esp query string true "e-sign provider, i.e. NSDL, emudhra, etc"
// @Param signerIdentifier query string false "Identifier assigned by setu for a document signer. It is also received in the setu initiate request response "
// @Param errCode query string false "Error code from setu. Eg: signer_declined etc. Refer setu documentation for the possible values"
// @Param errorMessage query string false "Message returned by setu for errors. eg: Document+signing+was+declined"
// @Param source query string true "source sent to setu in redirect URL"
// @Success 303
// @Failure 303
// @Router /v1/onboarding/nri/setu/redirect [get].
func HandleNRIOnboardingRedirectFromSetu(ctx *gin.Context) {
	request := modelsV1.ESignSetuRedirectAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	parseRedirectionQueryParams(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNRIOnboardingRedirectFromSetu: error validating req")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = decryptSetuRequestData(ctx, &request.Data)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNRIOnboardingRedirectFromSetu: error decrypting data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = validateDecodedRequestDataOnboarding(ctx, &request.Data.DecodedRequestData)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNRIOnboardingRedirectFromSetu: error validating data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	nriConfig := config.Setu().Flow.NRI
	handleGenericRedirectFromSetu(ctx, &request, eSignNRIOnboardingTask, &nriConfig)
}

func validateDecodedRequestDataOnboarding(_ context.Context, u *modelsV1.ESignSetuRedirectURLData) error {
	if u.Source == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty source")
	}
	if u.AppNumber == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty appNumber")
	}
	if u.Platform == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty platform")
	}
	if u.Mobile == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty mobile")
	}
	if u.CorrelationID == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty correlation id")
	}
	if u.AppVersion == constants.Empty {
		return constants.ErrSetuAPIValidationFailed.WithDetails("empty appversion")
	}
	return nil
}

func handleGenericRedirectFromSetu(ctx *gin.Context, request *modelsV1.ESignSetuRedirectAPIRequest, esignTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) {
	ctx.Set(constants.AppNumberContextKey, request.Data.DecodedRequestData.AppNumber)
	ctx.Set(constants.CorrelationIDContextKey, request.Data.DecodedRequestData.AppNumber)
	response := setuBusinessLogic.RedirectFromSetuGeneric(ctx, request, esignTask, flowConfig)
	ctx.Redirect(http.StatusSeeOther, response.RedirectURL)
}

// HandleCorrectionESignPDFCreation godoc
// @Summary Handle E-Sign PDF creation
// @Message Handle E-Sign PDF creation
// @ID HandleCorrectionESignPDFCreation
// @Tags kyc
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/setu/pdf [post].
func HandleCorrectionESignPDFCreation(ctx *gin.Context) {
	esign.HandleGenericESignPDFCreation(ctx)
}

// HandleOnboardingESignPDFCreation godoc
// @Summary Handle E-Sign PDF creation
// @Message Handle E-Sign PDF creation
// @ID HandleOnboardingESignPDFCreation
// @Tags onboarding
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/setu/pdf [post].
func HandleOnboardingESignPDFCreation(ctx *gin.Context) {
	flowConfig := config.Setu().Flow.Onboarding
	handleGenericESignPDFCreation(ctx, esignOnboardingTask, &flowConfig)
}

// HandleNRIOnboardingESignPDFCreation godoc
// @Summary Handle E-Sign PDF creation
// @Message Handle E-Sign PDF creation
// @ID HandleNRIOnboardingESignPDFCreation
// @Tags nri
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/setu/pdf [post].
func HandleNRIOnboardingESignPDFCreation(ctx *gin.Context) {
	flowConfig := config.Setu().Flow.NRI
	handleGenericESignPDFCreation(ctx, eSignNRIOnboardingTask, &flowConfig)
}

func handleGenericESignPDFCreation(ctx *gin.Context, esignTask task.SetuEsignTask, flowConfig *models2.SetuFlowConfig) {
	request := modelsV1.ESignCreatePDFAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	response, err := setuBusinessLogic.CreateEsignPDFGeneric(ctx, &request, esignTask, flowConfig)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericESignPDFCreation: error while processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionESignEligibility godoc
// @Summary eSign eligibility Route
// @Description API to check eSign eligibility
// @ID HandleCorrectionESignEligibility
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.ESignEligibilityAPIRequest true "esign eligibility request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/setu/eligibility [GET].
func HandleCorrectionESignEligibility(ctx *gin.Context) {
	esign.HandleGenericESignEligibility(ctx)
}

// HandleOnBoardingSignEligibility godoc
// @Summary eSign eligibility Route
// @Description API to check eSign eligibility
// @ID HandleOnBoardingSignEligibility
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.ESignEligibilityAPIRequest true "esign eligibility request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/setu/eligibility [GET].
func HandleOnBoardingSignEligibility(ctx *gin.Context) {
	esign.HandleGenericESignEligibility(ctx)
}

func parseRedirectionQueryParams(ctx *gin.Context, request *modelsV1.ESignSetuRedirectAPIRequest) {
	request.Data.RequestData = ctx.Query(constants.SetuRequestDataQueryParamKey)
	request.Data.Success = ctx.Query(constants.SetuSuccessQueryParamKey)
	request.Data.ID = ctx.Query(constants.SetuESignRequestIDQueryParamKey)
	request.Data.SignerIdentifier = ctx.Query(constants.SetuSignerIdentifierQueryParamKey)
	request.Data.ErrorCode = ctx.Query(constants.SetuErrorCodeQueryParamKey)
	request.Data.ErrorMessage = ctx.Query(constants.SetuErrorMessageQueryParamKey)
	request.Data.Source = ctx.Query(constants.SetuSourceQueryParamKey)
	request.Data.ESignProvider = ctx.Query(constants.SetuEsignProviderQueryParamKey)
}
