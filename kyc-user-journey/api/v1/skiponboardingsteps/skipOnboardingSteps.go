package skiponboardingsteps

import (
	"github.com/gin-gonic/gin"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
)

// HandleSkipOnboardingSteps Handle skip onboarding step godoc
// @Summary skip onboarding Route
// @Description API to skip digio, cams, esign etc.
// @ID HandleSkipOnboardingSteps
// @Tags HandleSkipOnboardingSteps
// / @Accept formData
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/skipFlow [POST].
func HandleSkipOnboardingSteps(ctx *gin.Context) {
	request := modelsV1.SkipOnboardingStepsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := BindFormDataAndHandleFileUploads(ctx, &request)
	if err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSkipOnboardingSteps: error validating  skip onboarding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = skipOnboardingStepsBusinessLogic.SkipOnboardingSteps(ctx, &request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleSkipOnboardingSteps: error while skipping onboarding steps")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// BindFormDataAndHandleFileUploads binds form-data fields and handles file uploads.
func BindFormDataAndHandleFileUploads(ctx *gin.Context, request *modelsV1.SkipOnboardingStepsAPIRequest) error {
	request.Data.Flow = ctx.PostForm("flow")
	request.Data.ID = ctx.PostForm("aadhaarNumber")
	request.Data.FullName = ctx.PostForm("fullName")
	request.Data.Type = ctx.PostForm("type")
	request.Data.AddressLine1 = ctx.PostForm("addressLine")
	request.Data.AddressPincode = ctx.PostForm("addressPincode")

	var err error
	request.Data.FirstPath, err = ctx.FormFile("aadhaarFront")
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error fetching the aadhaar front file")
		return constants.ErrBindingRequest.WithDetails(err.Error())
	}

	request.Data.SecondPath, err = ctx.FormFile("aadhaarBack")
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error fetching the aadhaar back file")
		return constants.ErrBindingRequest.WithDetails(err.Error())
	}

	request.Data.PhotoPath, err = ctx.FormFile("photo")
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error fetching the photo file")
		return constants.ErrBindingRequest.WithDetails(err.Error())
	}

	return nil
}
