package skiponboardingsteps

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type SkipOnboardingStepsBusinessLogic interface {
	SkipOnboardingSteps(ctx context.Context, request *modelsV1.SkipOnboardingStepsAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic

var skipOnboardingStepsBusinessLogic SkipOnboardingStepsBusinessLogic

func SetLeadBusinessLogic(apiLogic CommonAPILogic, businessLogic SkipOnboardingStepsBusinessLogic) {
	commonAPILogic = apiLogic
	skipOnboardingStepsBusinessLogic = businessLogic
}
