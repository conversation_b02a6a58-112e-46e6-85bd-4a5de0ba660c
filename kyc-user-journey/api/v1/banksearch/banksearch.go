package banksearch

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleBankIfscSearch godoc
// @Summary Handle IFSC List
// @Message Handle IFSC List
// @ID HandleBankIfscSearch
// @Tags search
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-source header string true "source"
// @Param Authorization header string true "Bearer ${JWT}"
// @Param search query string false "search string"
// @Param from query string false "from string"
// @Param size query string false "size string"
// @Success 200 {object} models.BankIfscSearchAPIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/bank/ifsc [get].
func HandleBankIfscSearch(ctx *gin.Context) {
	searchQueryParam := ctx.Query(constants.SearchQuery)
	from := ctx.Query(constants.FromQuery)
	size := ctx.Query(constants.SizeQuery)

	var bankIfscSearchRequest modelsV1.BankIfscSearchAPIRequest
	err := bankIfscSearchRequest.Create(from, size, searchQueryParam)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleBankIfscSearch -Error converting query param to int in bank ifsc search API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := bankSearchBusinessLogic.BankIfscSearch(ctx, bankIfscSearchRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleBankIfscSearch - error searching for ifsc")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleBankBranchSearch godoc
// @Summary Handle Bank List
// @Message Handle Bank List
// @ID HandleBankBranchSearch
// @Tags search
// @Produce json
// @Param X-source header string true "source"
// @Param Authorization header string true "Bearer ${JWT}"
// @Param search query string false "search string"
// @Param from query string false "search string"
// @Param size query string false "search string"
// @Success 200 {object} models.BankBranchSearchAPIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /v1/bank/branch [get].
func HandleBankBranchSearch(ctx *gin.Context) {
	searchQueryParam := ctx.Query(constants.SearchQuery)
	from := ctx.Query(constants.FromQuery)
	size := ctx.Query(constants.SizeQuery)
	bankQueryParam := ctx.Query(constants.BankQueryParam)

	var bankBranchSearchRequest modelsV1.BankBranchSearchAPIRequest
	err := bankBranchSearchRequest.Create(from, size, bankQueryParam, searchQueryParam)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleBankBranchSearch -Error converting query param to int in bank branch search API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := bankSearchBusinessLogic.BankBranchSearch(ctx, bankBranchSearchRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleBankBranchSearch - error searching for bank")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleBankSearch godoc
// @Summary Handle Bank List
// @Message Handle Bank List
// @ID HandleBankSearch
// @Tags search
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-requestId header string false "request id"
// @Param X-source header string true "source"
// @Param Authorization header string true "Bearer ${JWT}"
// @Param search query string false "search string"
// @Param from query string false "search string"
// @Param size query string false "search string"
// @Success 200 {object} models.BankBranchSearchAPIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /v1/bank/list [get].
func HandleBankSearch(ctx *gin.Context) {
	searchQueryParam := ctx.Query(constants.SearchQuery)
	from := ctx.Query(constants.FromQuery)
	size := ctx.Query(constants.SizeQuery)

	var bankSearchRequest modelsV1.BankSearchAPIRequest
	err := bankSearchRequest.Create(from, size, searchQueryParam)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleSearch - Error converting query param to int in bank search API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := bankSearchBusinessLogic.BankSearch(ctx, bankSearchRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleBankSearch - error searching for bank")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
