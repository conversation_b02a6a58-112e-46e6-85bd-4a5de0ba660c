package banksearch

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type BankSearchBusinessLogic interface {
	BankIfscSearch(ctx context.Context, bankIfscSearchRequest modelsV1.BankIfscSearchAPIRequest) (*modelsV1.BankIfscSearchAPIResponse, error)
	BankBranchSearch(ctx context.Context, bankBranchSearchRequest modelsV1.BankBranchSearchAPIRequest) (*modelsV1.BankBranchSearchAPIResponse, error)
	BankSearch(ctx context.Context, bankSearchRequest modelsV1.BankSearchAPIRequest) (*modelsV1.BankSearchAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var bankSearchBusinessLogic BankSearchBusinessLogic

func SetBankSearchBusinessLogic(businessLogic BankSearchBusinessLogic) {
	bankSearchBusinessLogic = businessLogic
}
