package diy

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type JourneyDIYToggleBusinessLogic interface {
	CheckAndActivateDIYESignJourney(ctx context.Context, request *modelsAPIV1.CorrectionDIYESignAPIRequest) (*modelsAPIV1.CorrectionDIYJourneyCheckAndActivateAPIResponse, error)
	CheckAndActivateCorrectionDIYJourney(ctx context.Context, request *modelsAPIV1.CorrectionDIYJourneyCheckAndActivateAPIRequest) (*modelsAPIV1.CorrectionDIYJourneyCheckAndActivateAPIResponse, error)
	SubmitCorrectionDIYJourney(ctx context.Context, request *modelsAPIV1.CorrectionJourneyDIYSubmitAPIRequest) (*modelsAPIV1.CorrectionJourneySubmitAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var correctionJourneyDIYToggleBusinessLogic JourneyDIYToggleBusinessLogic

func SetCorrectionDIYJourneyToggleBusinessLogic(apiLogic CommonAPILogic, businessLogic JourneyDIYToggleBusinessLogic) {
	commonAPILogic = apiLogic
	correctionJourneyDIYToggleBusinessLogic = businessLogic
}
