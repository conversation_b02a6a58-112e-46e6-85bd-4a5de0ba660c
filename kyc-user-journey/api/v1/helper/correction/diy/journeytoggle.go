package diy

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionJourneyDIYESignRoute godoc
// @Summary Correction DIY Journey CheckAndActivate Route
// @Description API to check and activate correction diy journey
// @ID HandleCorrectionJourneyDIYESignRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/helper/journey/diy/activate/esign [POST].
func HandleCorrectionJourneyDIYESignRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionDIYESignAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction helper journey diy eSign request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionJourneyDIYESignRoute - request validated successfully")

	response, err := correctionJourneyDIYToggleBusinessLogic.CheckAndActivateDIYESignJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYESignRoute - error getting correction helper journey check and activate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionJourneyDIYCheckAndActivateRoute godoc
// @Summary Correction DIY Journey CheckAndActivate Route
// @Description API to check and activate correction diy journey
// @ID HandleCorrectionJourneyDIYCheckAndActivateRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/helper/journey/diy/activate [POST].
func HandleCorrectionJourneyDIYCheckAndActivateRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionDIYJourneyCheckAndActivateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYCheckAndActivateRoute - Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYCheckAndActivateRoute - error validating correction helper journey check and activate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionJourneyDIYCheckAndActivateRoute - request validated successfully")

	response, err := correctionJourneyDIYToggleBusinessLogic.CheckAndActivateCorrectionDIYJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYCheckAndActivateRoute - error getting correction helper journey check and activate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionJourneyDIYSubmitRoute godoc
// @Summary Correction Journey Submit Route
// @Description API to submit active correction journeys
// @ID HandleCorrectionJourneyDIYSubmitRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/helper/journey/diy/submit [POST].
func HandleCorrectionJourneyDIYSubmitRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionJourneyDIYSubmitAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYSubmitRoute - error validating correction journey submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionJourneyDIYSubmitRoute - request validated successfully")

	response, err := correctionJourneyDIYToggleBusinessLogic.SubmitCorrectionDIYJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCorrectionJourneyDIYSubmitRoute - error getting correction helper journey submit response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
