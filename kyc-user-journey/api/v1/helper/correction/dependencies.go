package correction

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type JourneyToggleBusinessLogic interface {
	CheckAndActivateCorrectionJourney(ctx context.Context, request *modelsV1.CorrectionJourneyCheckAndActivateAPIRequest) (*modelsV1.CorrectionJourneyCheckAndActivateAPIResponse, error)
	DeactivateCorrectionJourney(ctx context.Context, request *modelsV1.CorrectionJourneyDeactivateAPIRequest) (*modelsV1.CorrectionJourneyDeactivateAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var correctionJourneyToggleBusinessLogic JourneyToggleBusinessLogic

func SetCorrectionJourneyToggleBusinessLogic(apiLogic CommonAPILogic, businessLogic JourneyToggleBusinessLogic) {
	commonAPILogic = apiLogic
	correctionJourneyToggleBusinessLogic = businessLogic
}
