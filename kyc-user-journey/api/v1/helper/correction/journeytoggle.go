package correction

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionJourneyDeactivateRoute godoc
// @Summary Correction Journey Deactivate Route
// @Description API to deactivate active correction journeys
// @ID HandleCorrectionJourneyDeactivateRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/helper/journey/deactivate [POST].
func HandleCorrectionJourneyDeactivateRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionJourneyDeactivateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction helper journey deactivate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionJourneyDeactivateRoute - request validated successfully")

	response, err := correctionJourneyToggleBusinessLogic.DeactivateCorrectionJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting correction helper journey deactivate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionJourneyCheckAndActivateRoute godoc
// @Summary Correction Journey CheckAndActivate Route
// @Description API to check and activate correction journey
// @ID HandleCorrectionJourneyCheckAndActivateRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/helper/journey/activate [POST].
func HandleCorrectionJourneyCheckAndActivateRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionJourneyCheckAndActivateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction helper journey check and activate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionJourneyCheckAndActivateRoute - request validated successfully")

	response, err := correctionJourneyToggleBusinessLogic.CheckAndActivateCorrectionJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting correction helper journey check and activate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
