package clientdetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type ClientDetailsBusinessLogic interface {
	ClientDetails(ctx context.Context, request *modelsV1.ClientDetailsAPIRequest) error
}

var commonAPILogic CommonAPILogic
var clientDetailsBusinessLogic ClientDetailsBusinessLogic
var clientDetailsNriBusinessLogic ClientDetailsNriBusinessLogic

func SetClientDetailsBusinessLogic(apiLogic CommonAPILogic, businessLogic ClientDetailsBusinessLogic) {
	commonAPILogic = apiLogic
	clientDetailsBusinessLogic = businessLogic
}

type ClientDetailsNriBusinessLogic interface {
	ClientDetailsNri(ctx context.Context, request *modelsV1.ClientDetailsNriRequest) error
}

func SetClientDetailsNriBusinessLogic(apiLogic CommonAPILogic, businessLogic ClientDetailsNriBusinessLogic) {
	commonAPILogic = apiLogic
	clientDetailsNriBusinessLogic = businessLogic
}
