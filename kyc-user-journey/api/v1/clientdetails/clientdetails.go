package clientdetails

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingClientDetailsRoute godoc
// @Summary ClientDetails Route
// @Description API to post client details in onboarding flow
// @ID HandleOnboardingClientDetailsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.ClientDetailsAPIRequest true "client details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/client-details [POST].
func HandleOnboardingClientDetailsRoute(ctx *gin.Context) {
	handleGenericClientDetailsRoute(ctx)
}

func handleGenericClientDetailsRoute(ctx *gin.Context) {
	request := modelsV1.ClientDetailsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating onboarding client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = clientDetailsBusinessLogic.ClientDetails(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleOnboardingNriClientDetailsRoute godoc
// @Summary Nri ClientDetails Route
// @Description API to post client details in onboarding flow
// @ID HandleOnboardingNriClientDetailsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.ClientDetailsNriRequest true "client details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/client-details [POST].
func HandleOnboardingNriClientDetailsRoute(ctx *gin.Context) {
	handleGenericNriClientDetailsRoute(ctx)
}

func handleGenericNriClientDetailsRoute(ctx *gin.Context) {
	request := modelsV1.ClientDetailsNriRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNriClientDetailsRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNriClientDetailsRoute: error validating onboarding nri client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = clientDetailsNriBusinessLogic.ClientDetailsNri(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNriClientDetailsRoute: error processing nri client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
