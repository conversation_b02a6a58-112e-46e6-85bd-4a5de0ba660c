package nriaddress

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNriAddressRoute godoc
// @Summary OnBoarding Nri Address Route
// @Description API to add address for NRI onboarding journey
// @ID HandleNriAddressRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// POST v1/nri/onboarding/address/:type.
func HandleNriAddressRoute(ctx *gin.Context) {
	var request modelsV1.NriAddressRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)
	request.AddressType = ctx.Param(constants.TypeKey)

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriAddressRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriAddressRoute: error while validating nri address request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = nriAddressLogic.NriAddress(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriAddressRoute: error processing nri address request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleNriSkipAddressRoute godoc
// @Summary OnBoarding Nri skip Address Route
// @Description API to skio address for NRI onboarding journey
// @ID HandleNriSkipAddressRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// POST v1/nri/onboarding/skip/address.
func HandleNriSkipAddressRoute(ctx *gin.Context) {
	request := modelsV1.NriAddressSkipAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriSkipAddressRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleNriSkipAddressRoute: - request validated successfully")

	// process the request.
	err = nriAddressLogic.SkipAddress(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriSkipAddressRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
