package nriaddress

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type NriAddressBusinessLogic interface {
	NriAddress(ctx context.Context, request *api.NriAddressRequest) error
	SkipAddress(ctx context.Context, request *api.NriAddressSkipAPIRequest) error
}

var commonAPILogic CommonAPILogic
var nriAddressLogic NriAddressBusinessLogic

func SetNriAddressBusinessLogic(commonLogic CommonAPILogic, businessLogic NriAddressBusinessLogic) {
	commonAPILogic = commonLogic
	nriAddressLogic = businessLogic
}
