package daeassistance

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type DaeAssistanceBusinessLogic interface {
	RequestDaeAssistance(ctx context.Context, request *modelsV1.DaeAssistanceRequest) (*modelsV1.DaeAssistanceResponse, error)
}

var commonAPILogic CommonAPILogic
var daeAssistanceBusinessLogic DaeAssistanceBusinessLogic

func SetDaeAssistanceBusinessLogic(apiLogic CommonAPILogic, businessLogic DaeAssistanceBusinessLogic) {
	commonAPILogic = apiLogic
	daeAssistanceBusinessLogic = businessLogic
}
