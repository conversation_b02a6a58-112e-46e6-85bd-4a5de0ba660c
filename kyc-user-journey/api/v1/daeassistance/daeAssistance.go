package daeassistance

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleDaeAssistanceRoute godoc
// @Summary DAE Assistance Route
// @Description API to request DAE Callback in onboarding flow
// @ID HandleDaeAssistanceRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.DaeAssistanceRequest true "page"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/dae-assistance [POST].
func HandleDaeAssistanceRoute(ctx *gin.Context) {
	handleGenericDaeAssistanceRoute(ctx)
}

func handleGenericDaeAssistanceRoute(ctx *gin.Context) {
	request := modelsV1.DaeAssistanceRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid dae assistance request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating dae assistance request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericDaeAssistanceRoute - request validated successfully")

	response, err := daeAssistanceBusinessLogic.RequestDaeAssistance(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing dae assistance request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
