package esign

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleGenericESignPDFCreation(ctx *gin.Context) {
	request := modelsV1.ESignCreatePDFAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	response, err := eSignBusinessLogic.CreateESignPDF(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting create esign pdf response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func HandleGenericESignEligibility(ctx *gin.Context) {
	request := modelsV1.ESignEligibilityAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating esign eligibility request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response := eSignBusinessLogic.CheckESignEligibility(ctx, request.AppNumber, request.CorrectionJourneyType)
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleESignPdf godoc
// @Summary Returns E-Sign PDF
// @Message Handle E-Sign PDF
// @ID HandleESignPdf
// @Tags kyc
// @Accept mpfd
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-requestId header string false "request id"
// @Param X-source header string true "source"
// @Param X-platform header string true "source"
// @Param AppVersion header string true "AppVersion"
// @Param Authorization header string true "Bearer ${JWT}"
// @Success 200 {object} models.ESignPdfResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /v1/kyc/esign/pdf [GET].
func HandleESignPdf(ctx *gin.Context) {
	request := modelsV1.ESignPdfRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
	}

	userID := ctx.GetString(constants.ClientCodeContextKey)
	ctx.Set(constants.CorrelationIDContextKey, ctx.GetString(constants.ClientCodeContextKey))

	var err error

	// now validate the request
	err = request.Validate(userID)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error validating the e-sign pdf request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now process the request
	response, err := eSignBusinessLogic.ESignPdf(ctx, userID)

	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing e-sign pdf request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func HandleEsignDelete(ctx *gin.Context) {
	request := modelsV1.ESignDeleteRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEsignDelete: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEsignDelete: error validating esign delete request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = eSignBusinessLogic.ESignDelete(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEsignDelete: error processing emod delete request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func HandleOnboardingESignEligibility(ctx *gin.Context) {
	request := modelsV1.ESignEligibilityAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingESignEligibility: error validating esign eligibility request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = eSignOnboardingBusinessLogic.CheckEsignEligibility(ctx, request.AppNumber, request.Mobile, request.Source)

	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleOnboardingESignEligibility: error processing esign eligibility request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
