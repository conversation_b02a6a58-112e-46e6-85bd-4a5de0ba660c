package esign

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type ESignBusinessLogic interface {
	CreateESignPDF(ctx context.Context, request *modelsV1.ESignCreatePDFAPIRequest) (*modelsV1.ESignCreatePDFAPIResponse, error)
	CheckESignEligibility(ctx context.Context, appNumber, correctionJourneyType string) *modelsV1.ESignEligibilityAPIResponse
	ESignPdf(ctx context.Context, userID string) (*modelsV1.ESignPdfResponse, error)
	ESignDelete(ctx context.Context, request *modelsV1.ESignDeleteRequest) error
}

type EsignOnboardingBusinessLogic interface {
	CheckEsignEligibility(ctx context.Context, appNumber, mobile, source string) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var eSignBusinessLogic ESignBusinessLogic
var eSignOnboardingBusinessLogic EsignOnboardingBusinessLogic

func SetESignBusinessLogic(apiLogic CommonAPILogic, businessLogic ESignBusinessLogic, esignOnboardingBusinessLogic EsignOnboardingBusinessLogic) {
	commonAPILogic = apiLogic
	eSignBusinessLogic = businessLogic
	eSignOnboardingBusinessLogic = esignOnboardingBusinessLogic
}
