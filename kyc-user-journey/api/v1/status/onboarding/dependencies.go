package onboarding

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type StatusBusinessLogic interface {
	Status(ctx context.Context, request *modelsV1.StatusAPIRequest) (*modelsV1.StatusAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var statusBusinessLogic StatusBusinessLogic

func SetStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic StatusBusinessLogic) {
	commonAPILogic = apiLogic
	statusBusinessLogic = businessLogic
}
