package onboarding

import (
	"strconv"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingStatusRoute godoc
// @Summary Onboarding Status Route
// @Description API to fetch status of Onboarding Journey
// @ID HandleOnboardingStatusRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/lead/status [GET].
func HandleOnboardingStatusRoute(ctx *gin.Context) {
	handleGenericStatusRoute(ctx)
}

func handleGenericStatusRoute(ctx *gin.Context) {
	request := modelsV1.StatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.IgnoreRejections, _ = strconv.ParseBool(ctx.Query(constants.StatusIgnoreRejectionsQueryParamKey))
	request.IgnoreOneKYCStatus, _ = strconv.ParseBool(ctx.Query(constants.StatusIgnoreOneKYCStatusQueryParamKey))
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating onboarding status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericStatusRoute - request validated successfully")
	response, err := statusBusinessLogic.Status(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting onboarding status response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
