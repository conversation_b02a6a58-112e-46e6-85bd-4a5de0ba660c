package emod

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodStatusRoute  godoc
// @Summary EMOD Status Route
// @Description API to give status of ongoing emod applications
// @ID HandleEmodStatusRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Param segment_details query string false "Segment Details"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/status [GET].
func HandleEmodStatusRoute(ctx *gin.Context) {
	request := modelsV1.EmodStatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindQuery(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodStatusRoute- error binding emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodStatusRoute- error validating emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodStatusRoute- request validated successfully")

	// process the request.
	response, err := emodStatusBusinessLogic.EmodActiveApplicationsStatus(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodStatusRoute- error processing emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
