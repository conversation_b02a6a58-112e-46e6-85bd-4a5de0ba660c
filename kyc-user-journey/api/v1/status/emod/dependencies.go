package emod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodStatusBusinessLogic interface {
	EmodActiveApplicationsStatus(ctx context.Context, request *modelsV1.EmodStatusAPIRequest) (*modelsV1.EmodStatusAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodStatusBusinessLogic EmodStatusBusinessLogic

func SetEmodStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodStatusBusinessLogic) {
	commonAPILogic = apiLogic
	emodStatusBusinessLogic = businessLogic
}
