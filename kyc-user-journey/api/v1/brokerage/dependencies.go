package brokerage

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPI "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type BrokerageBusinessLogic interface {
	GetBrokerageDetails(ctx context.Context, request *modelsAPI.BrokerageDetailsAPIRequest) (*modelsAPI.BrokerageDetailsAPIResponse, error)
	UpdateBrokeragePlan(ctx context.Context, request *modelsAPI.BrokeragePlanUpdateAPIRequest) error
	GetBrokeragePlanComparisonData(ctx context.Context, request *modelsAPI.BrokeragePlanCompareAPIRequest) (*modelsAPI.BrokeragePlanCompareAPIResponse, error)
	GetBrokeragePlanDetailsComparisonData(request *modelsAPI.BrokeragePlanDetailsCompareAPIRequest) (*modelsAPI.BrokeragePlanDetailsCompareAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var brokerageBusinessLogic BrokerageBusinessLogic
var commonAPILogic CommonAPILogic

func SetBrokerageBusinessLogic(apiLogic CommonAPILogic, businessLogic BrokerageBusinessLogic) {
	brokerageBusinessLogic = businessLogic
	commonAPILogic = apiLogic
}
