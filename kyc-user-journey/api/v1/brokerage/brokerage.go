package brokerage

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingBrokerageDetailsRoute godoc
// @Summary BrokerageDetails Route
// @Description API to get brokerage details in onboarding flow
// @ID HandleOnboardingBrokerageDetailsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/brokerage-details [GET].
func HandleOnboardingBrokerageDetailsRoute(ctx *gin.Context) {
	handleGenericBrokerageDetailsRoute(ctx)
}

// HandleNRIOnboardingBrokerageDetailsRoute godoc
// @Summary BrokerageDetails Route
// @Description API to get brokerage details in NRI onboarding flow
// @ID HandleNRIOnboardingBrokerageDetailsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/brokerage-details [GET].
func HandleNRIOnboardingBrokerageDetailsRoute(ctx *gin.Context) {
	handleGenericBrokerageDetailsRoute(ctx)
}

// HandleOnboardingBrokeragePlanCompareRoute godoc
// @Summary BrokeragePlanCompare Route
// @Description API to get brokerage plan comparison details in onboarding flow
// @ID HandleOnboardingBrokeragePlanCompareRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router  /v1/onboarding/brokerage/plan/compare [POST].
func HandleOnboardingBrokeragePlanCompareRoute(ctx *gin.Context) {
	handleGenericBrokeragePlanCompareRoute(ctx)
}

// HandleOnboardingBrokeragePlanDetailsCompareRoute godoc
// @Summary Brokerage plan details compare Route
// @Description API to get brokerage plan details comparison in onboarding flow
// @ID HandleOnboardingBrokeragePlanDetailsCompareRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router  /v1/onboarding/brokerage/plan/details/compare [POST].
func HandleOnboardingBrokeragePlanDetailsCompareRoute(ctx *gin.Context) {
	handleGenericBrokeragePlanDetailsCompareRoute(ctx)
}

func handleGenericBrokeragePlanDetailsCompareRoute(ctx *gin.Context) {
	request := modelsV1.BrokeragePlanDetailsCompareAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating brokerage plan details compare request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := brokerageBusinessLogic.GetBrokeragePlanDetailsComparisonData(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing brokerage plan details compare request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleGenericBrokerageDetailsRoute(ctx *gin.Context) {
	request := modelsV1.BrokerageDetailsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating brokerage details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := brokerageBusinessLogic.GetBrokerageDetails(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing brokerage details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleGenericBrokeragePlanCompareRoute(ctx *gin.Context) {
	request := modelsV1.BrokeragePlanCompareAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating brokerage plan compare request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := brokerageBusinessLogic.GetBrokeragePlanComparisonData(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing brokerage plan compare request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingBrokeragePlanUpdateRoute godoc
// @Summary update brokerage plan Route
// @Description API to update brokerage plan in onboarding flow
// @ID HandleOnboardingBrokeragePlanUpdateRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/brokerage/plan [PUT].
func HandleOnboardingBrokeragePlanUpdateRoute(ctx *gin.Context) {
	handleGenericBrokeragePlanUpdateRoute(ctx)
}

func handleGenericBrokeragePlanUpdateRoute(ctx *gin.Context) {
	request := modelsV1.BrokeragePlanUpdateAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating brokerage details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = brokerageBusinessLogic.UpdateBrokeragePlan(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing brokerage details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
