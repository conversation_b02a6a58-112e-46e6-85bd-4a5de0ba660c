package rpd

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingCreateRPDDeepLinkRoute godoc
// @Summary Onboarding create rpd deep link route
// @Description API to create rpd deep link
// @ID HandleOnboardingCreateRPDDeepLinkRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/rpd/create-deeplink [post].
func HandleOnboardingCreateRPDDeepLinkRoute(ctx *gin.Context) {
	handleGenericCreateRPDDeepLinkRoute(ctx)
}

func handleGenericCreateRPDDeepLinkRoute(ctx *gin.Context) {
	request := modelsV1.CreateRPDDeepLinkAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating rpd create deep link request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := rpdBusinessLogic.CreateDeepLink(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing create rpd deep link request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingRPDStatusRoute godoc
// @Summary Onboarding get rpd status route
// @Description API to get rpd status
// @ID HandleOnboardingRPDStatusRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/rpd/status [get].
func HandleOnboardingRPDStatusRoute(ctx *gin.Context) {
	handleGenericRPDStatusRoute(ctx)
}

func handleGenericRPDStatusRoute(ctx *gin.Context) {
	request := modelsV1.RPDStatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindQuery(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating rpd status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := rpdBusinessLogic.GetStatus(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing rpd status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
