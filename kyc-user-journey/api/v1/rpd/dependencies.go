package rpd

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type RPDBusinessLogic interface {
	GetStatus(ctx context.Context, request *modelsAPIv1.RPDStatusAPIRequest) (*modelsAPIv1.GetRPDStatusAPIResponse, error)
	CreateDeepLink(ctx context.Context, request *modelsAPIv1.CreateRPDDeepLinkAPIRequest) (*modelsAPIv1.CreateRPDDeepLinkAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var rpdBusinessLogic RPDBusinessLogic

func SetRPDBusinessLogic(apiLogic CommonAPILogic, businessLogic RPDBusinessLogic) {
	commonAPILogic = apiLogic
	rpdBusinessLogic = businessLogic
}
