package login

import (
	"net/http"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleAgentLogin(ctx *gin.Context) {
	var request modelsApiV1.AdminUserLoginRequest
	request.EmployeeID = ctx.GetString(constants.EmployeeID)

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("HandleAgentLogin: invalid agent login request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// User Request Validation from DB.
	result, err := loginBusinessLogic.AdminPanelLogin(ctx, request)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("HandleAgentLogin: error validating user for agent portal login")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, config.SSO().RedirectURL[constants.AdminPanel]+result.Token)
}
