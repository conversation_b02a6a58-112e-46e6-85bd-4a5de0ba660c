package login

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type LoginBusinessLogic interface {
	AdminPanelLogin(ctx context.Context, request modelsApiV1.AdminUserLoginRequest) (*modelsApiV1.AdminUserLoginResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var loginBusinessLogic LoginBusinessLogic

func SetLoginBusinessLogic(businessLogic LoginBusinessLogic) {
	loginBusinessLogic = businessLogic
}
