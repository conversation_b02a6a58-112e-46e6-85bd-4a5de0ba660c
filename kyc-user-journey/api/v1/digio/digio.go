package digio

import (
	"context"
	"net/http"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionInitiateDigioRoute godoc
// @Summary Correction Initiate Digio Route
// @Description API to post initiation of digio
// @ID HandleCorrectionInitiateDigioRoute
// @Tags digio
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/digio/initiate [post].
func HandleCorrectionInitiateDigioRoute(ctx *gin.Context) {
	handleGenericInitiateDigioRoute(ctx)
}

// HandleEmodDigioInitiateRoute godoc
// @Summary Emod Initiate Digio Route
// @Description API to post initiation of digio
// @ID HandleEmodDigioInitiateRoute
// @Tags digio
// @Accept json
// @Produce json
// @Param X-source header string true "spark-emod"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/digio/initiate [post].
func HandleEmodDigioInitiateRoute(ctx *gin.Context) {
	handleEmodInitiateDigioRoute(ctx)
}

// HandleOnboardingInitiateDigioRoute godoc
// @Summary Onboarding Initiate Digio Route
// @Description API to post initiation of digio
// @ID HandleOnboardingInitiateDigioRoute
// @Tags digio
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/digio/initiate [post].

type digioSelectionFunction func(ctx context.Context, request *modelsV1.InitiateDigioRequest) (*modelsV1.InitiateDigioResponse, error)

func HandleOnboardingInitiateDigioRoute(ctx *gin.Context) {
	handleOnboardingInitiateDigioRoute(ctx)
}

// HandleNRIOnboardingInitiateDigioRoute godoc
// @Summary NRI Onboarding Initiate Digio Route
// @Description API to post initiation of digio for nri
// @ID HandleNRIOnboardingInitiateDigioRoute
// @Tags digio
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/nri/onboarding/digio/initiate [post].
func HandleNRIOnboardingInitiateDigioRoute(ctx *gin.Context) {
	handleNRIInitiateDigioRoute(ctx)
}

func handleOnboardingInitiateDigioRoute(ctx *gin.Context) {
	request := modelsV1.InitiateDigioRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := fetchDigioIntiateFunctionForOnboarding(request.Meta.KycType)(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleNRIInitiateDigioRoute(ctx *gin.Context) {
	request := modelsV1.InitiateNRIDigioRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := nriOnboardingDigioBusinessLogic.InitiateDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingRedirectDigioRoute @Router /v1/onboarding/digio/redirect [POST].
func HandleOnboardingRedirectDigioRoute(ctx *gin.Context) {
	handleOnboardingRedirectRequest(ctx)
}

func handleOnboardingRedirectRequest(ctx *gin.Context) {
	var request modelsV1.DigioRedirectAPIRequest
	parseQueryParams(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := onboardingDigioBusinessLogic.RedirectionFromOnboardingDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, response.RedirectURL)
}

// HandlNRIRedirectDigioRoute godoc
// @Summary Handle Digio callback and redirect the user
// @Message Handle Digio callback and redirect the user
// @ID HandlNRIRedirectDigioRoute
// @Tags nri
// @Param requestData query string true "encrypted request data sent to digio in the redirect URl"
// @Param message query string true "message from digio"
// @Param status query string true "status from digio"
// @Param digio_doc_id query string true "digio doc id (starting with KID)"
// @Success 301
// @Failure 301
// @Router /v1/nri/onboarding/digio/redirect [GET].
func HandlNRIRedirectDigioRoute(ctx *gin.Context) {
	handleNRIRedirectRequest(ctx)
}

func handleNRIRedirectRequest(ctx *gin.Context) {
	var request modelsV1.DigioRedirectAPIRequest
	parseQueryParams(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := nriOnboardingDigioBusinessLogic.RedirectionFromDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, response.RedirectURL)
}

// HandleReKYCInitiateDigioRoute godoc
// @Summary ReKYC Initiate Digio Route
// @Description API to post initiation of digio
// @ID HandleReKYCInitiateDigioRoute
// @Tags rekyc
// @Accept json
// @Produce json
// @Param X-source header string true "spark-emod"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/rekyc/digio/initiate [post].
func HandleReKYCInitiateDigioRoute(ctx *gin.Context) {
	handleEmodInitiateDigioRoute(ctx)
}

func handleGenericInitiateDigioRoute(ctx *gin.Context) {
	request := modelsV1.InitiateDigioRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := digioBusinessLogic.InitiateDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleEmodInitiateDigioRoute(ctx *gin.Context) {
	request := modelsV1.EmodInitiateDigioRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindQuery(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error binding query params to emod initiate digio request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := emodDigioBusinessLogic.InitiateEmodDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleCorrectionRedirectFromDigio godoc
// @Summary Handle Digio callback and redirect the user
// @Message Handle Digio callback and redirect the user
// @ID HandleCorrectionRedirectFromDigio
// @Tags kyc
// @Param requestData query string true "encrypted request data sent to digio in the redirect URl"
// @Param message query string true "message from digio"
// @Param status query string true "status from digio"
// @Param digio_doc_id query string true "digio doc id (starting with KID)"
// @Success 301
// @Failure 301
// @Router /v1/correction/digio/redirect [get].
func HandleCorrectionRedirectFromDigio(ctx *gin.Context) {
	handleGenericRedirectFromDigio(ctx)
}

// HandleReKYCRedirectFromDigio godoc
// @Summary Handle Digio callback and redirect the user
// @Message Handle Digio callback and redirect the user
// @ID HandleReKYCRedirectFromDigio
// @Tags rekyc
// @Param requestData query string true "encrypted request data sent to digio in the redirect URl"
// @Param message query string true "message from digio"
// @Param status query string true "status from digio"
// @Param digio_doc_id query string true "digio doc id (starting with KID)"
// @Success 301
// @Failure 301
// @Router /v1/rekyc/digio/redirect [get].
func HandleReKYCRedirectFromDigio(ctx *gin.Context) {
	handleEmodRedirectFromDigio(ctx)
}

func handleGenericRedirectFromDigio(ctx *gin.Context) {
	var request modelsV1.DigioRedirectAPIRequest
	parseQueryParams(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := digioBusinessLogic.RedirectionFromDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, response.RedirectURL)
}

// HandleEmodDigioRedirectRoute godoc
// @Summary Emod Redirect Digio Route
// @Description API to post initiation of digio
// @ID HandleEmodDigioRedirectRoute
// @Tags digio
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/digio/redirect [POST].
func HandleEmodDigioRedirectRoute(ctx *gin.Context) {
	handleEmodRedirectFromDigio(ctx)
}

func handleEmodRedirectFromDigio(ctx *gin.Context) {
	var request modelsV1.EmodDigioRedirectAPIRequest
	parseQueryParamsEmod(ctx, &request)
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := emodDigioBusinessLogic.RedirectionFromEmodDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio redirect request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusMovedPermanently, response.RedirectURL)
}

// HandleNRIDigioSkipRoute godoc
// @Summary NRI Digio Opt Out Route
// @Description API to opt out of digio
// @ID HandleNRIDigioSkipRoute
// @Tags nri
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/nri/onboarding/digio/consent [post].
func HandleNRIDigioSkipRoute(ctx *gin.Context) {
	handlegenericDigioSkipRoute(ctx)
}

func handlegenericDigioSkipRoute(ctx *gin.Context) {
	request := modelsV1.DigioSkipRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digio skip request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = nriOnboardingDigioBusinessLogic.SkipDigio(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing digio initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func parseQueryParams(ctx *gin.Context, request *modelsV1.DigioRedirectAPIRequest) {
	request.DigioDocID = ctx.Query(constants.DigioDocIDQueryParamKey)
	request.Message = ctx.Query(constants.DigioMessageParamKey)
	request.Status = ctx.Query(constants.DigioStatusParamKey)
	request.RequestData = ctx.Query(constants.DigioRequestDataParamKey)
	request.Source = ctx.Query(constants.DigioSourceQueryParamKey)
	request.Intent = ctx.Query(constants.DigioIntentQueryParamKey)
}

func parseQueryParamsEmod(ctx *gin.Context, request *modelsV1.EmodDigioRedirectAPIRequest) {
	request.DigioDocID = ctx.Query(constants.DigioDocIDQueryParamKey)
	request.Message = ctx.Query(constants.DigioMessageParamKey)
	request.Status = ctx.Query(constants.DigioStatusParamKey)
	request.RequestData = ctx.Query(constants.DigioRequestDataParamKey)
	request.Source = ctx.Query(constants.DigioSourceQueryParamKey)
	request.Intent = ctx.Query(constants.DigioIntentQueryParamKey)
}

func fetchDigioIntiateFunctionForOnboarding(key string) digioSelectionFunction {
	LeadfunctionMap := map[string]digioSelectionFunction{
		constants.Default:            onboardingDigioBusinessLogic.InitiateOnboardingDigio,
		constants.PhysicalMFToEquity: onboardingDigioBusinessLogic.InitiateOnboardingDigioForPanAlredyExistInSystem,
	}
	if function, exists := LeadfunctionMap[key]; exists {
		return function
	} else {
		return LeadfunctionMap[constants.Default]
	}
}
