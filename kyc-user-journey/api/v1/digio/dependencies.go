package digio

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type DigioBusinessLogic interface {
	InitiateDigio(ctx context.Context, request *modelsV1.InitiateDigioRequest) (*modelsV1.InitiateDigioResponse, error)
	RedirectionFromDigio(ctx context.Context, request *modelsV1.DigioRedirectAPIRequest) (*modelsV1.DigioRedirectResponse, error)
}

type EmodDigioBusinessLogic interface {
	InitiateEmodDigio(ctx context.Context, request *modelsV1.EmodInitiateDigioRequest) (*modelsV1.InitiateDigioResponse, error)
	RedirectionFromEmodDigio(ctx context.Context, request *modelsV1.EmodDigioRedirectAPIRequest) (*modelsV1.DigioRedirectResponse, error)
}

type OnboardingDigioBusinessLogic interface {
	InitiateOnboardingDigio(ctx context.Context, request *modelsV1.InitiateDigioRequest) (*modelsV1.InitiateDigioResponse, error)
	RedirectionFromOnboardingDigio(ctx context.Context, request *modelsV1.DigioRedirectAPIRequest) (*modelsV1.DigioRedirectResponse, error)
	InitiateOnboardingDigioForPanAlredyExistInSystem(ctx context.Context, request *modelsV1.InitiateDigioRequest) (*modelsV1.InitiateDigioResponse, error)
}

type NRIDigioBusinessLogic interface {
	InitiateDigio(ctx context.Context, request *modelsV1.InitiateNRIDigioRequest) (*modelsV1.InitiateDigioResponse, error)
	RedirectionFromDigio(ctx context.Context, request *modelsV1.DigioRedirectAPIRequest) (*modelsV1.DigioRedirectResponse, error)
	SkipDigio(ctx context.Context, request *modelsV1.DigioSkipRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var digioBusinessLogic DigioBusinessLogic
var emodDigioBusinessLogic EmodDigioBusinessLogic
var onboardingDigioBusinessLogic OnboardingDigioBusinessLogic
var nriOnboardingDigioBusinessLogic NRIDigioBusinessLogic

func SetDigioBusinessLogic(apiLogic CommonAPILogic, businessLogic DigioBusinessLogic) {
	commonAPILogic = apiLogic
	digioBusinessLogic = businessLogic
}

func SetEmodDigioBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodDigioBusinessLogic) {
	commonAPILogic = apiLogic
	emodDigioBusinessLogic = businessLogic
}

func SetOnboardingDigioBusinessLogic(apiLogic CommonAPILogic, businessLogic OnboardingDigioBusinessLogic) {
	commonAPILogic = apiLogic
	onboardingDigioBusinessLogic = businessLogic
}

func SetNRIOnboardingDigioBusinessLogic(apiLogic CommonAPILogic, businessLogic NRIDigioBusinessLogic) {
	commonAPILogic = apiLogic
	nriOnboardingDigioBusinessLogic = businessLogic
}
