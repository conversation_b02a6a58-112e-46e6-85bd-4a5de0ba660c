package country

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CountrySearchBusinessLogic interface {
	CountrySearch(ctx context.Context, countrySearchAPIRequest modelsV1.CountrySearchAPIRequest) (*modelsV1.CountryAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var countrySearchBusinessLogic CountrySearchBusinessLogic

func SetCountrySearchBusinessLogic(businessLogic CountrySearchBusinessLogic) {
	countrySearchBusinessLogic = businessLogic
}
