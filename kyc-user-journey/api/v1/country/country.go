package country

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCountrySearch godoc
// @Summary Handle Country List
// @Message Handle Country List
// @ID HandleCountrySearch
// @Tags search
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-source header string true "source"
// @Param Authorization header string true "Bearer ${JWT}"
// @Param search query string false "search string"
// @Param from query string false "from string"
// @Param size query string false "size string"
// @Success 200 {object} models.CountryAPIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/country/list [get].
func HandleCountrySearch(ctx *gin.Context) {
	searchQueryParam := ctx.Query(constants.SearchQuery)
	from := ctx.Query(constants.FromQuery)
	size := ctx.Query(constants.SizeQuery)

	var countrySearchAPIRequest modelsV1.CountrySearchAPIRequest
	err := countrySearchAPIRequest.Create(from, size, searchQueryParam)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleCountrySearch -Error converting query param to int in country search API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := countrySearchBusinessLogic.CountrySearch(ctx, countrySearchAPIRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCountrySearch - error searching for country")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
