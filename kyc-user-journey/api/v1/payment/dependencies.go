package payment

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PaymentBusinessLogic interface {
	DdpiPayment(ctx context.Context, req *modelsV1.PaymentInitiateRequest) (*modelsV1.DdpiPaymentResponse, error)
	CheckStatus(ctx context.Context, req *modelsV1.PaymentStatusRequest) (*modelsV1.PaymentStatusResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var paymentBusinessLogic PaymentBusinessLogic

func SetEmodDdpiPaymentBusinessLogic(apiLogic CommonAPILogic, businessLogic PaymentBusinessLogic) {
	commonAPILogic = apiLogic
	paymentBusinessLogic = businessLogic
}
