package payment

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleDdpiPayment  godoc
// @Summary EMOD DDPI Payment Route
// @Description API to initiate payment with the pg team
// @ID HandleDdpiPayment
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/ddpi/pay [POST].
func HandleDdpiPayment(ctx *gin.Context) {
	request := modelsV1.PaymentInitiateRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleDdpiPayment: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating emod ddpi payment request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleDdpiPayment- request validated successfully")

	// process the request.
	response, err := paymentBusinessLogic.DdpiPayment(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing payment request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetCreatedSuccessResponse(response))
}

// HandleTransactionStatus  godoc
// @Summary DDPI Payment Status Route
// @Description API to return status of the payment from the pg team
// @ID HandleTransactionStatus
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/transaction-status [GET].
func HandleTransactionStatus(ctx *gin.Context) {
	appNumber := ctx.Query(constants.AppNumberParamKey)
	request := modelsV1.PaymentStatusRequest{
		Headers:       commonAPILogic.GetHeaders(ctx),
		Meta:          commonAPILogic.GetMeta(ctx),
		EmodAppNumber: appNumber,
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating payment status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleTransactionStatus- request validated successfully")

	// process the request.
	response, err := paymentBusinessLogic.CheckStatus(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing payment request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
