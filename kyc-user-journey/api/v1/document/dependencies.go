package document

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	documentModel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var documentBusinessLogic DocumentBusinessLogic

type DocumentBusinessLogic interface {
	UploadGenericDocument(ctx context.Context, req *documentModel.DocumentUploadRequest) error
	UploadMultipleDocuments(ctx context.Context, req *documentModel.MultipleDocumentUploadRequest, types, passwords []string) error
	UploadGenericNRIDocument(ctx context.Context, req *documentModel.NRIDocumentUploadRequest) error
	PreviewGenericNRIDocument(ctx context.Context, req *documentModel.NRIDocumentPreviewRequest) (*documentModel.NRIDocumentPreviewResponse, error)
	UploadMultiplePhysicalDocuments(ctx context.Context, req *documentModel.PhysicalDocumentUploadRequest, passwords []string) error
}

func SetDocumentBusinessLogic(apiLogic CommonAPILogic,
	businessLogic DocumentBusinessLogic) {
	commonAPILogic = apiLogic
	documentBusinessLogic = businessLogic
}
