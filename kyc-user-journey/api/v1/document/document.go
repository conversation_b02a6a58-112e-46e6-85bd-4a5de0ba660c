package document

import (
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	documentModel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAccountTransferDocumentUpload godoc
// @Summary Upload Any Generic Document
// @Description API to upload Generic proof for any applicable journey
// @ID HandleGenericDocumentUpload
// @Tags genericDocumentUpload
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param FormData ["File"]
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/acc-transfer/document/upload [POST].
func HandleAccountTransferDocumentUpload(ctx *gin.Context) {
	handleGenericDocumentUpload(ctx)
}

// HandleIncomeProofDocumentUpload godoc
// @Summary Upload Any Generic Document
// @Description API to upload income proof for any applicable journey
// @ID HandleIncomeProofDocumentUpload
// @Tags genericDocumentUpload
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param FormData ["File"]
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/income-proof/document/upload [POST].
func HandleIncomeProofDocumentUpload(ctx *gin.Context) {
	handleMultipleDocumentUpload(ctx)
}

// HandleNRIDocumentUpload
// @Summary Upload Any Generic Document for NRI
// @Description API to upload proof for NRI journey
// @ID HandleNRIDocumentUpload
// @Tags nriDocumentUpload
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param FormData ["File"]
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/upload [POST].
func HandleNRIDocumentUpload(ctx *gin.Context) {
	handleGenericNRIDocumentUpload(ctx)
}

// HandleNRIDocumentPreview
// @Summary Preview Generic Document for NRI
// @Description API to preview proof for NRI journey
// @ID HandleNRIDocumentPreview
// @Tags nriDocumentPreview
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param FormData ["File"]
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/preview [GET].
func HandleNRIDocumentPreview(ctx *gin.Context) {
	handleGenericNRIDocumentPreview(ctx)
}

// HandlePhysicalDocumentUpload godoc
// @Summary Upload Any Physical Document
// @Description API to upload physical document proof for any applicable journey
// @ID HandlePhysicalDocumentUpload
// @Tags physicalDocumentUpload
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param FormData ["File"]
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/physical/upload [POST].
func HandlePhysicalDocumentUpload(ctx *gin.Context) {
	handleGenericPhysicalDocumentUpload(ctx)
}

func handleGenericDocumentUpload(ctx *gin.Context) {
	request := documentModel.DocumentUploadRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGenericDocumentUpload: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// bind file.
	request.Data.File, err = ctx.FormFile("file")
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGenericDocumentUpload: error binding file")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingDocumentProof.WithDetails(err.Error())))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleGenericDocumentUpload: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = documentBusinessLogic.UploadGenericDocument(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleGenericDocumentUpload: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func handleGenericNRIDocumentPreview(ctx *gin.Context) {
	request := documentModel.NRIDocumentPreviewRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericNRIDocumentPreview: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNRIDocumentPreview: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	response, err := documentBusinessLogic.PreviewGenericNRIDocument(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNRIDocumentPreview: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, &response))
}

func handleGenericNRIDocumentUpload(ctx *gin.Context) {
	request := documentModel.NRIDocumentUploadRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericNRIDocument: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// bind file.
	request.Data.File, err = ctx.FormFile("file")
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericNRIDocument: error binding file")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingDocumentProof.WithDetails(err.Error())))
		return
	}
	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("UploadGenericNRIDocument: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = documentBusinessLogic.UploadGenericNRIDocument(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("UploadGenericNRIDocument: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func handleMultipleDocumentUpload(ctx *gin.Context) {
	request := documentModel.MultipleDocumentUploadRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleMultipleDocumentUpload: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	types := strings.Split(request.Data.Type, ",")
	passwords := strings.Split(request.Data.Password, ",")

	// validate the request.
	err = request.Validate(types)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleMultipleDocumentUpload: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = documentBusinessLogic.UploadMultipleDocuments(ctx, &request, types, passwords)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleMultipleDocumentUpload: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func handleGenericPhysicalDocumentUpload(ctx *gin.Context) {
	request := documentModel.PhysicalDocumentUploadRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handlePhysicalDocumentUpload: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	passwords := strings.Split(request.Data.Password, ",")

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handlePhysicalDocumentUpload: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = documentBusinessLogic.UploadMultiplePhysicalDocuments(ctx, &request, passwords)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handlePhysicalDocumentUpload: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
