package nriscrutiny

import (
	"fmt"
	"net/http"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNriSubmitBeforeEsign godoc
// @Summary Submit Nri application for Scrutiny One Route
// @Description API to submit Nri application for Scrutiny One Route
// @ID HandleNriSubmitBeforeEsign
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/nri/onboarding/submitBeforeEsign [POST].
func HandleNriSubmitBeforeEsign(ctx *gin.Context) {
	request := modelsV1.NriSubmitAppForBeforeEsignAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriSubmitBeforeEsign: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = nriScrutinyBusinessLogic.ValidateDataAndUpdateScrutinyStatus(ctx, request.AppNumber, request.Mobile, request.Meta.KycJourneyType, request.Source)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriSubmitBeforeEsign Error while updateing scrutiny status")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleNriUnSignedEsignDownload godoc
// @Summary Download unsigned esign for NRI
// @Description Download unsigned esign for NRI
// @ID HandleNriUnSignedEsignDownload
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/nri/onboarding/unsigned-esign-download/ [POST].
func HandleNriUnSignedEsignDownload(ctx *gin.Context) {
	request := modelsV1.NriSubmitAppForBeforeEsignAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriUnSignedEsignDownload: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := nriScrutinyBusinessLogic.DownloadNriUnSignedEsign(ctx, request.AppNumber, request.Mobile, request.Meta.KycJourneyType)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriUnSignedEsignDownload: error while downloading pdf from s3")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// Set the headers to indicate a file attachment
	filename := fmt.Sprintf("%s.pdf", request.AppNumber)
	ctx.Header("Content-Type", "application/pdf")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	ctx.Data(http.StatusOK, "application/pdf", response)
}

// HandleNriUploadAttestedForm  godoc
// @Summary Upload Attested Form
// @Description Uploads attested Form and move application to scrutiny_1_approved to scrutiny_2
// @ID HandleNriUploadAttestedForm
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Form document form
// @Form type form
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/nri/onboarding/upload-attested-form [POST].
func HandleNriUploadAttestedForm(ctx *gin.Context) {
	var request modelsV1.UploadAttestedFormRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriUploadAttestedForm: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	request.File, err = ctx.FormFile(constants.NriAttestedForm)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriUploadAttestedForm: error binding file")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriUploadAttestedForm: error while validating additional documents request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = nriScrutinyBusinessLogic.UploadAdditionalAttestedForm(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriUploadAttestedForm Error uploading additional document")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
