package nriscrutiny

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type NriScrutinyBusinessLogic interface {
	ValidateDataAndUpdateScrutinyStatus(ctx context.Context, appNumber, mobile, kycJourneyType, source string) error
	DownloadNriUnSignedEsign(ctx *gin.Context, appNumber, mobile, kycJourneyType string) ([]byte, error)
	UploadAdditionalAttestedForm(ctx context.Context, request *modelsV1.UploadAttestedFormRequest) error
}

var commonAPILogic CommonAPILogic
var nriScrutinyBusinessLogic NriScrutinyBusinessLogic

func SetNriScrutinyBusinessLogic(apiLogic CommonAPILogic, businessLogic NriScrutinyBusinessLogic) {
	commonAPILogic = apiLogic
	nriScrutinyBusinessLogic = businessLogic
}
