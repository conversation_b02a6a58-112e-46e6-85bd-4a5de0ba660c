package loan

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleLoanDetails godoc
// @Summary loan details Route
// @Description API to Fetch occupation, income and pan using client code
// @ID HandleLoanDetails
// @Tags loan
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/kyc/external/loan/details [get].
func HandleLoanDetails(ctx *gin.Context) {
	request := modelsAPIV1.LoanDetailsRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.Data.ClientCode = ctx.GetString(constants.ClientCodeContextKey)

	err := request.Validate() // now process the request.
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating loan details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := loanDetailsBusinessLogic.GetLoanDetails(ctx, request.Data.ClientCode)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting loan details response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
