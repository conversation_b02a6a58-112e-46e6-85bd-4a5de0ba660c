package loan

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type LoanDetailsBusinessLogic interface {
	GetLoanDetails(ctx context.Context, clientCode string) (*modelsAPIV1.LoanDetailsResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var loanDetailsBusinessLogic LoanDetailsBusinessLogic

func SetLoanDetailsBusinessLogic(apiLogic CommonAPILogic, businessLogic LoanDetailsBusinessLogic) {
	commonAPILogic = apiLogic
	loanDetailsBusinessLogic = businessLogic
}
