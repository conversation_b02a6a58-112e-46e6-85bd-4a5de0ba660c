package signature

import (
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionSignatureRoute godoc
// @Summary Correction Signature Route
// @Description API to post signature of Correction Journey
// @ID HandleCorrectionSignatureRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/signature [POST].
func HandleCorrectionSignatureRoute(ctx *gin.Context) {
	handleGenericSignatureRoute(ctx, false)
}

// HandleOnboardingSignatureRoute godoc
// @Summary Onboarding Signature Route
// @Description API to post signature of Onboarding Journey
// @ID HandleOnboardingSignatureRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/signature [POST].
func HandleOnboardingSignatureRoute(ctx *gin.Context) {
	skipValidation := true
	if ctx.GetString(constants.KycJourneyTypeContextKey) == constants.KycJourneyTypeRejectionDIY {
		skipValidation = false
	}
	handleGenericSignatureRoute(ctx, skipValidation)
}

// HandleEmodCorrectionSignatureRoute godoc
// @Summary Emod Correction Signature Route
// @Description API to post signature of Emod correction journey
// @ID HandleEmodCorrectionSignatureRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/correction/signature [POST].
func HandleEmodCorrectionSignatureRoute(ctx *gin.Context) {
	handleGenericSignatureRoute(ctx, false)
}

func handleGenericSignatureRoute(ctx *gin.Context, skipValidation bool) {
	request := modelsV1.SignatureAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	var err = parseSignatureRequestData(ctx, &request)
	if err != nil {
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction signature request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionSignatureRoute - request validated successfully")
	var response *modelsV1.SignatureAPIResponse
	if skipValidation {
		// Onboarding.
		appNumber := ctx.GetString(constants.AppNumberContextKey)
		if percentagerollout.IsFeatureRolloutEnabled(ctx, appNumber, constants.ApplicationRolloutSignatureRejectionConfigKey, config.Feature().Enable.SignatureRejection, false) &&
			!strings.HasPrefix(appNumber, "NR") {
			response, err = signatureBusinessLogic.OnboardingWithValidation(ctx, &request)
		} else {
			response, err = signatureBusinessLogic.SignatureWithoutValidation(ctx, &request)
		}
	} else if request.Meta.KycJourneyType == constants.KycJourneyTypeRejectionDIY {
		response, err = signatureBusinessLogic.SignatureWithValidation(ctx, &request)
	} else {
		response, err = signatureBusinessLogic.Signature(ctx, &request)
	}
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting correction signature response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func parseSignatureRequestData(ctx *gin.Context, request *modelsV1.SignatureAPIRequest) error {
	var err error
	request.Data.SignatureImageFile, err = ctx.FormFile(constants.SignatureFormKey)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error getting the signature file")
		ctx.JSON(models.GetErrorResponse(constants.ErrSignatureFileNotFound.WithDetails(err.Error())))
		return err
	}
	return nil
}

// HandleGetSignatureDocumentKyc1 godoc
// @Summary Returns Signature Document
// @Message Handle GetSignature Document Kyc1
// @ID HandleGetSignatureDocumentKyc1
// @Tags kyc
// @Accept mpfd
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-requestId header string false "request id"
// @Param X-source header string true "source"
// @Param X-platform header string true "source"
// @Param AppVersion header string true "AppVersion"
// @Param Authorization header string true "Bearer ${JWT}"
// @Success 200 {object} models.SignatureAPIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /v1/internal/kyc/signature/document/azure [GET].
func HandleGetSignatureDocumentAzure(ctx *gin.Context) {
	request := modelsV1.ESignPdfRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
	}

	userID := ctx.Query(constants.ClientCodeContextKey)
	ctx.Set(constants.CorrelationIDContextKey, ctx.GetString(constants.ClientCodeContextKey))

	var err error

	// now validate the request
	err = request.Validate(userID)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error validating the get signature doc from azure request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// now process the request
	response, err := signatureBusinessLogic.GetSignatureDocFromAzure(ctx, userID)

	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing get signature doc from azure request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
