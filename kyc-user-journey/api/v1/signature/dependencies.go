package signature

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type SignatureBusinessLogic interface {
	SignatureWithoutValidation(ctx context.Context, request *modelsV1.SignatureAPIRequest) (*modelsV1.SignatureAPIResponse, error)
	OnboardingWithValidation(ctx context.Context, request *modelsV1.SignatureAPIRequest) (*modelsV1.SignatureAPIResponse, error)
	Signature(ctx context.Context, request *modelsV1.SignatureAPIRequest) (*modelsV1.SignatureAPIResponse, error)
	SignatureWithValidation(ctx context.Context, request *modelsV1.SignatureAPIRequest) (*modelsV1.SignatureAPIResponse, error)
	GetSignatureDocFromAzure(ctx context.Context, userID string) (*modelsV1.SignatureAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var signatureBusinessLogic SignatureBusinessLogic

func SetSignatureBusinessLogic(apiLogic CommonAPILogic, businessLogic SignatureBusinessLogic) {
	commonAPILogic = apiLogic
	signatureBusinessLogic = businessLogic
}
