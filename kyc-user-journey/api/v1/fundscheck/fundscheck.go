package fundscheck

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleFundsCheck godoc
// @Summary FundsCheck Route
// @ID HandleFundsCheck
// @Tags userDetails
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/physical/funds [get].
func HandleFundsCheck(ctx *gin.Context) {
	request := api.FundsCheckAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("handleFundsCheck: attempt")

	// validate the request
	err := request.Validate() // now process the request.
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating funds check request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleFundsCheck - request validated successfully")

	// process request
	response, err := fundsCheckBusinessLogic.ProcessFundsCheckRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing get profile details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
