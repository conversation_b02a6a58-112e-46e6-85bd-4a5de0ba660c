package fundscheck

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type FundsCheckBusinessLogic interface {
	ProcessFundsCheckRequest(ctx context.Context, request *api.FundsCheckAPIRequest) (*api.FundsCheckAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var fundsCheckBusinessLogic FundsCheckBusinessLogic

func SetFundsCheckBusinessLogic(apiLogic CommonAPILogic, businessLogic FundsCheckBusinessLogic) {
	commonAPILogic = apiLogic
	fundsCheckBusinessLogic = businessLogic
}
