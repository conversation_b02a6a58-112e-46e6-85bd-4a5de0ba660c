package mobile

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type MobileBusinessLogic interface {
	GetMobiles(ctx context.Context, req *modelsAPIV1.GetMobilesRequest) (*modelsAPIV1.GetMobilesResponse, error)
	GetAppNumber(ctx context.Context, req *modelsAPIV1.GetAppNumberRequest) (*modelsAPIV1.GetAppNumberResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var mobileBusinessLogic MobileBusinessLogic

func SetMobileBusinessLogic(apiLogic CommonAPILogic, businessLogic MobileBusinessLogic) {
	commonAPILogic = apiLogic
	mobileBusinessLogic = businessLogic
}
