package mobile

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleGetMobileForAppNumbers  godoc
// @Summary Send OTP to Mobile Route
// @Description Creates OTP and sends to mobile
// @ID HandleGetMobileForAppNumbers
// @Tags mobileVerification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /internal/v1/client-details [POST].
func HandleGetMobileForAppNumbers(ctx *gin.Context) {
	req := modelsV1.GetMobilesRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGetMobileForAppNumbers: error binding GetMobile request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = req.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleGetMobileForAppNumbers: error validating GetMobile request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := mobileBusinessLogic.GetMobiles(ctx, &req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGetMobileForAppNumbers: error processing GetMobile request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleGetAppNumberForMobile  godoc
// @Summary Gets AppNumber to Mobile Route
// @Description Gets AppNumber for given mobile
// @ID HandleGetAppNumberForMobile
// @Tags mobile
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /internal/v1/app-number [POST].
func HandleGetAppNumberForMobile(ctx *gin.Context) {
	req := modelsV1.GetAppNumberRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGetAppNumberForMobile: error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = req.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleGetMobileForAppNumbers: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := mobileBusinessLogic.GetAppNumber(ctx, &req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleGetMobileForAppNumbers: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
