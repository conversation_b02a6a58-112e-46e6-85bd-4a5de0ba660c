package clientinfo

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleClientInfoRoute godoc
// @Summary Client info Route
// @Description API to Fetch client data.
// @ID HandleClientInfoRoute
// @Tags clientInfo
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/data/client-info [get].
func HandleClientInfoRoute(ctx *gin.Context) {
	request := modelsAPIV1.ClientInfoRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate() // now process the request.
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating client Info request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := clientInfoBusinessLogic.GetClientInfo(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting client Info response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
