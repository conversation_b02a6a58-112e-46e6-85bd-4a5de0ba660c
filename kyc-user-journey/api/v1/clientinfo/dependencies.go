package clientinfo

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPI "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type ClientInfoBusinessLogic interface {
	GetClientInfo(ctx context.Context, request *modelsAPI.ClientInfoRequest) (any, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var clientInfoBusinessLogic ClientInfoBusinessLogic

func SetClientInfoBusinessLogic(apiLogic CommonAPILogic, businessLogic ClientInfoBusinessLogic) {
	commonAPILogic = apiLogic
	clientInfoBusinessLogic = businessLogic
}
