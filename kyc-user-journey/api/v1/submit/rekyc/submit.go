package rekyc

import (
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleReKycSubmit godoc
// @Summary ReKyc Submit Route
// @Description API to Submit reKyc Journey
// @ID HandleReKycSubmit
// @Tags rekyc
// @Accept json
// @Produce json
// @Param X-source header string true "spark-emod"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/rekyc/submit [POST].
func HandleReKycSubmit(ctx *gin.Context) {
	request := &modelsV1.ReKycSubmitAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReKycSubmit - error binding rekyc Submit request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Str(constants.LogRequestMetaKey, fmt.Sprintf("%s, %s", request.Platform, request.AppVersion)).
		Msg("HandleReKycSubmit - Platform and App Version")

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReKycSubmit - error validating rekyc Submit request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Str(constants.LogRequestMetaKey, fmt.Sprintf("%v", request.Meta)).
		Msg("HandleReKycSubmit - request received successfully")
	response, err := reKycSubmitBusinessLogic.ReKycJourneySubmit(ctx, request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReKycSubmit - error processing rekyc submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
