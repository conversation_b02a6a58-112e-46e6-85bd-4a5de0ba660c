package rekyc

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type ReKycSubmitBusinessLogic interface {
	ReKycJourneySubmit(ctx context.Context, request *modelsV1.ReKycSubmitAPIRequest) (*modelsV1.ReKycSubmitAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var (
	commonAPILogic           CommonAPILogic
	reKycSubmitBusinessLogic ReKycSubmitBusinessLogic
)

func SetReKycSubmitBusinessLogic(apiLogic CommonAPILogic, businessLogic ReKycSubmitBusinessLogic) {
	commonAPILogic = apiLogic
	reKycSubmitBusinessLogic = businessLogic
}
