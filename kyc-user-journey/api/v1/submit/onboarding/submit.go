package onboarding

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleOnboardingSubmit(ctx *gin.Context) {
	request := modelsV1.OnboardingSubmitRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingSubmit: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = onboardingSubmitBusinessLogic.HandleOnboardingSubmit(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingSubmit: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
