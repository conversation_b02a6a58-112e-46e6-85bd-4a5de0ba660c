package onboarding

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type OnboardingSubmitBusinessLogic interface {
	HandleOnboardingSubmit(ctx context.Context, request *modelsApiV1.OnboardingSubmitRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var onboardingSubmitBusinessLogic OnboardingSubmitBusinessLogic
var commonAPILogic CommonAPILogic

func SetOnboardingSubmitBusinessLogic(businessLogic OnboardingSubmitBusinessLogic, apiLogic CommonAPILogic) {
	onboardingSubmitBusinessLogic = businessLogic
	commonAPILogic = apiLogic
}
