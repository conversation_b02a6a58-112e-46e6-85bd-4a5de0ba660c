package additionaldocuments

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type AdditionalDocumentsBusinessLogic interface {
	UploadAdditionalDocument(ctx context.Context, request *api.UploadAdditionalDocumentRequest) (*api.UploadAdditionalDocumentResponseData, error)
	PreviewAdditionalDocument(ctx context.Context, request *api.AdditionalDocumentPreviewRequest) (*api.AdditionalDocumentPreviewResponseData, error)
}

var commonAPILogic CommonAPILogic
var additionalDocumentsBusinessLogic AdditionalDocumentsBusinessLogic

func SetAdditionalDocumentBusinessLogic(commonLogic CommonAPILogic, businessLogic AdditionalDocumentsBusinessLogic) {
	commonAPILogic = commonLogic
	additionalDocumentsBusinessLogic = businessLogic
}
