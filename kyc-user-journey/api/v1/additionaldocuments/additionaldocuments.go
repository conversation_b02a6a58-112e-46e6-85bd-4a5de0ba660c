package additionaldocuments

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPI "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAdditionalDocumentRoute  godoc
// @Summary Upload additional document
// @Description Uploads additional document
// @ID HandleAdditionalDocumentRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Form document form
// @Form type form
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/additional-document [POST].
func HandleAdditionalDocumentRoute(ctx *gin.Context) {
	var request modelsAPI.UploadAdditionalDocumentRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAdditionalDocumentRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	request.Data.File, err = ctx.FormFile(constants.DocumentFormKey)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAdditionalDocumentRoute: error binding file")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAdditionalDocumentRoute: error while validating additional documents request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	_, err = additionalDocumentsBusinessLogic.UploadAdditionalDocument(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAdditionalDocumentRoute Error uploading additional document")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func HandleAdditionalDocumentOnboardingRoute(ctx *gin.Context) {
	handleAdditionalDocumentRoute(ctx, constants.Onboarding)
}

func handleAdditionalDocumentRoute(ctx *gin.Context, journeyType string) {
	var request modelsAPI.UploadAdditionalDocumentRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)
	request.JourneyType = journeyType

	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	request.Data.File, err = ctx.FormFile(constants.DocumentFormKey)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentRoute: error binding file")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentRoute: error while validating additional documents request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	res, err := additionalDocumentsBusinessLogic.UploadAdditionalDocument(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentRoute Error uploading additional document")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, res))
}

func HandleAdditionalDocumentPreviewRoute(ctx *gin.Context) {
	handleAdditionalDocumentPreviewRoute(ctx, constants.Onboarding)
}

func handleAdditionalDocumentPreviewRoute(ctx *gin.Context, journeyType string) {
	var request modelsAPI.AdditionalDocumentPreviewRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)
	request.JourneyType = journeyType
	request.DocumentType = ctx.Query(constants.DocumentTypeQueryKey)

	// validate request.
	err := request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentPreviewRoute: error while validating additional documents preview request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	res, err := additionalDocumentsBusinessLogic.PreviewAdditionalDocument(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleAdditionalDocumentPreviewRoute Error getting additional document preview URL")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, res))
}
