package cams

import (
	"net/http"
	"strings"

	"github.com/angel-one/kyc-user-journey/config"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

// HandleCommonAvailableBanksRoute godoc
// @Summary Cams Available Banks Route
// @Description API to get available banks for cams
// @ID HandleCommonAvailableBanksRoute
// @Tags cams
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/cams/available-banks [GET].
func HandleCommonAvailableBanksRoute(ctx *gin.Context) {
	handleGenericCamsAvailableBanksRoute(ctx)
}

// HandleEmodCamsAvailableBanksRoute godoc
// @Summary Emod Cams Available Banks Route
// @Description API to get available banks for cams
// @ID HandleEmodCamsAvailableBanksRoute
// @Tags cams
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/cams/available-banks [GET].
func HandleEmodCamsAvailableBanksRoute(ctx *gin.Context) {
	handleGenericCamsAvailableBanksRoute(ctx)
}

func handleGenericCamsAvailableBanksRoute(ctx *gin.Context) {
	request := modelsV1.CamsAvailableBanksRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsAvailableBanksRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// process request.
	response, err := camsBusinessLogic.FetchAvailableBanks(ctx)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsAvailableBanksRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleEmodCamsCreateConsentRoute godoc
// @Summary Emod Cams Create Consent Route
// @Description API to create consent for cams
// @ID HandleEmodCamsCreateConsentRoute
// @Tags cams
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param X-intent header string true "intent"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/cams/create-consent [POST].
func HandleEmodCamsCreateConsentRoute(ctx *gin.Context) {
	intent := ctx.GetString(constants.IntentContextKey)
	handleGenericCamsCreateConsentRoute(ctx, intent)
}

// HandleOnboardingCamsCreateConsentRoute godoc
// @Summary Onboarding Cams Create Consent Route
// @Description API to create consent for cams
// @ID HandleOnboardingCamsCreateConsentRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/cams/create-consent [POST].
func HandleOnboardingCamsCreateConsentRoute(ctx *gin.Context) {
	handleGenericCamsCreateConsentRoute(ctx, constants.Onboarding)
}

func handleGenericCamsCreateConsentRoute(ctx *gin.Context, journeyType string) {
	request := modelsV1.CamsCreateConsentRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericCamsCreateConsentRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}
	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsCreateConsentRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// process request.
	response, err := camsBusinessLogic.CreateConsent(ctx, &request, journeyType)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsCreateConsentRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleEmodCamsStatusRoute godoc
// @Summary Emod Cams Status Route
// @Description API to check status for cams
// @ID HandleEmodCamsStatusRoute
// @Tags cams
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param X-intent header string true "intent"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/cams/status [GET].
func HandleEmodCamsStatusRoute(ctx *gin.Context) {
	handleGenericCamsStatusRoute(ctx)
}

// HandleOnboardingCamsStatusRoute godoc
// @Summary Onboarding Cams Status Route
// @Description API to check status for cams
// @ID HandleOnboardingCamsStatusRoute
// @Tags cams
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/cams/status [GET].
func HandleOnboardingCamsStatusRoute(ctx *gin.Context) {
	handleGenericCamsStatusRoute(ctx)
}
func handleGenericCamsStatusRoute(ctx *gin.Context) {
	request := modelsV1.CamsStatusRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsStatusRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// process request.
	response, err := camsBusinessLogic.CheckStatus(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericCamsStatusRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleEmodCamsRedirectRoute godoc
// @Summary Cams callback Route
// @Description Handle Cams callback and redirect the user
// @ID HandleEmodCamsRedirectRoute
// @Tags cams
// @Success 300
// @Failure 500 {object} models.Response
// @Router /v1/emod/cams/redirect [GET].
func HandleEmodCamsRedirectRoute(ctx *gin.Context) {
	intent := ctx.Query(constants.CamsIntentQueryParamsKey)
	if intent == constants.Empty {
		log.Info(ctx).Msg("HandleEmodCamsRedirectRoute: intent not supported")
		ctx.JSON(models.GetErrorResponse(constants.ErrInvalidIntent.Value()))
		return
	}
	handleGenericCamsRedirectRoute(ctx, intent)
}

// HandleOnboardingCamsRedirectRoute godoc
// @Summary Cams correction callback Route
// @Description Handle Cams callback and redirect the user
// @ID HandleOnboardingCamsRedirectRoute
// @Tags cams
// @Success 300
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/cams/redirect [GET].
func HandleOnboardingCamsRedirectRoute(ctx *gin.Context) {
	handleGenericCamsRedirectRoute(ctx, constants.Onboarding)
}

func handleGenericCamsRedirectRoute(ctx *gin.Context, journeyType string) {
	// Get all query parameters
	queryParams := ctx.Request.URL.Query()

	response := camsBusinessLogic.CamsRedirection(ctx, &queryParams, journeyType)
	ctx.Redirect(http.StatusMovedPermanently, response.RedirectURL)
}

// HandleCamsWebhookRoute godoc
// @Summary Cams Webhook Route
// @Description Process Webhooks received from CAMS
// @ID HandleCamsWebhookRoute
// @Tags cams
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/cams/webhook [POST].
func HandleCamsWebhookRoute(ctx *gin.Context) {
	apiKey := strings.TrimSpace(ctx.GetHeader(constants.APIKey))
	if config.Cams().WebhookAPIKey != constants.Empty && apiKey != config.Cams().WebhookAPIKey {
		log.Error(ctx).Stack().Msg("HandleCamsWebhookRoute: invalid api key")
		ctx.JSON(models.GetErrorResponse(constants.ErrUnauthorizedAPIAccess.WithDetails("invalid api key")))
		return
	}

	var request modelsV1.CamsWebhookRequest
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleCamsWebhookRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCamsWebhookRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	err = camsBusinessLogic.ProcessCamsWebhook(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleCamsWebhookRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
