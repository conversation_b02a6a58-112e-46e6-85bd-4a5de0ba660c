package cams

import (
	"context"
	"net/url"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CamsBusinessLogic interface {
	FetchAvailableBanks(ctx context.Context) (*modelsV1.CamsAvailableBanksResponse, error)
	CreateConsent(ctx context.Context, req *modelsV1.CamsCreateConsentRequest, journeyType string) (*modelsV1.CamsCreateConsentResponse, error)
	CheckStatus(ctx context.Context, req *modelsV1.CamsStatusRequest) (*modelsV1.CamsStatusResponse, error)
	CamsRedirection(ctx context.Context, queryParams *url.Values, journeyType string) *modelsV1.CamsRedirectionResponse
	ProcessCamsWebhook(ctx context.Context, req *modelsV1.CamsWebhookRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var camsBusinessLogic CamsBusinessLogic

func SetCamsBusinessLogic(apiLogic CommonAPILogic, businessLogic CamsBusinessLogic) {
	commonAPILogic = apiLogic
	camsBusinessLogic = businessLogic
}
