package notification

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleSendNotificationRoute godoc
// @Summary send communication via template Route
// @Description API to send notification
// @ID HandleSendNotificationRoute
// @Tags notification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsAPIv1.SendNotificationRequest true "notification details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/send/notification/ [POST].
func HandleSendNotificationRoute(ctx *gin.Context) {
	var request modelsAPIv1.SendNotificationRequest
	// bind the request
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSendNotification - error binding send notification request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSendNotification - error validating send notification request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := sendNotificationBusinessLogic.SendNotificationViaTemplate(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSendNotification - error processing send notification request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
