package notification

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type SendNotificationBusinessLogic interface {
	SendNotificationViaTemplate(ctx context.Context, request *modelsAPIv1.SendNotificationRequest) (*modelsAPIv1.SendNotificationResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var sendNotificationBusinessLogic SendNotificationBusinessLogic

func SetSendNotificationBusinessLogic(businessLogic SendNotificationBusinessLogic) {
	sendNotificationBusinessLogic = businessLogic
}
