package nrifatca

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type NriFatcaBusinessLogic interface {
	NriFatca(ctx context.Context, request *api.NriFatcaRequest) error
}

var commonAPILogic CommonAPILogic
var nriFatcaLogic NriFatcaBusinessLogic

func SetNriAddressBusinessLogic(commonLogic CommonAPILogic, businessLogic NriFatcaBusinessLogic) {
	commonAPILogic = commonLogic
	nriFatcaLogic = businessLogic
}
