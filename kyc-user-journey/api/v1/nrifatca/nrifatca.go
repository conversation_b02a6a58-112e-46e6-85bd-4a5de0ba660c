package nrifatca

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNriFatcaRoute godoc
// @Summary OnBoarding Nri Add fatca Route
// @Description API to add fatca deatils for NRI onboarding journey
// @ID HandleNriFatcaRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// POST /v1/onboarding/nri/fatca.
func HandleNriFatcaRoute(ctx *gin.Context) {
	var request modelsV1.NriFatcaRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriFatcaRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleNriFatcaRoute: error while validating nri address request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = nriFatcaLogic.NriFatca(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleNriFatcaRoute: error processing nri address request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
