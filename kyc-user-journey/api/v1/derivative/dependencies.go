package derivative

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	v1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type DerivativeBusinessLogic interface {
	UploadDerivativeProof(ctx context.Context, req *v1.DerivativeUploadRequest) error
	SkipDerivative(ctx context.Context, request *v1.DerivativeSkipRequest) error
	DerivativeSegments(ctx context.Context, request *v1.DerivativeSegmentsAPIRequest) error
	NRIDDPIDerivativeSegments(ctx context.Context, request *v1.NRIDDPIDerivativeSegmentsAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var derivativeBusinessLogic DerivativeBusinessLogic

func SetDerivativeBusinessLogic(apiLogic CommonAPILogic, businessLogic DerivativeBusinessLogic) {
	commonAPILogic = apiLogic
	derivativeBusinessLogic = businessLogic
}
