package derivative

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	v1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodDerivativeRoute godoc
// @Summary Emod Derivative Route
// @Description API to upload document proof for derivative in emod journey
// @ID HandleEmodDerivativeRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param type formData string true "type of the derivative proof BANK_STATEMENT, PASSBOOK, ITR, FORM16, SALARY_SLIP, DEMAT_HOLDING_STATEMENT"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/derivative [POST].
func HandleEmodDerivativeRoute(ctx *gin.Context) {
	handleGenericDerivativeRoute(ctx)
}

// HandleOnBoardingDerivativeRoute godoc
// @Summary OnBoarding Derivative Route
// @Description API to upload document proof for derivative in onboarding journey
// @ID HandleOnBoardingDerivativeRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param type formData string true "type of the derivative proof BANK_STATEMENT, PASSBOOK, ITR, FORM16, SALARY_SLIP, DEMAT_HOLDING_STATEMENT"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/proof [POST].
func HandleOnBoardingDerivativeRoute(ctx *gin.Context) {
	handleGenericDerivativeRoute(ctx)
}

func handleGenericDerivativeRoute(ctx *gin.Context) {
	request := v1.DerivativeUploadRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericDerivativeRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// bind file.
	request.Data.File, err = ctx.FormFile(constants.DerivativeProofFormKey)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericDerivativeRoute: error binding file")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingDerivativeProof.WithDetails(err.Error())))
		return
	}
	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// process the request.
	err = derivativeBusinessLogic.UploadDerivativeProof(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleOnBoardingDerivativeSkipRoute godoc
// @Summary onboarding Derivative skip Route
// @Description API to skip derivative in onboarding
// @ID HandleOnBoardingDerivativeSkipRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/skip [POST].
func HandleOnBoardingDerivativeSkipRoute(ctx *gin.Context) {
	handleGenericDerivativeSkipRoute(ctx)
}

func handleGenericDerivativeSkipRoute(ctx *gin.Context) {
	request := v1.DerivativeSkipRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSkipRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = derivativeBusinessLogic.SkipDerivative(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSkipRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleOnBoardingDerivativeSegmentsRoute godoc
// @Summary onboarding Derivative segments Route
// @Description API to add derivative segments in onboarding
// @ID HandleOnBoardingDerivativeSegmentsRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/derivative/segments [POST].
func HandleOnBoardingDerivativeSegmentsRoute(ctx *gin.Context) {
	handleGenericDerivativeSegmentsRoute(ctx)
}

func handleGenericDerivativeSegmentsRoute(ctx *gin.Context) {
	request := v1.DerivativeSegmentsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSegmentsRoute: Invalid derivative segments request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSegmentsRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = derivativeBusinessLogic.DerivativeSegments(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSegmentsRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleNRIDerivativeRoute godoc
// @Summary NRI Derivative Route
// @Description API to upload document proof for derivative in emod journey
// @ID HandleNRIDerivativeRoute
// @Tags nri
// @Accept json
// @Produce json
// @Param X-source header string true "source"\
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/derivative [POST].
func HandleNRIDerivativeRoute(ctx *gin.Context) {
	request := v1.NRIDDPIDerivativeSegmentsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleNRIDerivativeRoute: Invalid derivative segments ddpi request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleNRIDerivativeRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = derivativeBusinessLogic.NRIDDPIDerivativeSegments(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericDerivativeSkipRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
