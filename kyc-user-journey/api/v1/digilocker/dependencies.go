package digilocker

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	externalModelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/gin-gonic/gin"
)

type DigilockerBusinessLogic interface {
	InitiateDigilockerSignInRoute(ctx context.Context, request *modelsV1.DigilockerAPIRequest) (*externalModelsV1.DigilockerSignInResponseData, error)
	HandleWebhookDigilocker(ctx context.Context, responseFromDigilocker *externalModelsV1.DigilockerCallbackResponseSuccess) (*externalModelsV1.WebhookResponse, error)
	GetRedirectionURL(ctx context.Context, status, metadataEnc string) string
}

var digilockerBusinessLogic DigilockerBusinessLogic

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic

func SetDigilockerBusinessLogic(apiLogic CommonAPILogic, businessLogic DigilockerBusinessLogic) {
	commonAPILogic = apiLogic
	digilockerBusinessLogic = businessLogic
}
