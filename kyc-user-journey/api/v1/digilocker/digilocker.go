package digilocker

import (
	"net/http"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	models "github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	externalModelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/gin-gonic/gin"
)

func InitiateDigilocker(ctx *gin.Context) {
	req := modelsV1.DigilockerAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error binding Digilocker Signin request body")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = req.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating digilocker initiate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := digilockerBusinessLogic.InitiateDigilockerSignInRoute(ctx, &req)
	if err != nil {
		log.Error(ctx).Err(err).Msg("InitiateDigilocker: Failed to get response from external service")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func HandleDigilockerCallbackResponse(ctx *gin.Context) {
	var responseFromDigilocker externalModelsV1.DigilockerCallbackResponseSuccess
	if err := ctx.BindJSON(&responseFromDigilocker); err != nil {
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Info(ctx).Interface(constants.LogResponseKey, responseFromDigilocker).Msg("Received response from Digilocker")

	response, err := digilockerBusinessLogic.HandleWebhookDigilocker(ctx, &responseFromDigilocker)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleDigilockerCallbackResponse: Failed to handle webhook")
		ctx.JSON(http.StatusOK, response)
		return
	}

	ctx.JSON(http.StatusOK, response)
}

func HandleRedirection(ctx *gin.Context) {
	status := ctx.Query(constants.StatusQueryParamKey)
	metadataEnc := ctx.Query(constants.CodeQueryParamKey)

	redirectURL := digilockerBusinessLogic.GetRedirectionURL(ctx, status, metadataEnc)
	ctx.Redirect(http.StatusPermanentRedirect, redirectURL)
}
