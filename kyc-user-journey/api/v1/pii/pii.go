package pii

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandlePiiRoute godoc
// @Summary Get PII Hash from plaintext
// @Description API to get hash from plaintext
// @ID HandlePiiRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PhyJourneyChangeAPIRequest true "request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /internal/v1/pii/hash [GET].
func HandlePiiRoute(ctx *gin.Context) {
	request := modelsV1.PiiRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandlePiiRoute: Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandlePiiRoute: error validating pii request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := piiBusinessLogic.GetHashFromPlainText(ctx, request.Data)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandlePiiRoute: error processing pii hash request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
