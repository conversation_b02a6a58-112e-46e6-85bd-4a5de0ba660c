package pii

import (
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type PiiBusinessLogic interface {
	GetHashFromPlainText(ctx *gin.Context, text string) (string, error)
}

var commonAPILogic CommonAPILogic
var piiBusinessLogic PiiBusinessLogic

func SetPiiBusinessLogic(apiLogic CommonAPILogic, businessLogic PiiBusinessLogic) {
	commonAPILogic = apiLogic
	piiBusinessLogic = businessLogic
}
