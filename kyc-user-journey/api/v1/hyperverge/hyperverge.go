package hyperverge

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleHyperVergeLogin godoc
// @Summary HyperVerge Login Route
// @Description API to Fetch HyperVerge token via Login
// @ID HandleHyperVergeLogin
// @Tags hyperVerge
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/token/hyperverge [get].
func HandleHyperVergeLogin(ctx *gin.Context) {
	request := modelsAPIV1.HyperVergeTokenRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
	}

	err := request.Validate() // now process the request.
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating hyperVerge token request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	response, err := hyperVergeBusinessLogic.GetHyperVergeToken(ctx)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting hyperVerge token response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
