package hyperverge

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type TokenGenerateBusinessLogic interface {
	GetHyperVergeToken(ctx context.Context) (*modelsAPIV1.HyperVergeTokenResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var hyperVergeBusinessLogic TokenGenerateBusinessLogic

func SetHyperVergeBusinessLogic(apiLogic CommonAPILogic, businessLogic TokenGenerateBusinessLogic) {
	commonAPILogic = apiLogic
	hyperVergeBusinessLogic = businessLogic
}
