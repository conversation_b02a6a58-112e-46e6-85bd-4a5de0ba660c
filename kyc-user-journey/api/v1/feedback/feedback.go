package feedback

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleFeedbackRoute Handle Lead Route godoc
// @Summary feedback Route
// @Description API to submit feedback
// @ID HandleFeedbackRoute
// @Tags Onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/feedback [POST].
func HandleFeedbackRoute(ctx *gin.Context) {
	request := modelsV1.FeedbackAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid feedback Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating feedback request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = feedbackBusinessLogic.Feedback(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing feedback request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
