package feedback

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type FeedbackBusinessLogic interface {
	Feedback(ctx context.Context, request *modelsAPIV1.FeedbackAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var feedbackBusinessLogic FeedbackBusinessLogic

func SetFeedbackBusinessLogic(apiLogic CommonAPILogic, businessLogic FeedbackBusinessLogic) {
	commonAPILogic = apiLogic
	feedbackBusinessLogic = businessLogic
}
