package hypervergeEsign

import (
	"context"

	models2 "github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/task"
	"github.com/gin-gonic/gin"
)

type ESignHyperVergeBusinessLogic interface {
	InitiateESign(ctx context.Context, request *modelsAPIV1.ESignHypervergeAPIRequest, eSignTask task.HypervergeEsignTask, flowConfig *models2.HypervergeESignFlowConfig) (*modelsAPIV1.ESignHypervergeAPIResponse, error)
	HandleRedirect(ctx context.Context, req *modelsAPIV1.ESignHypervergeRedirectAPIRequest) (*modelsAPIV1.HypervergeRedirectAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var hypervergeESignBusinessLogic ESignHyperVergeBusinessLogic
var eSignOnboardingTask task.HypervergeEsignTask

func setHypervergeESignBusinessLogic(apiLogic CommonAPILogic, businessLogic ESignHyperVergeBusinessLogic, esignOnboardingTask task.HypervergeEsignTask) {
	commonAPILogic = apiLogic
	hypervergeESignBusinessLogic = businessLogic
	eSignOnboardingTask = esignOnboardingTask
}
