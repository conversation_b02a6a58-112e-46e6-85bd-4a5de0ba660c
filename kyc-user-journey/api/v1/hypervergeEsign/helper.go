package hypervergeEsign

import (
	"context"
	"github.com/angel-one/go-pii-utils/log"
	goUtils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/security"
	"github.com/gin-gonic/gin"
)

func parseRedirectionQueryParams(ctx *gin.Context, request *modelsV1.ESignHypervergeRedirectAPIRequest) {
	request.Data.RequestData = ctx.Query(constants.HypervergeRequestDataQueryParamKey)
	request.Data.Success = ctx.Query(constants.HypervergeSuccessQueryParamKey)
	request.Data.ID = ctx.Query(constants.HypervergeESignRequestIDQueryParamKey)
	request.Data.SignerIdentifier = ctx.Query(constants.HypervergeSignerIdentifierQueryParamKey)
	request.Data.ErrorCode = ctx.Query(constants.HypervergeErrorCodeQueryParamKey)
	request.Data.ErrorMessage = ctx.Query(constants.HypervergeErrorMessageQueryParamKey)
	request.Data.Source = ctx.Query(constants.HypervergeSourceQueryParamKey)
	request.Data.ESignProvider = ctx.Query(constants.HypervergeEsignProviderQueryParamKey)
}

func validateDecodedRequestDataOnboarding(_ context.Context, u *modelsV1.ESignHypervergeRedirectURLData) error {
	if u.Source == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty source")
	}
	if u.AppNumber == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty appNumber")
	}
	if u.Platform == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty platform")
	}
	if u.Mobile == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty mobile")
	}
	if u.CorrelationID == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty correlation id")
	}
	if u.AppVersion == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty appversion")
	}
	return nil
}

func decryptHypervergeRequestData(ctx context.Context, request *modelsV1.ESignHypervergeRedirectAPIRequestData) error {
	key := config.Security().AESEncryptionKey
	hypervergeRedirectURLDataString, err := security.DecryptAES([]byte(key), request.RequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptHypervergeRequestData: error decrypting request data string")
		return constants.ErrAESDecryption.WithDetails(err.Error())
	}
	err = goUtils.UnmarshalJSON([]byte(hypervergeRedirectURLDataString), &request.DecodedRequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptHypervergeRequestData: error unmarshalling setu redirect URL data string")
		return err
	}
	return nil
}
