package hypervergeEsign

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	models2 "github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/task"
	"github.com/gin-gonic/gin"
	"net/http"
)

// HandleOnboardingHypervergeESignRoute : /v1/onboarding/hyperverge/esign
func HandleOnboardingHypervergeESignRoute(ctx *gin.Context) {
	onboardingConfig := config.HyperVergeESign().Flow.Onboarding
	handleInitiateESignRoute(ctx, eSignOnboardingTask, &onboardingConfig)
}

func handleInitiateESignRoute(ctx *gin.Context, eSignTask task.HypervergeEsignTask, flowConfig *models2.HypervergeESignFlowConfig) {
	request := modelsAPIV1.ESignHypervergeAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	response, err := hypervergeESignBusinessLogic.InitiateESign(ctx, &request, eSignTask, flowConfig)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting hyperverge esign response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingRedirectFromHyperverge : /v1/onboarding/hyperverge/redirect
func HandleOnboardingRedirectFromHyperverge(ctx *gin.Context) {
	request := modelsAPIV1.ESignHypervergeRedirectAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	parseRedirectionQueryParams(ctx, &request)

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromHyperverge: error validating req")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = decryptHypervergeRequestData(ctx, &request.Data)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromHyperverge: error decrypting data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = validateDecodedRequestDataOnboarding(ctx, &request.Data.DecodedRequestData)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromSetu: error validating data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	onboardingFlowConfig := config.HyperVergeESign().Flow.Onboarding
	handleGenericRedirectFromHyperverge(ctx, &request, eSignOnboardingTask, &onboardingFlowConfig)
}

func handleGenericRedirectFromHyperverge(ctx *gin.Context, request *modelsAPIV1.ESignHypervergeRedirectAPIRequest, esignTask task.HypervergeEsignTask, flowConfig *models2.HypervergeESignFlowConfig) {
	ctx.Set(constants.AppNumberContextKey, request.Data.DecodedRequestData.AppNumber)
	ctx.Set(constants.CorrelationIDContextKey, request.Data.DecodedRequestData.AppNumber)
	response, err := hypervergeESignBusinessLogic.HandleRedirect(ctx, request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingRedirectFromHyperverge: error decrypting data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.Redirect(http.StatusSeeOther, response.RedirectURL)
}
