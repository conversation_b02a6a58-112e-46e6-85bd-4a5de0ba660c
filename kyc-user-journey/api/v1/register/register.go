package register

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingRegisterRoute godoc
// @Summary Register Route
// @Description API to do register in onboarding journey
// @ID HandleOnboardingRegisterRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.RegisterAPIRequest true "register request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/register [POST].
func HandleOnboardingRegisterRoute(ctx *gin.Context) {
	handleGenericRegisterRoute(ctx)
}

func handleGenericRegisterRoute(ctx *gin.Context) {
	request := modelsV1.RegisterAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating register request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = registerBusinessLogic.Register(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing register request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
