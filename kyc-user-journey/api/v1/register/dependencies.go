package register

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type RegisterBusinessLogic interface {
	Register(ctx context.Context, request *modelsV1.RegisterAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var registerBusinessLogic RegisterBusinessLogic

func SetRegisterBusinessLogic(apiLogic CommonAPILogic, businessLogic RegisterBusinessLogic) {
	commonAPILogic = apiLogic
	registerBusinessLogic = businessLogic
}
