package emailverification

import (
	"context"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

type EmailOTPBusinessLogic interface {
	SendEmailOTP(ctx context.Context, email string) error
	ValidateOTPAndMarkEmailAsVerified(ctx context.Context, email, userOtp, appNumber, source string) error
	ValidateOTP(ctx context.Context, email, userOtp string) (*modelsV1.ValidateEmailOTPResponse, error)
	GetEmailForUser(ctx context.Context, clientCode string) (*modelsV1.ValidateEmailOTPPageResponse, error)
	StartWorkflow(ctx context.Context, request *modelsV1.ValidateEmailOTPPageRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emailOtpbusinessLogic EmailOTPBusinessLogic

func SetEmailOtpBusinessLogic(apiLogic CommonAPILogic, businessLogic EmailOTPBusinessLogic) {
	commonAPILogic = apiLogic
	emailOtpbusinessLogic = businessLogic
}
