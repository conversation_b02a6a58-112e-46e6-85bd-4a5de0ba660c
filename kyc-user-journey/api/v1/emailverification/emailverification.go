package emailverification

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	v1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmailSendOTPRoute godoc
// @Summary Email Send OTP Route
// @Description API to send OTP to email
// @ID HandleEmailSendOTPRoute
// @Tags email-verification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.SendEmailOTPRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/email/send-otp [POST].
func HandleEmailSendOTPRoute(ctx *gin.Context) {
	handleGenericEmailSendOTPRoute(ctx)
}

// HandleEmailValidateOTPRoute godoc
// @Summary Email OTP Verify Route
// @Description API to verify OTP sent to email
// @ID HandleEmailValidateOTPRoute
// @Tags email-verification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.ValidateEmailOTPRequest true "email otp validate request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/email/validate-otp [POST].
func HandleEmailValidateOTPRoute(ctx *gin.Context) {
	var request v1.ValidateEmailOTPRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	response, err := emailOtpbusinessLogic.ValidateOTP(ctx, request.Data.Email, request.Data.OTP)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing validate otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingEmailValidateOTPRoute godoc
// @Summary Email OTP Verify Route for onboarding
// @Description API to verify OTP sent to email
// @ID HandleOnboardingEmailValidateOTPRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.ValidateEmailOTPRequest true "email otp validate request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/email/validate-otp [POST].
func HandleOnboardingEmailValidateOTPRoute(ctx *gin.Context) {
	handleGenericEmailValidationAndSaveRoute(ctx)
}

func handleGenericEmailValidationAndSaveRoute(ctx *gin.Context) {
	var request v1.ValidateEmailOTPRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	err = emailOtpbusinessLogic.ValidateOTPAndMarkEmailAsVerified(ctx, request.Data.Email, request.Data.OTP, request.AppNumber, request.Source)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing validate otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleOnboardingEmailSendOTPRoute godoc
// @Summary Onboarding Email Send OTP Route
// @Description API to send OTP to email
// @ID HandleOnboardingEmailSendOTPRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.SendEmailOTPRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/email/send-otp [POST].
func HandleOnboardingEmailSendOTPRoute(ctx *gin.Context) {
	handleGenericEmailSendOTPRoute(ctx)
}

func handleGenericEmailSendOTPRoute(ctx *gin.Context) {
	var request v1.SendEmailOTPRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	err = emailOtpbusinessLogic.SendEmailOTP(ctx, request.Data.Email)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleWelcomeEmailValidationRoute godoc
// @Summary Onboarding Email Send OTP Route
// @Description API to send OTP to email
// @ID HandleWelcomeEmailValidationRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.SendEmailOTPRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/email/validation-page [POST].
func HandleWelcomeEmailValidationRoute(ctx *gin.Context) {
	handleGenericEmailValidationPageRoute(ctx)
}

// HandleWelcomeEmailSendOTPRoute godoc
// @Summary Onboarding Email Send OTP Route
// @Description API to send OTP to email
// @ID HandleWelcomeEmailSendOTPRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.SendEmailOTPRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/email/validation-page/send-otp [POST].
func HandleWelcomeEmailSendOTPRoute(ctx *gin.Context) {
	handleGenericWelcomeEmailSendOtpRoute(ctx)
}

// HandleWelcomeEmailValidateOTPRoute godoc
// @Summary Onboarding Email Send OTP Route
// @Description API to send OTP to email
// @ID HandleWelcomeEmailValidateOTPRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body v1.SendEmailOTPRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/email/validation-page/validate-otp [POST].
func HandleWelcomeEmailValidateOTPRoute(ctx *gin.Context) {
	handleGenericWelcomeEmailValidateOtpRoute(ctx)
}

func handleGenericEmailValidationPageRoute(ctx *gin.Context) {
	var request v1.ValidationPageRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	if request.ClientCode == "" {
		log.Error(ctx).Stack().Err(err).Msg("error getting client code from request")
		ctx.JSON(models.GetErrorResponse(constants.ErrClientCodeMissingInRequest.Value()))
		return
	}

	response, err := emailOtpbusinessLogic.GetEmailForUser(ctx, request.ClientCode)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func handleGenericWelcomeEmailSendOtpRoute(ctx *gin.Context) {
	var request v1.ValidationPageRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	if request.ClientCode == "" {
		log.Error(ctx).Stack().Err(err).Msg("error getting client code from request")
		ctx.JSON(models.GetErrorResponse(constants.ErrClientCodeMissingInRequest.Value()))
		return
	}

	response, err := emailOtpbusinessLogic.GetEmailForUser(ctx, request.ClientCode)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = emailOtpbusinessLogic.SendEmailOTP(ctx, response.Data.Email)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func handleGenericWelcomeEmailValidateOtpRoute(ctx *gin.Context) {
	var request v1.ValidateEmailOTPPageRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	if request.ClientCode == "" {
		log.Error(ctx).Stack().Err(err).Msg("error getting client code from request")
		ctx.JSON(models.GetErrorResponse(constants.ErrClientCodeMissingInRequest.Value()))
		return
	}

	response, err := emailOtpbusinessLogic.GetEmailForUser(ctx, request.ClientCode)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing send otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	_, err = emailOtpbusinessLogic.ValidateOTP(ctx, response.Data.Email, request.Data.OTP)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing validate otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = emailOtpbusinessLogic.StartWorkflow(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing validate otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
