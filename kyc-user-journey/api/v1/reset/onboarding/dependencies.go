package onboarding

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type OnboardingResetBusinessLogic interface {
	Reset(ctx context.Context, request *modelsAPIv1.ResetAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var (
	commonAPILogic               CommonAPILogic
	onboardingResetBusinessLogic OnboardingResetBusinessLogic
)

func SetOnboardingResetBusinessLogic(apiLogic CommonAPILogic, businessLogic OnboardingResetBusinessLogic) {
	commonAPILogic = apiLogic
	onboardingResetBusinessLogic = businessLogic
}
