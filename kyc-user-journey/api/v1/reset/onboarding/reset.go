package onboarding

import (
	"github.com/angel-one/go-pii-utils/log"

	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleReset(ctx *gin.Context) {
	request := &modelsApiV1.ResetAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReset - error binding request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReset - error validating request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = onboardingResetBusinessLogic.Reset(ctx, request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReset - error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
