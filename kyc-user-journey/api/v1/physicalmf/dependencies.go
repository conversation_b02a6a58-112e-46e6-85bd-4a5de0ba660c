package physicalmf

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PhyMfBusinessLogic interface {
	PhyMfJourneyChange(ctx context.Context, request *modelsV1.PhyJourneyChangeAPIRequest) (*modelsV1.PhyJourneyChangeAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var phyMfBusinessLogic PhyMfBusinessLogic

func SetPhyMfBusinessLogic(apiLogic CommonAPILogic, businessLogic PhyMfBusinessLogic) {
	commonAPILogic = apiLogic
	phyMfBusinessLogic = businessLogic
}
