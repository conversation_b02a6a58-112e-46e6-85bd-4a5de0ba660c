package physicalmf

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandlePhysicalMFJourneyChangeRoute godoc
// @Summary Physical MF journey change Route
// @Description API to post email in onboarding flow
// @ID HandlePhysicalMFJourneyChangeRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.PhyJourneyChangeAPIRequest true "request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/phy-mf/journey/change [POST].
func HandlePhysicalMFJourneyChangeRoute(ctx *gin.Context) {
	request := modelsV1.PhyJourneyChangeAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating email otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := phyMfBusinessLogic.PhyMfJourneyChange(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing email otp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
