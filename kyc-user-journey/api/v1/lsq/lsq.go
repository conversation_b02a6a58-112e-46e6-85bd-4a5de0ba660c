package lsq

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func LsqLeadCalling(ctx *gin.Context) {
	var leadRequest api.LeadLsqRequest
	if err := ctx.ShouldBind(&leadRequest); err != nil {
		log.ErrorWarn(ctx, err).Msg("Binding Failed")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if err := leadRequest.Validate(); err != nil {
		log.ErrorWarn(ctx, err).Msg("Validation Failed")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err := lsqBusinessLogic.CheckCohortMatching(ctx, &leadRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Cohort Matching Failed")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
