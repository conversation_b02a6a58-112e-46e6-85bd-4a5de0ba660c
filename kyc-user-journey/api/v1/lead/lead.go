package lead

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/gin-gonic/gin"
)

// HandleLeadRoute Handle Lead Route godoc
// @Summary Lead Route
// @Description API to start journey
// @ID HandleLeadRoute
// @Tags lead
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/lead [POST].

type leadFunction func(ctx context.Context, request *modelsV1.LeadAPIRequest) *modelsV1.LeadAPIResponse

func HandleLeadRoute(ctx *gin.Context) {
	request := modelsV1.LeadAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Lead Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating lead request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleOnboardingLeadRoute - request validated successfully")

	if request.Meta.KycJourneyType == constants.KycJourneyTypeRejectionDIY {
		request.Data.RawURL = constants.Empty
	}
	updateDataBasedOnReferralRawURL(ctx, &request)
	response := fetchLeadFunctionName(request.Data.CType)(ctx, &request)
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func updateDataBasedOnReferralRawURL(ctx context.Context, req *modelsV1.LeadAPIRequest) {
	parsedURL, urlParseErr := url.Parse(req.Data.RawURL)
	if urlParseErr != nil {
		log.Error(ctx).Err(urlParseErr).Str(constants.LogMobileNo, req.Mobile).Msg("Error while parsing the Raw URL")
		return
	}

	urlQueryParams := parsedURL.Query()
	updateReferralData(ctx, urlQueryParams, req)
	req.Data.ReferralString = parsedURL.RawQuery
	updateUTMParametersDataFromQueryParams(ctx, urlQueryParams, req)
	req.Data.ReferralCode = strings.ToUpper(req.Data.ReferralCode)
	if req.Data.CType == constants.Empty {
		req.Data.CType = fetchJourneyTypeFromRawURLPath(parsedURL.Path)
	}
	log.Info(ctx).Msg("updateDataBasedOnReferralRawURL - updated request with data from raw url")
}

func updateReferralData(ctx context.Context, urlQueryParams url.Values, req *modelsV1.LeadAPIRequest) {
	refCode := constants.Empty
	sbTag := constants.Empty
	planType := constants.Empty
	cType := constants.Empty

	if deeplinkValue := urlQueryParams.Get(constants.DeeplinkValueField); deeplinkValue != constants.Empty {
		deeplinkValue, _ = url.QueryUnescape(deeplinkValue)
		refCode, sbTag, planType, cType = GetReferralDataFromColonSeperatedString(deeplinkValue)
	}

	if referralCodeValue := urlQueryParams.Get(constants.ReferralCodeField); referralCodeValue != constants.Empty {
		if strings.Contains(referralCodeValue, "::") {
			refCode, sbTag, planType, cType = GetReferralDataFromColonSeperatedString(constants.ReferralCodeField + "=" + referralCodeValue)
		} else {
			refCode = referralCodeValue
		}
	}

	if sbTagValue := urlQueryParams.Get(constants.SBTagReferralField); sbTagValue != constants.Empty {
		sbTag = sbTagValue
	}

	if sbTagValue := urlQueryParams.Get("sbtag"); sbTagValue != constants.Empty {
		sbTag = sbTagValue
	}

	if sbTagValue := urlQueryParams.Get("sbTag"); sbTagValue != constants.Empty {
		sbTag = sbTagValue
	}

	if btypeValue := urlQueryParams.Get(constants.BrokerageTypeReferralField); btypeValue != constants.Empty {
		planType = btypeValue
	}

	if refCode != constants.Empty {
		req.Data.ReferralCode = refCode
	}

	if sbTag != constants.Empty {
		req.Data.SubBrokerTag = sbTag
		if sbTagDecoded, decodeErr := utils.DecodeB64(sbTag); decodeErr == nil {
			req.Data.SubBrokerTag = sbTagDecoded
		}
	}

	if planType != constants.Empty {
		req.Data.BrokerageType, _ = utils.DecodeB64(planType)
	}
	if cType == constants.Empty {
		cType = urlQueryParams.Get(constants.ReferralCTypeField)
	}

	req.Data.CType = cType
	log.Info(ctx).Msg("updateReferralData - updated request with referral data from raw URL")
}

func updateUTMParametersDataFromQueryParams(ctx context.Context, urlQueryParams url.Values, req *modelsV1.LeadAPIRequest) {
	var lsDataObj modelsV1.UtmParametersData
	var encodedPageURL string

	utmSource := strings.TrimSpace(urlQueryParams.Get(constants.UTMSource))
	utmMedium := strings.TrimSpace(urlQueryParams.Get(constants.UTMMedium))
	utmCampaign := strings.TrimSpace(urlQueryParams.Get(constants.UTMCampaign))

	if encodedPageURL = urlQueryParams.Get(constants.PageURLField); encodedPageURL != constants.Empty {
		pageURL, err := utils.DecodeB64(encodedPageURL)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("error while DecodeB64 pageUrl")
		}
		pageURL = strings.TrimSpace(pageURL)
		if pageURL != constants.Empty {
			pURL, err := url.Parse(pageURL)
			if err != nil {
				log.Error(ctx).Stack().Err(err).Msg("error while parsing pageUrl")
			} else {
				pURLData := pURL.Query()
				utmSource = strings.TrimSpace(pURLData.Get(constants.UTMSource))
				utmMedium = strings.TrimSpace(pURLData.Get(constants.UTMMedium))
				utmCampaign = strings.TrimSpace(pURLData.Get(constants.UTMCampaign))
			}
		}
		lsDataObj.EntryURL = pageURL
	}

	if utmSource != constants.Empty || utmMedium != constants.Empty || utmCampaign != constants.Empty || encodedPageURL != constants.Empty {
		lsDataObj.SourceMedium = utmSource
		lsDataObj.LeadMedium = utmMedium
		lsDataObj.LeadCampaign = utmCampaign

		updateFieldFromEncodedB64(ctx, urlQueryParams, constants.City, &lsDataObj.City)
		updateFieldFromEncodedB64(ctx, urlQueryParams, constants.Name, &lsDataObj.FirstName)

		referralPromoCode := urlQueryParams.Get(constants.ReferralPromoCode)
		if referralPromoCode != constants.Empty {
			referralCode, err := utils.DecodeB64(referralPromoCode)
			if err != nil {
				log.Error(ctx).Stack().Err(err).Msg("error while DecodeB64 ReferralCode")
			}
			req.Data.ReferralCode = strings.TrimSpace(referralCode)
		}

		lsDataObj.Phone = req.Meta.Mobile
		lsDataObj.GAUserAgent = req.UserAgent
		b, err := json.Marshal(lsDataObj)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("error while MarshalJSON ls_data")
		}
		req.Data.LsData = string(b)
		log.Info(ctx).Msg("updateUTMParametersDataFromQueryParams - updated request with utm parameters")
	}
}

func updateFieldFromEncodedB64(ctx context.Context, urlQueryParams url.Values, field string, target *string) {
	if value := urlQueryParams.Get(field); value != constants.Empty {
		if decodedValue, err := utils.DecodeB64(value); err == nil {
			*target = strings.TrimSpace(decodedValue)
		} else {
			log.Error(ctx).Stack().Err(err).Msg("error while DecodeB64 " + field)
		}
	}
}

func GetReferralDataFromColonSeperatedString(str string) (refCode, sbTag, planType, cType string) {
	if deeplinkValueParams := strings.Split(str, "::"); len(deeplinkValueParams) > 0 {
		for _, deeplinkParam := range deeplinkValueParams {
			if paramField := strings.Split(deeplinkParam, "="); len(paramField) == 2 {
				if strings.EqualFold(paramField[0], constants.ReferralCodeField) {
					refCode = paramField[1]
				}
				if strings.EqualFold(paramField[0], constants.SBTagReferralField) {
					sbTag = paramField[1]
				}
				if strings.EqualFold(paramField[0], constants.BrokerageTypeReferralField) {
					planType = paramField[1]
				}
				if strings.EqualFold(paramField[0], constants.ReferralCTypeField) {
					cType = paramField[1]
				}
			}
		}
	}
	return
}

func fetchLeadFunctionName(key string) leadFunction {
	LeadfunctionMap := map[string]leadFunction{
		constants.Default:            leadBusinessLogic.LeadStart,
		constants.PhysicalMFToEquity: leadBusinessLogic.MfToEquityLeadStart,
	}
	if function, exists := LeadfunctionMap[key]; exists {
		return function
	} else {
		return LeadfunctionMap[constants.Default]
	}
}

func fetchJourneyTypeFromRawURLPath(urlPath string) string {
	pathSegments := strings.Split(urlPath, "&")
	for _, segment := range pathSegments {
		kv := strings.SplitN(segment, "=", 2)
		if len(kv) == 2 && kv[0] == constants.ReferralCTypeField {
			return kv[1]
		}
	}
	return constants.Empty
}

// HandleDeleteLeadRoute Handle Delete Lead Route godoc
// @Summary Delete Lead Route
// @Description API to delete journey
// @ID HandleLeadRoute
// @Tags lead
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-requestId header string false "request id"
// @Param Authorization header string true "Bearer ${JWT}"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/onboarding/nri/lead [DELETE].
func HandleDeleteLeadRoute(ctx *gin.Context) {
	request := modelsV1.DeleteLeadAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating lead request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleDeleteLeadRoute - request validated successfully")

	err = leadBusinessLogic.ArchiveAndDeleteLead(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error deleting lead data")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
