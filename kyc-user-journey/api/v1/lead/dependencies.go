package lead

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type LeadBusinessLogic interface {
	LeadStart(ctx context.Context, request *modelsV1.LeadAPIRequest) *modelsV1.LeadAPIResponse
	MfToEquityLeadStart(ctx context.Context, request *modelsV1.LeadAPIRequest) *modelsV1.LeadAPIResponse
	ArchiveAndDeleteLead(ctx context.Context, request *modelsV1.DeleteLeadAPIRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var leadBusinessLogic LeadBusinessLogic

func SetLeadBusinessLogic(apiLogic CommonAPILogic, businessLogic LeadBusinessLogic) {
	commonAPILogic = apiLogic
	leadBusinessLogic = businessLogic
}
