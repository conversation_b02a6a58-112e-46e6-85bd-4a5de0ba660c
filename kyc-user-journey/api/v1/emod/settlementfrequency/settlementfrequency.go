package settlementfrequency

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodSettlementFrequency godoc
// @Summary EMOD Settlement Frequency Route
// @ID HandleEmodSettlementFrequency
// @Tags settlement frequency
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/emod/settlement-frequency [post].
func HandleEmodSettlementFrequency(ctx *gin.Context) {
	request := modelsV1.EmodSettlementFrequencyAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding emod settlement-frequency request")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate the request
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating emod settlement-frequency request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	response, err := emodSettlementFrequencyBusinessLogic.ProcessSettlementFrequencyRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing emod settlement-frequency request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
