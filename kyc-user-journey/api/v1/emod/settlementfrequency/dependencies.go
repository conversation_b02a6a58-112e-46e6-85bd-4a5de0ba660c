package settlementfrequency

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodSettlementFrequencyBusinessLogic interface {
	ProcessSettlementFrequencyRequest(ctx context.Context, request *modelsV1.EmodSettlementFrequencyAPIRequest) (*modelsV1.EmodSettlementFrequencyAPIResponse, error)
}
type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodSettlementFrequencyBusinessLogic EmodSettlementFrequencyBusinessLogic

func SetEmodSettlementFrequencyBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodSettlementFrequencyBusinessLogic) {
	commonAPILogic = apiLogic
	emodSettlementFrequencyBusinessLogic = businessLogic
}
