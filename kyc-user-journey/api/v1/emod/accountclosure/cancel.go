package accountclosure

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAccountClosureRequestCancellation godoc
// @Summary Request to cancel a/c closure request
// @Description accountclosure
// @ID HandleAccountClosureRequestCancellation
// @Tags accountclosure
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/acc-closure/cancel [POST].
func HandleAccountClosureRequestCancellation(ctx *gin.Context) {
	var request emodmodel.AccountClosureRequestCancellationRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// validate request.
	err := request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureRequestCancellation: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	err = accountClosureRequestCancellationLogic.ProcessRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureRequestCancellation: error processing the request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
