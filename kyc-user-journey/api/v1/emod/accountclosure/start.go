package accountclosure

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAccountClosureStart godoc
// @Summary Start account closure
// @Description accountclosure
// @ID HandleAccountClosureStart
// @Tags accountclosure
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/acc-closure/start [POST].
func HandleAccountClosureStart(ctx *gin.Context) {
	var request emodmodel.AccountClosureStartRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)
	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("AccountClosureStart: attempt")

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountClosureStart: error binding request body")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureStart: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := accountClosureStartLogic.ProcessRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureStart: error processing the request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// ValidateAccountClosureRequest godoc
// @Summary Start account closure
// @Description validate account closure or transfer request
// @ID ValidateAccountClosureOrTransferRequest
// @Tags validateAccountClosureRequest
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/validate/emod/account-closure [GET].
func ValidateAccountClosureRequest(ctx *gin.Context) {
	var request emodmodel.ValidateAccountClosureRequest

	request.Meta = commonAPILogic.GetMeta(ctx)
	request.Headers = commonAPILogic.GetHeaders(ctx)
	parseQueryParams(ctx, &request)

	ctx.Set(constants.CorrelationIDContextKey, request.ClientID)
	log.Info(ctx).Str("clientCode", request.ClientID).Msg("ValidateAccountClosureRequest: attempt")

	// validate request.
	err := request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("ValidateAccountClosureRequest: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	err = accountClosureStartLogic.ValidateRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("ValidateAccountClosureRequest: error processing the request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func parseQueryParams(ctx *gin.Context, request *emodmodel.ValidateAccountClosureRequest) {
	request.ClientID = ctx.Query("clientId")
}
