package accountclosure

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var accountClosureRequestCancellationLogic AccountClosureCancellationBusinessLogic
var accountClosureStartLogic AccountClosureStartBusinessLogic

type AccountClosureCancellationBusinessLogic interface {
	ProcessRequest(ctx context.Context,
		request *emodmodel.AccountClosureRequestCancellationRequest) error
}

func SetAccountClosureCancellationLogic(apiLogic CommonAPILogic,
	businessLogic AccountClosureCancellationBusinessLogic) {
	commonAPILogic = apiLogic
	accountClosureRequestCancellationLogic = businessLogic
}

type AccountClosureStartBusinessLogic interface {
	ProcessRequest(ctx context.Context,
		request *emodmodel.AccountClosureStartRequest) (*emodmodel.AccountClosureStartResponse, error)
	ValidateRequest(ctx context.Context, request *emodmodel.ValidateAccountClosureRequest) error
}

func SetAccountClosureStartLogic(apiLogic CommonAPILogic,
	businessLogic AccountClosureStartBusinessLogic) {
	commonAPILogic = apiLogic
	accountClosureStartLogic = businessLogic
}
