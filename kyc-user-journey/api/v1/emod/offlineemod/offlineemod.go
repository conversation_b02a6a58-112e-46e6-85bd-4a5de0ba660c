package offlineemod

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleOfflineEmodEligibility(ctx *gin.Context) {
	request := modelsV1.OfflineEmodAPIRequestData{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("OfflineEmodEligibility: attempt")

	// Validate the request body and parameters
	err := request.Validate()

	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating offline eMod eligibility request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleOfflineEmodEligibility - request validated successfully")

	// call business layer for checking eligibility
	resp, err := offlineEmodBusinessLogic.OfflineEmodEligibility(ctx, &request)

	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing offline eMod eligibility")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, resp))
}
