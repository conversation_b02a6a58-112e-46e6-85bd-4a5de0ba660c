package offlineemod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type OfflineEmodBusinessLogic interface {
	OfflineEmodEligibility(ctx context.Context, request *modelsV1.OfflineEmodAPIRequestData) (*modelsV1.OfflineEmodAPIResponse, error)
}

var commonAPILogic CommonAPILogic
var offlineEmodBusinessLogic OfflineEmodBusinessLogic

func SetOfflineEmodBusinessLogic(apiLogic CommonAPILogic, businessLogic OfflineEmodBusinessLogic) {
	commonAPILogic = apiLogic
	offlineEmodBusinessLogic = businessLogic
}
