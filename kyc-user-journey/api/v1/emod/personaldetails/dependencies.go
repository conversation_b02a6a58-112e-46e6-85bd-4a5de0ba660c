package personaldetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PersonalDetailsEmodBusinessLogic interface {
	ProcessRequest(ctx context.Context, request *emodmodel.PersonalDetailsRequest) (*emodmodel.PersonalDetailsResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var personalDetailsEmodLogic PersonalDetailsEmodBusinessLogic

func SetPersonalDetailsLogic(apiLogic CommonAPILogic, businessLogic PersonalDetailsEmodBusinessLogic) {
	commonAPILogic = apiLogic
	personalDetailsEmodLogic = businessLogic
}
