package personaldetails

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleEmodPersonalDetails(ctx *gin.Context) {
	var request emodmodel.PersonalDetailsRequest

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate(ctx)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := personalDetailsEmodLogic.ProcessRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing the mobile personal details submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
