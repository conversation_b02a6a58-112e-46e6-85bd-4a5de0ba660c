package trsseedingcheck

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleTRSPanAadhaarSeedingRoute godoc
// @Summary Starts Workflow for checking seeding status
// @Description Starts trs-pan-aadhaar-seeding-check workflow
// @ID HandleTRSPanAadhaarSeedingRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param X-Intent header string true "intent"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/trs-seeding-check [POST].
func HandleTRSPanAadhaarSeedingRoute(ctx *gin.Context) {
	request := api.TRSPanAadhaarSeedingCheckRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleTRSPanAadhaarSeedingRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process the request.
	err = trsPanAadhaarSeedingBusinessLogic.StartSeedingCheckWorkflow(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleTRSPanAadhaarSeedingRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
