package trsseedingcheck

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type TRSPanAadhaarSeedingBusinessLogic interface {
	StartSeedingCheckWorkflow(ctx context.Context, req *api.TRSPanAadhaarSeedingCheckRequest) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var trsPanAadhaarSeedingBusinessLogic TRSPanAadhaarSeedingBusinessLogic

func SetTRSPanAadhaarSeedingBusinessLogic(apiLogic CommonAPILogic, businessLogic TRSPanAadhaarSeedingBusinessLogic) {
	commonAPILogic = apiLogic
	trsPanAadhaarSeedingBusinessLogic = businessLogic
}
