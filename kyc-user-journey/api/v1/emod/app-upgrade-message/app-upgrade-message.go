package appupgrademessage

import (
	"net/http"

	emodmodel "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

func HandleEmodOneAppUpgradeMsg(ctx *gin.Context) {
	handleEmodOneAppUpgradeMsg(ctx)
}

func handleEmodOneAppUpgradeMsg(ctx *gin.Context) {
	var request emodmodel.EmodOneAppUpgradeReq
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).
			Msg("Invalid Request Payload For EmodOneAppUpgradeReq")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).
		Interface(constants.ClientCode, request.ClientCode).
		Interface("pushedFrom", request.PushedFrom).
		Msg("request received for token generation of e-mod 1.0")

	response := emodmodel.EmodOneAppUpgradeResp{
		Message:   "success",
		IsSuccess: true,
		Token:     "768a0354-d658-4e58-901d-b0ca5e3eaf20", /// this is dummy token, there is no use or validation for this token
	}
	ctx.JSON(http.StatusOK, response)
}
