package physicalemod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PhysicalEmodBusinessLogic interface {
	PhysicalEmodHandleFlow(ctx context.Context, request *modelsV1.PhysicalEmodSubmitAPIRequest) (*modelsV1.PhysicalEmodAPIResponse, error)
	ProcessPhysicalEmodStartRequest(ctx context.Context, request *modelsV1.PhysicalEmodStartAPIRequest) (*modelsV1.PhysicalEmodStartAPIResponse, error)
	ProcessSegmentDeactivationEmodStartRequest(ctx context.Context, request *modelsV1.PhysicalEmodStartAPIRequest) (*modelsV1.PhysicalEmodStartAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var physicalEmodBusinessLogic PhysicalEmodBusinessLogic

func SetPhysicalEmodBusinessLogic(apiLogic CommonAPILogic, businessLogic PhysicalEmodBusinessLogic) {
	commonAPILogic = apiLogic
	physicalEmodBusinessLogic = businessLogic
}
