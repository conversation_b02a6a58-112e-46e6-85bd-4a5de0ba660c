package physicalemod

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandlePhysicalEmodSubmit(ctx *gin.Context) {
	// Extract headers and metadata from the context
	request := modelsV1.PhysicalEmodSubmitAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("PhysicalEmodStart: attempt")

	err := parsePhysicalEmodRequestData(ctx, &request)
	if err != nil {
		return
	}

	// Validate the request body and parameters
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating physical eMod submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandlePhysicalEmodSubmit - request validated successfully")

	// call business layer for storing and updating the db.
	resp, err := physicalEmodBusinessLogic.PhysicalEmodHandleFlow(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing physical eMod file submission")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, resp))
}

func HandlePhysicalEmodStart(ctx *gin.Context) {
	// Extract headers and metadata from the context
	request := modelsV1.PhysicalEmodStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandlePhysicalEmodStart: error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// Validate the request body and parameters.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandlePhysicalEmodStart: error validating physical eMod start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandlePhysicalEmodStart - request validated successfully")

	// call business layer.
	if request.Intent == constants.EmodSegmentDeactivationIntent {
		resp, err2 := physicalEmodBusinessLogic.ProcessSegmentDeactivationEmodStartRequest(ctx, &request)
		if err2 != nil {
			log.Error(ctx).Err(err).Msg("HandlePhysicalEmodStart: error processing segment deactivation request")
			ctx.JSON(models.GetErrorResponse(err2))
			return
		}
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, resp))
		return
	}
	resp, err := physicalEmodBusinessLogic.ProcessPhysicalEmodStartRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandlePhysicalEmodStart: error processing physical eMod request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, resp))
}

func parsePhysicalEmodRequestData(ctx *gin.Context, request *modelsV1.PhysicalEmodSubmitAPIRequest) error {
	var err error
	request.Data.PhysicalEmodFile, err = ctx.FormFile(constants.PhysicalEmodFormKey)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error getting the physical emod file")
		ctx.JSON(models.GetErrorResponse(constants.ErrPhysicalEmodFileNotFound.WithDetails(err.Error())))
		return err
	}
	return nil
}
