package accountactivation

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type AccountActivationBusinessLogic interface {
	UploadList(ctx context.Context, req *api.AccountActivationToggleUploadAPIRequest) error
	GetReasonsList(ctx context.Context, req *api.AccountActivationToggleReasonsAPIRequest) (*api.AccountActivationToggleReasonsAPIResponse, error)
	AccountActivationStart(ctx context.Context, req *api.AccountActivationToggleStartAPIRequest) (*api.AccountActivationToggleStartAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var accountActivationBusinessLogic AccountActivationBusinessLogic

func SetAccountActivationBusinessLogic(apiLogic CommonAPILogic, businessLogic AccountActivationBusinessLogic) {
	commonAPILogic = apiLogic
	accountActivationBusinessLogic = businessLogic
}
