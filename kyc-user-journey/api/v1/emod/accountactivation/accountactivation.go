package accountactivation

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAccountActivationToggleUploadRoute godoc
// @Summary Starts Account Reactivation/Deactivation Emod
// @Description Takes client listStarts Account Reactivation/Deactivation Flow for clients shared in file
// @ID HandleAccountActivationToggleUploadRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param X-Intent header string true "intent"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/activation-toggle/upload [POST].
func HandleAccountActivationToggleUploadRoute(ctx *gin.Context) {
	request := api.AccountActivationToggleUploadAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAccountActivationToggleUploadRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleAccountActivationToggleUploadRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleAccountActivationToggleUploadRoute: request validated successfully")

	// process the request.
	err = accountActivationBusinessLogic.UploadList(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAccountActivationToggleUploadRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleAccountActivationToggleReasonsRoute godoc
// @Summary Get Applicable Reasons for Account Reactivation/Deactivation
// @Description Get Applicable Reasons for Account Reactivation/Deactivation
// @ID HandleAccountActivationToggleReasonsRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/activation-toggle/reasons [GET].
func HandleAccountActivationToggleReasonsRoute(ctx *gin.Context) {
	request := api.AccountActivationToggleReasonsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleAccountActivationToggleReasonsRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleAccountActivationToggleReasonsRoute: request validated successfully")

	// process the request.
	response, err := accountActivationBusinessLogic.GetReasonsList(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAccountActivationToggleReasonsRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleAccountActivationToggleStartRoute godoc
// @Summary Start API for Account Reactivation/Deactivation
// @Description Checks Emod Eligibility and starts fulfilment.
// @ID HandleAccountActivationToggleStartRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param X-intent header string true "emod-reactivation"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 400 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/activation-toggle [POST].
func HandleAccountActivationToggleStartRoute(ctx *gin.Context) {
	request := api.AccountActivationToggleStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleAccountActivationToggleStartRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleAccountActivationToggleStartRoute: request validated successfully")

	// process the request.
	response, err := accountActivationBusinessLogic.AccountActivationStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAccountActivationToggleStartRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
