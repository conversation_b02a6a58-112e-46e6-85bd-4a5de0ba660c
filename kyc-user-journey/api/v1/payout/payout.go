package payout

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	apiModels "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleBalance godoc
// @Summary Balance Handler Route
// @ID HandleBalance
// @Tags userDetails
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/physical/funds/handle [post].
func HandleBalance(ctx *gin.Context) {
	request := apiModels.PayoutRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("handleBalance: attempt")

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleBalance: error binding request body")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating balance handle request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleBalance - request validated successfully")

	// process request
	response, err := payoutBusinessLogic.ProcessBalanceRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing balance handle request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
