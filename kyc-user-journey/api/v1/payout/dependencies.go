package payout

import (
	"context"

	"github.com/gin-gonic/gin"

	"github.com/angel-one/kyc-user-journey/models"
	apiModels "github.com/angel-one/kyc-user-journey/models/api"
)

type PayoutBusinessLogic interface {
	ProcessBalanceRequest(ctx context.Context, request *apiModels.PayoutRequest) (*apiModels.PayoutResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var payoutBusinessLogic PayoutBusinessLogic

func SetPayoutBusinessLogic(apiLogic CommonAPILogic, businessLogic PayoutBusinessLogic) {
	commonAPILogic = apiLogic
	payoutBusinessLogic = businessLogic
}
