package selfie

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type SelfieBusinessLogic interface {
	Selfie(ctx context.Context, request *modelsV1.SelfieAPIRequest) (*modelsV1.SelfieAPIResponse, error)
	SelfieWithoutValidation(ctx context.Context, request *modelsV1.SelfieAPIRequest) (*modelsV1.SelfieAPIResponse, error)
	SelfieWithValidation(ctx context.Context, request *modelsV1.SelfieAPIRequest) (*modelsV1.SelfieAPIResponse, error)
	SelfieNri(ctx context.Context, request *modelsV1.SelfieAPIRequest) (*modelsV1.SelfieAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var selfieBusinessLogic SelfieBusinessLogic

func SetSelfieBusinessLogic(apiLogic CommonAPILogic, businessLogic SelfieBusinessLogic) {
	commonAPILogic = apiLogic
	selfieBusinessLogic = businessLogic
}
