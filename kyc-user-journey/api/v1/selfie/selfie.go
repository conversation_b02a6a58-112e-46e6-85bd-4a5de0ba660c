package selfie

import (
	"strconv"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionSelfieRoute godoc
// @Summary Correction Selfie Route
// @Description API to post selfie of Correction Journey
// @ID HandleCorrectionSelfieRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param selfie formData file true "selfie"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/correction/selfie [POST].
func HandleCorrectionSelfieRoute(ctx *gin.Context) {
	handleGenericSelfieRoute(ctx, false)
}

// HandleOnboardingSelfieRoute godoc
// @Summary Selfie Route
// @Description API to post selfie in onboarding flow
// @ID HandleOnboardingSelfieRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param selfie formData file true "selfie"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/selfie [POST].
func HandleOnboardingSelfieRoute(ctx *gin.Context) {
	skipValidation := true
	if ctx.GetString(constants.KycJourneyTypeContextKey) == constants.KycJourneyTypeRejectionDIY {
		skipValidation = false
	}
	handleGenericSelfieRoute(ctx, skipValidation)
}

// HandleNriOnboardingSelfieRoute godoc
// @Summary Selfie Route
// @Description API to post selfie in nri onboarding flow
// @ID HandleNriOnboardingSelfieRoute
// @Tags nri
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param selfie formData file true "selfie"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/nri/onboarding/selfie [POST].
func HandleNriOnboardingSelfieRoute(ctx *gin.Context) {
	handleGenericSelfieRoute(ctx, true)
}

// HandleReKYCSelfieRoute godoc
// @Summary Selfie Route
// @Description API to post selfie in rekyc flow
// @ID HandleReKYCSelfieRoute
// @Tags rekyc
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param selfie formData file true "selfie"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/rekyc/selfie [POST].
func HandleReKYCSelfieRoute(ctx *gin.Context) {
	handleGenericSelfieRoute(ctx, false)
}

func handleGenericSelfieRoute(ctx *gin.Context, skipValidation bool) {
	request := modelsV1.SelfieAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	var err error
	err = parseSelfieRequestData(ctx, &request)
	if err != nil {
		return
	}
	err = request.Validate(ctx)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction selfie request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionSelfieRoute - request validated successfully")
	var response *modelsV1.SelfieAPIResponse

	if skipValidation {
		// Onboarding.
		appNumber := ctx.GetString(constants.AppNumberContextKey)
		if percentagerollout.IsFeatureRolloutEnabled(ctx, appNumber, constants.ApplicationRolloutSelfieRejectionConfigKey, config.Feature().Enable.SelfieRejection, false) &&
			!strings.HasPrefix(appNumber, constants.NR) {
			response, err = selfieBusinessLogic.SelfieWithValidation(ctx, &request)
		} else if strings.HasPrefix(appNumber, constants.NR) {
			response, err = selfieBusinessLogic.SelfieNri(ctx, &request)
		} else {
			response, err = selfieBusinessLogic.SelfieWithoutValidation(ctx, &request)
		}
	} else if request.Meta.KycJourneyType == constants.KycJourneyTypeRejectionDIY {
		response, err = selfieBusinessLogic.SelfieWithValidation(ctx, &request)
	} else {
		response, err = selfieBusinessLogic.Selfie(ctx, &request)
	}
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting correction selfie response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func parseSelfieRequestData(ctx *gin.Context, request *modelsV1.SelfieAPIRequest) error {
	var err error
	request.Data.LivelinessSource, _ = ctx.GetPostForm(constants.SelfieLivelinessSourceFormKey)
	if request.Data.LivelinessSource != constants.Empty {
		score, _ := ctx.GetPostForm(constants.SelfieLivelinessScoreFormKey)
		request.Data.LivelinessScore, err = strconv.ParseFloat(score, 64)
		if err != nil {
			log.Error(ctx).Err(err).Stack().Msg("error getting the selfie liveliness score")
			ctx.JSON(models.GetErrorResponse(constants.ErrSelfieLivelinessScore.WithDetails(err.Error())))
			return err
		}
	}
	request.Data.SelfieImageFile, err = ctx.FormFile(constants.SelfieFormKey)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error getting the selfie file")
		ctx.JSON(models.GetErrorResponse(constants.ErrSelfieFileNotFound.WithDetails(err.Error())))
		return err
	}
	return nil
}
