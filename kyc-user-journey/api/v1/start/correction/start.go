package correction

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCorrectionStartRoute godoc
// @Summary Correction Start Route
// @Description API to fetch status of Correction Journey
// @ID HandleCorrectionStartRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/correction/start [GET].
func HandleCorrectionStartRoute(ctx *gin.Context) {
	request := modelsV1.CorrectionStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCorrectionStartRoute - request validated successfully")

	response, err := correctionStartBusinessLogic.CorrectionJourneyStart(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting correction start response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
