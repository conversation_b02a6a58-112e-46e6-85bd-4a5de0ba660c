package correction

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CorrectionStartBusinessLogic interface {
	CorrectionJourneyStart(ctx context.Context, request *modelsV1.CorrectionStartAPIRequest) (*modelsV1.CorrectionStartAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var correctionStartBusinessLogic CorrectionStartBusinessLogic

func SetCorrectionStartBusinessLogic(apiLogic CommonAPILogic, businessLogic CorrectionStartBusinessLogic) {
	commonAPILogic = apiLogic
	correctionStartBusinessLogic = businessLogic
}
