package rekyc

import (
	"fmt"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
)

// HandleReKycStart godoc
// @Summary ReKyc Start Route
// @Description API to start reKyc Journey
// @ID HHandleReKycStartRoute
// @Tags rekyc
// @Accept json
// @Produce json
// @Param X-source header string true "spark-emod"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/rekyc/start [POST].
func HandleReKycStart(ctx *gin.Context) {
	// This counter is added for sample and for testing metrics.
	counter.Add(ctx, 1, attribute.String("dimension1", "value1"))

	request := &modelsV1.ReKycStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReKycStart - error validating rekyc start request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Str(constants.LogRequestMetaKey, fmt.Sprintf("%v", request.Meta)).
		Msg("HandleReKycStart - request received successfully")
	response, err := reKycStartBusinessLogic.ReKycJourneyStart(ctx, request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleReKycStart - error processing rekyc start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
