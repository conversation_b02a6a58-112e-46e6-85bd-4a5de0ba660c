package rekyc

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/monitoring/metrics"
	"github.com/gin-gonic/gin"
)

type ReKycStartBusinessLogic interface {
	ReKycJourneyStart(ctx context.Context, request *modelsV1.ReKycStartAPIRequest) (*modelsV1.ReKycStartAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var reKycStartBusinessLogic ReKycStartBusinessLogic
var counter, _ = metrics.IntCounter("sample_counter_metric", "Sample Counter Metric", "Count")

func SetReKycStartBusinessLogic(apiLogic CommonAPILogic, businessLogic ReKycStartBusinessLogic) {
	commonAPILogic = apiLogic
	reKycStartBusinessLogic = businessLogic
}
