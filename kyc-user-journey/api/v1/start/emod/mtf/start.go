package mtf

import (
	"strings"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/gin-gonic/gin"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
)

// HandleEmodMtf  godoc
// @Summary EMOD MTF Start Route
// @Description API to create new emod application for MTF
// @ID HandleEmodMtf
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/mtf [POST].
func HandleEmodMtf(ctx *gin.Context) {
	request := modelsV1.EmodMtfAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodMtf: error validating emod mtf request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodMtf: request validated successfully")

	// process the request.
	response, err := emodMtfBusinessLogic.MtfStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodMtf: error processing mtf start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if strings.EqualFold(response.Status, constants.EmodApplicationCreatedStatus) {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}
