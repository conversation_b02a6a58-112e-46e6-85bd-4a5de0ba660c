package mtf

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodMtfBusinessLogic interface {
	MtfStart(ctx context.Context, request *modelsV1.EmodMtfAPIRequest) (*modelsV1.EmodMtfAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodMtfBusinessLogic EmodMtfBusinessLogic

func SetEmodMtfBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodMtfBusinessLogic) {
	commonAPILogic = apiLogic
	emodMtfBusinessLogic = businessLogic
}
