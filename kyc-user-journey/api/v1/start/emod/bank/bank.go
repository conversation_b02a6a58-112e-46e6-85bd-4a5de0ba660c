package bank

import (
	"encoding/json"
	"strings"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/gin-gonic/gin"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
)

// HandleEmodAddBank  godoc
// @Summary EMOD bank route
// @Description API to create new emod application to add bank
// @ID HandleEmodAddBank
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/bank [POST].
func HandleEmodAddBank(ctx *gin.Context) {
	handleEmodAddOrUpdateBank(ctx, true)
}

// HandleEmodUpdateBank  godoc
// @Summary EMOD bank route
// @Description API to create new emod application to update bank
// @ID HandleEmodUpdateBank
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/bank [PUT].
func HandleEmodUpdateBank(ctx *gin.Context) {
	handleEmodAddOrUpdateBank(ctx, false)
}

func handleEmodAddOrUpdateBank(ctx *gin.Context, isAddBank bool) {
	request := modelsV1.EmodAddOrUpdateBankAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.Data.IsAddBank = isAddBank
	err := parseEmodAddBankRequestData(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodAddBank - Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodAddBank - error validating emod add bank request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodAddBank- request validated successfully")

	// process the request.
	response, err := emodBankBusinessLogic.AddOrUpdateBank(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodAddBank - error processing emod add bank request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if strings.EqualFold(response.Status, constants.EmodApplicationCreatedStatus) {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}

// HandleEmodBankStart  godoc
// @Summary EMOD bank route
// @Description API to create new application to start emod bank
// @ID HandleEmodBankStart
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/bank/start [POST].
func HandleEmodBankStart(ctx *gin.Context) {
	request := modelsV1.EmodBankStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating emod start bank request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodBankStart- request validated successfully")

	// process the request.
	response, err := emodBankBusinessLogic.StartEmodBank(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing emod bank start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if strings.EqualFold(response.Status, constants.EmodApplicationCreatedStatus) {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}

// HandleEmodBankOCR  godoc
// @Summary EMOD bank route
// @Description API to submit cheque in emod journey
// @ID HandleEmodBankOCR
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/bank/cheque/ocr [POST].
func HandleEmodBankOCR(ctx *gin.Context) {
	request := modelsV1.EmodBankOCRAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := parseBankOCRRequestData(ctx, &request)
	if err != nil {
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating bank ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleEmodbankOCR - request validated successfully")
	response, err := emodBankBusinessLogic.EmodBankOCR(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting generic bank ocr response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func parseEmodAddBankRequestData(ctx *gin.Context, request *modelsV1.EmodAddOrUpdateBankAPIRequest) error {
	var err error
	data, _ := ctx.GetPostForm(constants.BankDataFormKey)
	if data == constants.Empty {
		request.Data.BankCheque, err = ctx.FormFile(constants.BankChequeFormKey)
		if err != nil {
			log.Error(ctx).Err(err).Stack().Msg("error getting the bank cheque")
			return constants.ErrMissingBankDetails.Value()
		}
		request.Data.IsCheque = true
		return nil
	}
	err = json.Unmarshal([]byte(data), &request.Data)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error reading the bank data")
		return constants.ErrReadingBankData.WithDetails(err.Error())
	}
	return nil
}

func parseBankOCRRequestData(ctx *gin.Context, request *modelsV1.EmodBankOCRAPIRequest) error {
	var err error
	request.Data.BankCheque, err = ctx.FormFile(constants.BankChequeFormKey)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error getting the bank cheque")
		ctx.JSON(models.GetErrorResponse(constants.ErrBankChequeNotFound.WithDetails(err.Error())))
		return err
	}
	return nil
}
