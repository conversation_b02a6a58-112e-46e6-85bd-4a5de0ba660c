package bank

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodBankBusinessLogic interface {
	AddOrUpdateBank(ctx context.Context, request *modelsV1.EmodAddOrUpdateBankAPIRequest) (*modelsV1.EmodAddOrUpdateBankAPIResponse, error)
	StartEmodBank(ctx context.Context, request *modelsV1.EmodBankStartAPIRequest) (*modelsV1.EmodBankStartAPIResponse, error)
	EmodBankOCR(ctx context.Context, request *modelsV1.EmodBankOCRAPIRequest) (*modelsV1.EmodBankOCRAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodBankBusinessLogic EmodBankBusinessLogic

func SetEmodBankBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodBankBusinessLogic) {
	commonAPILogic = apiLogic
	emodBankBusinessLogic = businessLogic
}
