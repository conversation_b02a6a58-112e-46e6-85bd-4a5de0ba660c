package nominee

import (
	"strings"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/gin-gonic/gin"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
)

// HandleEmodNominee  godoc
// @Summary EMOD Nominee Start Route
// @Description API to create new emod application for Nominee
// @ID HandleEmodNominee
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/nominee [POST].
func HandleEmodNominee(ctx *gin.Context) {
	request := modelsV1.EmodNomineeAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	clientCode := request.Meta.ClientCode
	err = request.Validate(clientCode)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodNominee: error validating emod mtf request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodNominee: request validated successfully")

	// process the request.
	response, err := emodNomineeBusinessLogic.NomineeStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodNominee: error processing emod nominee start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if strings.EqualFold(response.Status, constants.EmodApplicationCreatedStatus) {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}
