package nominee

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodNomineeBusinessLogic interface {
	NomineeStart(ctx context.Context, request *modelsV1.EmodNomineeAPIRequest) (*modelsV1.EmodNomineeAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodNomineeBusinessLogic EmodNomineeBusinessLogic

func SetEmodNomineeBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodNomineeBusinessLogic) {
	commonAPILogic = apiLogic
	emodNomineeBusinessLogic = businessLogic
}
