package ddpi

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodDdpiBusinessLogic interface {
	DdpiStart(ctx context.Context, request *modelsV1.EmodDdpiRequest) (*modelsV1.EmodDdpiResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodDdpiBusinessLogic EmodDdpiBusinessLogic

func SetEmodDdpiBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodDdpiBusinessLogic) {
	commonAPILogic = apiLogic
	emodDdpiBusinessLogic = businessLogic
}
