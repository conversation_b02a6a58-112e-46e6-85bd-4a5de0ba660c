package ddpi

import (
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/angel-one/go-pii-utils/log"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
)

// HandleEmodDdpi  godoc
// @Summary EMOD DDPI Start Route
// @Description API to create new emod application for DDPI
// @ID HandleEmodDdpi
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/ddpi [POST].
func HandleEmodDdpi(ctx *gin.Context) {
	request := modelsV1.EmodDdpiRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.Bind(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodDdpi: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating emod ddpi request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodDdpi- request validated successfully")

	// process the request.
	response, err := emodDdpiBusinessLogic.DdpiStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error processing ddpi start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if strings.EqualFold(response.Status, constants.EmodApplicationCreatedStatus) {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}
