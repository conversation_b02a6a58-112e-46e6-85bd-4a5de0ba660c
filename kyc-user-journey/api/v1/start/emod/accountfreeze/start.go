package accountfreeze

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleAccountFreeze  godoc
// @Summary EMOD Account Freeze Route
// @Description API to start new emod application for Account Freezing or Unfreezing
// @ID HandleAccountFreeze
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/account-freeze [POST].
func HandleAccountFreeze(ctx *gin.Context) {
	request := modelsV1.AccountFreezeAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleAccountFreeze: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleAccountFreeze: request validated successfully")

	// process the request.
	response, err := accountFreezeBusinessLogic.AccountFreezeJourneyStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleAccountFreeze: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
