package accountfreeze

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type AccountFreezeBusinessLogic interface {
	AccountFreezeJourneyStart(ctx context.Context, request *api.AccountFreezeAPIRequest) (*api.AccountFreezeAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var accountFreezeBusinessLogic AccountFreezeBusinessLogic

func SetAccountFreezeBusinessLogic(apiLogic CommonAPILogic, businessLogic AccountFreezeBusinessLogic) {
	commonAPILogic = apiLogic
	accountFreezeBusinessLogic = businessLogic
}
