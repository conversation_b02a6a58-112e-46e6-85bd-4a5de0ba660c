package asba

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type ASBABusinessLogic interface {
	ASBAActivation(ctx context.Context, request *api.ASBAActivationAPIRequest) (*api.ASBAActivationAPIResponse, bool, error)
	ASBADeactivation(ctx context.Context, request *api.ASBADeactivationAPIRequest) (*api.ASBADeactivationAPIResponse, bool, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var asbaBusinessLogic ASBABusinessLogic

func SetASBABusinessLogic(apiLogic CommonAPILogic, businessLogic ASBABusinessLogic) {
	commonAPILogic = apiLogic
	asbaBusinessLogic = businessLogic
}
