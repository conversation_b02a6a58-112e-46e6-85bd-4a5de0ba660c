package asba

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleASBAActivation  godoc
// @Summary ASBA Activation Emod Start Route
// @Description API to start new emod journey for asba-activation
// @ID HandleASBAActivation
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/asba/activate [POST].
func HandleASBAActivation(ctx *gin.Context) {
	request := modelsV1.ASBAActivationAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleASBAActivation: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleASBAActivation: request validated successfully")

	// process the request.
	response, isNewApplication, err := asbaBusinessLogic.ASBAActivation(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleASBAActivation: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if isNewApplication {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}

// HandleASBADeactivation  godoc
// @Summary ASBA Deactivation Emod Start Route
// @Description API to start new emod journey for asba-deactivation
// @ID HandleASBADeactivation
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/asba/deactivate [POST].
func HandleASBADeactivation(ctx *gin.Context) {
	request := modelsV1.ASBADeactivationAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleASBAActivation: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleASBADeactivation: request validated successfully")

	// process the request.
	response, isNewApplication, err := asbaBusinessLogic.ASBADeactivation(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleASBADeactivation: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if isNewApplication {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}
