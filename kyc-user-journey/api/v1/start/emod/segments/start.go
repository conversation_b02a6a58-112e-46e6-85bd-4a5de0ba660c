package segments

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodSegments  godoc
// @Summary EMOD Segments Start Route
// @Description API to create new emod application for segments
// @ID HandleEmodSegments
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/segments [POST].
func HandleEmodSegments(ctx *gin.Context) {
	request := modelsV1.EmodSegmentsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodSegments: error binding emod segments request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodSegments: error validating emod segments request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodSegments: request validated successfully")

	// process the request.
	response, isNewApplication, err := emodSegmentsBusinessLogic.SegmentsStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodSegments: error processing segment start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	if isNewApplication {
		ctx.JSON(models.GetCreatedSuccessResponse(response))
	} else {
		ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
	}
}
