package segments

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodSegmentsBusinessLogic interface {
	SegmentsStart(ctx context.Context, request *modelsV1.EmodSegmentsAPIRequest) (*modelsV1.EmodSegmentsAPIResponse, bool, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodSegmentsBusinessLogic EmodSegmentsBusinessLogic

func SetEmodSegmentsBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodSegmentsBusinessLogic) {
	commonAPILogic = apiLogic
	emodSegmentsBusinessLogic = businessLogic
}
