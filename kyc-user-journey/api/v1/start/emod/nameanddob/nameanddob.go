package nameanddob

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodCheckNameAndDob  godoc
// @Summary NSDL Name Dob check Route
// @Description API to check name and dob as per nsdl
// @ID HandleEmodCheckNameAndDob
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/check-name-and-dob [POST].
func HandleEmodCheckNameAndDob(ctx *gin.Context) {
	request := modelsV1.EmodNameAndDobCheckAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate(ctx)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodCheckNameAndDob- request validated successfully")

	// process the request.
	response, err := emodNameAndDobUpdateBusinessLogic.VerifyCLCMNameAndDobWithNSDL(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodAddBank - error checking clcm pan name and dob request with nsdl")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleEmodNameAndDob  godoc
// @Summary EMOD NameAndDob Start Route
// @Description API to create new emod application for Updating name and dob
// @ID HandleEmodNameAndDob
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/name-and-dob [POST].
func HandleEmodNameAndDob(ctx *gin.Context) {
	request := modelsV1.EmodNameAndDobCheckAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error binding request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = request.Validate(ctx)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating submit request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := emodNameAndDobUpdateBusinessLogic.UpdatedNameAndDob(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing the name and dob update request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
