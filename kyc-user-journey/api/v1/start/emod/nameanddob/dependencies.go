package nameanddob

import (
	"context"

	"github.com/gin-gonic/gin"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
)

type EmodNameAndDobUpdateBusinessLogic interface {
	VerifyCLCMNameAndDobWithNSDL(ctx context.Context, request *modelsV1.EmodNameAndDobCheckAPIRequest) (*modelsV1.EmodNameAndDobCheckAPIResponse, error)
	UpdatedNameAndDob(ctx context.Context, request *modelsV1.EmodNameAndDobCheckAPIRequest) (*modelsV1.EmodNameAndDobCheckAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodNameAndDobUpdateBusinessLogic EmodNameAndDobUpdateBusinessLogic

func SetEmodNameAndDobUpdateBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodNameAndDobUpdateBusinessLogic) {
	commonAPILogic = apiLogic
	emodNameAndDobUpdateBusinessLogic = businessLogic
}
