package ddpi

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodDdpiDeactivateBusinessLogic interface {
	DdpiDeactivateStart(ctx context.Context, request *modelsV1.EmodDdpiRequest) (*modelsV1.EmodDdpiResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodDdpiDeactivateBusinessLogic EmodDdpiDeactivateBusinessLogic

func SetEmodDdpiDeactivateBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodDdpiDeactivateBusinessLogic) {
	commonAPILogic = apiLogic
	emodDdpiDeactivateBusinessLogic = businessLogic
}
