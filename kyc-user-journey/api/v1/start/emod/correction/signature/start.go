package signature

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodCorrectionSignatureStartRoute godoc
// @Summary Emod Correction Signature Start Route
// @Description API to fetch status of Emod Correction Signature Journey
// @ID HandleEmodCorrectionSignatureStartRoute
// @Tags correction
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/correction/signature/start [GET].
func HandleEmodCorrectionSignatureStartRoute(ctx *gin.Context) {
	request := modelsV1.EmodCorrectionSignatureStartAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating emod correction signature start request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodCorrectionSignatureStartRoute - request validated successfully")

	response, err := emodCorrectionSignatureStartBusinessLogic.EmodCorrectionSignatureJourneyStart(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting emod correction signature start response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
