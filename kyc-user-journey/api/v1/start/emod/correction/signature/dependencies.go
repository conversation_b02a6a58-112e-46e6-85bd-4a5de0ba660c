package signature

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodCorrectionSignatureStartBusinessLogic interface {
	EmodCorrectionSignatureJourneyStart(ctx context.Context, request *modelsV1.EmodCorrectionSignatureStartAPIRequest) (*modelsV1.EmodCorrectionSignatureStartAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodCorrectionSignatureStartBusinessLogic EmodCorrectionSignatureStartBusinessLogic

func SetEmodCorrectionSignatureStartBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodCorrectionSignatureStartBusinessLogic) {
	commonAPILogic = apiLogic
	emodCorrectionSignatureStartBusinessLogic = businessLogic
}
