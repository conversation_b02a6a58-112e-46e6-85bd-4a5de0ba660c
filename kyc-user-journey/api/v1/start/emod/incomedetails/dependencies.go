package incomedetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodIncomeDetailsBusinessLogic interface {
	IncomeDetailsUpdateStart(ctx context.Context, req *api.IncomeDetailsAPIRequest) (*api.IncomeDetailsResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodIncomeDetailsBusinessLogic EmodIncomeDetailsBusinessLogic

func SetEmodIncomeUpdateBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodIncomeDetailsBusinessLogic) {
	commonAPILogic = apiLogic
	emodIncomeDetailsBusinessLogic = businessLogic
}
