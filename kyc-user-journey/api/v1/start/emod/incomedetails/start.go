package incomedetails

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodIncomeDetails  godoc
// @Summary EMOD INCOME Start Route
// @Description API to create new emod application for Income
// @ID HandleEmodIncomeDetails
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/emod/income [POST].
func HandleEmodIncomeDetails(ctx *gin.Context) {
	request := modelsV1.IncomeDetailsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleIncomeDetailsUpdate - error binding emod income update request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleIncomeDetailsUpdate - error validating emod income details update request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleIncomeDetailsUpdate - request validated successfully")
	response, err := emodIncomeDetailsBusinessLogic.IncomeDetailsUpdateStart(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleIncomeDetailsUpdate - error processing emod income update request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
