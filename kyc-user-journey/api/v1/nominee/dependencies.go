package nominee

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type NomineeBusinessLogic interface {
	SkipNominee(ctx context.Context, request *modelsAPIv1.NomineeSkipAPIRequest) error
	Nominee(ctx context.Context, request *modelsAPIv1.NomineeAPIRequest) error
}

var commonAPILogic CommonAPILogic

var nomineeBusinessLogic NomineeBusinessLogic

func SetNomineeBusinessLogic(apiLogic CommonAPILogic, businessLogic NomineeBusinessLogic) {
	commonAPILogic = apiLogic
	nomineeBusinessLogic = businessLogic
}
