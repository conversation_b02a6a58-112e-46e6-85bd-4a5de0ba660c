package nominee

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	v1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnBoardingNomineeRoute godoc
// @Summary OnBoarding Nominee Route
// @Description API to add nominee in onboarding journey
// @ID HandleOnBoardingNomineeRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nominee [POST].
func HandleOnBoardingNomineeRoute(ctx *gin.Context) {
	handleGenericNomineeRoute(ctx)
}

// HandleNRIOnBoardingNomineeRoute godoc
// @Summary NRI OnBoarding Nominee Route
// @Description API to add nominee in NRI onboarding journey
// @ID HandleNRIOnBoardingNomineeRoute
// @Tags NRI onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-SubType header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/nominee [POST].
func HandleNRIOnBoardingNomineeRoute(ctx *gin.Context) {
	handleGenericNomineeRoute(ctx)
}

// HandleOnBoardingNomineeSkipRoute godoc
// @Summary onboarding Nominee skip Route
// @Description API to skip nominee in onboarding
// @ID HandleOnBoardingNomineeSkipRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-Intent header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nominee/skip [POST].
func HandleOnBoardingNomineeSkipRoute(ctx *gin.Context) {
	handleGenericNomineeSkipRoute(ctx)
}

// HandleNRIOnBoardingNomineeSkipRoute godoc
// @Summary NRI onboarding Nominee skip Route
// @Description API to skip nominee in NRI onboarding
// @ID HandleNRIOnBoardingNomineeSkipRoute
// @Tags NRI onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-SubType header string true "intent"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/nominee/skip [POST].
func HandleNRIOnBoardingNomineeSkipRoute(ctx *gin.Context) {
	handleGenericNomineeSkipRoute(ctx)
}

func handleGenericNomineeSkipRoute(ctx *gin.Context) {
	request := v1.NomineeSkipAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNomineeSkipRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericNomineeSkipRoute: - request validated successfully")

	// process the request.
	err = nomineeBusinessLogic.SkipNominee(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNomineeSkipRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

func handleGenericNomineeRoute(ctx *gin.Context) {
	request := v1.NomineeAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("handleGenericNomineeRoute: error binding request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate the request.
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNomineeRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericNomineeRoute: - request validated successfully")

	// process the request.
	err = nomineeBusinessLogic.Nominee(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("handleGenericNomineeRoute: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
