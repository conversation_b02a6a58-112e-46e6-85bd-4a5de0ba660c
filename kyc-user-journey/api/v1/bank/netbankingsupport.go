package bank

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNetBankingSupport godoc
// @Summary NetBanking Support Route
// @ID HandleNetBankingSupport
// @Tags netbanking
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/bank/netbanking-supported [get].
func HandleNetBankingSupport(ctx *gin.Context) {
	bankIFSC := ctx.Query(constants.BankIFSCQueryParamKey)
	request := modelsV1.NetBankingSupportAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	request.Data.BankIFSC = bankIFSC

	// validate the request
	err := request.Validate() // now process the request.
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating net-banking support request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	response, err := netBankingSupportBusinessLogic.NetBankingSupport(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing net-banking support request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
