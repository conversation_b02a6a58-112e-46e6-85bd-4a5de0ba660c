package bank

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type NetBankingSupportBusinessLogic interface {
	NetBankingSupport(ctx context.Context, request *modelsV1.NetBankingSupportAPIRequest) (*modelsV1.NetBankingSupportAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type BankBusinessLogic interface {
	BankIMPS(ctx context.Context, request *modelsV1.BankIMPSAPIRequest) (*modelsV1.BankIMPSAPIResponse, error)
	BankOCR(ctx context.Context, request *modelsV1.BankOCRAPIRequest) (*modelsV1.BankOCRAPIResponse, error)
	BankOCRConfirm(ctx context.Context, request *modelsV1.BankOCRConfirmAPIRequest) (*modelsV1.BankOCRConfirmAPIResponse, error)
	BankRPD(ctx context.Context, request *modelsV1.BankRPDAPIRequest) (*modelsV1.BankRPDAPIResponse, error)
	JointBankAccountConfirm(ctx context.Context, request *modelsV1.JointBankAccountConfirmationAPIRequest) error
}

var commonAPILogic CommonAPILogic
var netBankingSupportBusinessLogic NetBankingSupportBusinessLogic
var bankBusinessLogic BankBusinessLogic

func SetNetBankingSupportBusinessLogic(apiLogic CommonAPILogic, businessLogic NetBankingSupportBusinessLogic) {
	commonAPILogic = apiLogic
	netBankingSupportBusinessLogic = businessLogic
}

func SetBankBusinessLogic(apiLogic CommonAPILogic, businessLogic BankBusinessLogic) {
	commonAPILogic = apiLogic
	bankBusinessLogic = businessLogic
}

type NRIBankBusinessLogic interface {
	GetNRIPartnerBank(ctx context.Context, nriPartnerBankSearchRequest *modelsV1.NRIPartnerBankSearchAPIRequest) (*modelsV1.NRIPartnerBankSearchAPIResponse, error)
	NRIBank(ctx context.Context, request *modelsV1.NRIBankAPIRequest) (*modelsV1.NRIBankAPIResponse, error)
}

var nriBusinessLogic NRIBankBusinessLogic

func SetNRIBankBusinessLogic(businessLogic NRIBankBusinessLogic) {
	nriBusinessLogic = businessLogic
}
