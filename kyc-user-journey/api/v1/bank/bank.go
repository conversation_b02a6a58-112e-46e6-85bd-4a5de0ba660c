package bank

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingBankIMPSRoute godoc
// @Summary Bank Route
// @Description API to post bank in onboarding flow
// @ID HandleOnboardingBankIMPSRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.BankIMPSAPIRequest true "bank imps request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/bank/imps [POST].
func HandleOnboardingBankIMPSRoute(ctx *gin.Context) {
	handleGenericBankIMPSRoute(ctx)
}

func handleGenericBankIMPSRoute(ctx *gin.Context) {
	request := modelsV1.BankIMPSAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid bank imps request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating bank imps request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericBankIMPSRoute - request validated successfully")

	response, err := bankBusinessLogic.BankIMPS(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing bank imps request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingBankOCRConfirmRoute godoc
// @Summary Onboarding bank ocr update
// @Description API to update data received by OCR
// @ID HandleOnboardingBankOCRConfirmRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/bank/ocr/confirm [POST].
func HandleOnboardingBankOCRConfirmRoute(ctx *gin.Context) {
	handleGenericBankOCRConfirmRoute(ctx)
}

func handleGenericBankOCRConfirmRoute(ctx *gin.Context) {
	request := modelsV1.BankOCRConfirmAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid bank ocr confirmation Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating bank ocr confirmation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericBankOCRConfirmRoute - request validated successfully")

	response, err := bankBusinessLogic.BankOCRConfirm(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing bank ocr confirmation request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingBankRPDRoute godoc
// @Summary pnboarding bank rpd Route
// @Description API to do rpd in onboarding journey
// @ID HandleOnboardingBankRPDRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param bankCheque formData file true "bank cheque"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/bank/rpd [POST].
func HandleOnboardingBankRPDRoute(ctx *gin.Context) {
	handleGenericBankRPDRoute(ctx)
}

func handleGenericBankRPDRoute(ctx *gin.Context) {
	request := modelsV1.BankRPDAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid bank RPD Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating bank RPD API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericBankRPDRoute - request validated successfully")
	response, err := bankBusinessLogic.BankRPD(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting generic bank rpd response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleOnboardingJointBankAccountConfirmationRoute godoc
// @Summary pnboarding joint bank account confirmation Route
// @Description API to confirm joint bank account
// @ID HandleOnboardingJointBankAccountConfirmationRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param bankCheque formData file true "bank cheque"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/bank/joint-account-confirmation [POST].
func HandleOnboardingJointBankAccountConfirmationRoute(ctx *gin.Context) {
	handleGenericJointBankAccountConfirmationRoute(ctx)
}

func handleGenericJointBankAccountConfirmationRoute(ctx *gin.Context) {
	request := modelsV1.JointBankAccountConfirmationAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating joint bank account confirmation API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericJointBankAccountConfirmationRoute - request validated successfully")
	err = bankBusinessLogic.JointBankAccountConfirm(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting generic joint bank account confirmation response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
