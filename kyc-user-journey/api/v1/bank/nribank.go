package bank

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNRIPartnerBankList godoc
// @Summary NRI onboarding bank list Route
// @Description API to return bank list in nri onboarding journey
// @ID HandleNRIPartnerBankList
// @Tags NRI onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/bank/list [GET].
func HandleNRIPartnerBankList(ctx *gin.Context) {
	bankType := ctx.Query(constants.BankTypeQueryParam)

	var bankSearchRequest = modelsV1.NRIPartnerBankSearchAPIRequest{
		BankType: bankType,
	}
	err := bankSearchRequest.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleSearch - Error validating nri bank search API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := nriBusinessLogic.GetNRIPartnerBank(ctx, &bankSearchRequest)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleBankSearch - error searching for bank")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleNRIBankRoute godoc
// @Summary NRI onboarding bank  Route
// @Description API to bank in nri onboarding journey
// @ID HandleNRIBankRoute
// @Tags NRI onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nri/bank/ [POST].
func HandleNRIBankRoute(ctx *gin.Context) {
	request := modelsV1.NRIBankAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid nri bank request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating nri bank request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleNRIBankRoute - request validated successfully")

	response, err := nriBusinessLogic.NRIBank(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing nri bank request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
