package bank

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingBankOCRRoute godoc
// @Summary Onboarding bank cheque Route
// @Description API to post bank cheque of Onboarding Journey
// @ID HandleOnboardingBankOCRRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param bankCheque formData file true "bank cheque"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/bank/ocr [POST].
func HandleOnboardingBankOCRRoute(ctx *gin.Context) {
	handleGenericBankOCRRoute(ctx)
}

func handleGenericBankOCRRoute(ctx *gin.Context) {
	request := modelsV1.BankOCRAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := parseBankOCRRequestData(ctx, &request)
	if err != nil {
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating bank ocr request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("handleGenericBankOCRRoute - request validated successfully")
	response, err := bankBusinessLogic.BankOCR(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting generic bank ocr response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func parseBankOCRRequestData(ctx *gin.Context, request *modelsV1.BankOCRAPIRequest) error {
	var err error
	request.Data.BankChequeFile, err = ctx.FormFile(constants.BankChequeFormKey)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("error getting the bank cheque")
		ctx.JSON(models.GetErrorResponse(constants.ErrBankChequeNotFound.WithDetails(err.Error())))
		return err
	}
	return nil
}
