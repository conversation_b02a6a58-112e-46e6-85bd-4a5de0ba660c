package mobileverification

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type MobileOTPBusinessLogic interface {
	SendMobileOTP(ctx context.Context, mobile string) error
	ValidateMobileOTP(ctx context.Context, mobile, userOtp string) (*modelsV1.ValidateMobileOTPResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var mobileOtpBusinessLogic MobileOTPBusinessLogic

func SetMobileOtpBusinessLogic(apiLogic CommonAPILogic, businessLogic MobileOTPBusinessLogic) {
	commonAPILogic = apiLogic
	mobileOtpBusinessLogic = businessLogic
}
