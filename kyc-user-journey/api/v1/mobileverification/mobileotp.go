package mobileverification

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleMobileSendOTPRoute  godoc
// @Summary Send OTP to Mobile Route
// @Description Creates OTP and sends to mobile
// @ID HandleMobileSendOTPRoute
// @Tags mobileVerification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/mobile/send-otp [POST].
func HandleMobileSendOTPRoute(ctx *gin.Context) {
	req := modelsV1.SendMobileOTPRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	// bind the request.
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleMobileSendOTPRoute: error binding MobileSendOtp request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = req.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleMobileSendOTPRoute: error validating MobileSendOtp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	err = mobileOtpBusinessLogic.SendMobileOTP(ctx, req.Data.Mobile)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleMobileSendOTPRoute: error processing MobileSendOtp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}

// HandleMobileValidateOTPRoute godoc
// @Summary Validates Mobile OTP
// @Description Mobile
// @ID HandleMobileValidateOTPRoute
// @Tags mobileVerification
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/mobile/validate-otp [POST].
func HandleMobileValidateOTPRoute(ctx *gin.Context) {
	req := modelsV1.ValidateMobileOTPRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// bind the request.
	err := ctx.ShouldBindJSON(&req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleMobileValidateOTPRoute: error binding MobileValidateOtp request body")
		ctx.JSON(models.GetErrorResponse(constants.ErrBindingRequest.WithDetails(err.Error())))
		return
	}

	// validate request.
	err = req.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleMobileValidateOTPRoute: error validating MobileValidateOtp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := mobileOtpBusinessLogic.ValidateMobileOTP(ctx, req.Data.Mobile, req.Data.OTP)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleMobileValidateOTPRoute: error processing MobileValidateOtp request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
