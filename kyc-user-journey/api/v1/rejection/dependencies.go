package rejection

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type RejectionBusinessLogic interface {
	CheckAndActivateDIYJourney(ctx context.Context, request *modelsAPIv1.CheckAndActivateDIYJourneyAPIRequest) (*modelsAPIv1.CheckAndActivateDIYJourneyAPIResponse, error)
	GetActiveDIYFlows(ctx context.Context, request *modelsAPIv1.GetActiveDIYFlowsAPIRequest) (*modelsAPIv1.GetActiveDIYFlowsAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var rejectionBusinessLogic RejectionBusinessLogic

func SetRejectionBusinessLogic(apiLogic CommonAPILogic, businessLogic RejectionBusinessLogic) {
	commonAPILogic = apiLogic
	rejectionBusinessLogic = businessLogic
}
