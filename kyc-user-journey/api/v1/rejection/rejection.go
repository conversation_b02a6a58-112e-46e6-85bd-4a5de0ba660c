package rejection

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleCheckAndActivateDIYJourney godoc
// @Summary DIY journey activate route
// @Description API to check and activate DIY Rejection journey
// @ID HandleCheckAndActivateDIYJourney
// @Tags rejection
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/rejection/diy/activate [POST].
func HandleCheckAndActivateDIYJourney(ctx *gin.Context) {
	request := modelsV1.CheckAndActivateDIYJourneyAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating correction helper journey check and activate request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleCheckAndActivateDIYJourney - request validated successfully")

	response, err := rejectionBusinessLogic.CheckAndActivateDIYJourney(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting diy journey check and activate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleGetActiveDIYFlows godoc
// @Summary Get DIY Journey active flows route
// @Description API to get active DIY flows
// @ID HandleGetActiveDIYFlows
// @Tags rejection
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/rejection/diy/flows [GET].
func HandleGetActiveDIYFlows(ctx *gin.Context) {
	request := modelsV1.GetActiveDIYFlowsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating get diy active flows API request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleGetActiveDIYFlows - request validated successfully")

	response, err := rejectionBusinessLogic.GetActiveDIYFlows(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error getting diy journey check and activate response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
