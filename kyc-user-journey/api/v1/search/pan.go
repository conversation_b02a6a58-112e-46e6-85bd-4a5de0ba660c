package search

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleSearchClientDetailsFromPan godoc
// @ID HandleSearchClientDetailsFromPan
// @Router /internal/v1/search/pan [post].
func HandleSearchClientDetailsFromPan(ctx *gin.Context) {
	request := modelsApiV1.PANSearchRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleSearchClientDetailsFromPan - Invalid request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// validate the request
	err = request.Validate() // now process the request.
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating HandleSearchClientDetailsFromPan")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request
	response, err := clientDetailsPANBusinessLogic.GetClientDetailsFromPAN(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleSearchClientDetailsFromPan: error while processing get profile details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
