package search

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type ClientDetailsFromPANBusinessLogic interface {
	GetClientDetailsFromPAN(ctx context.Context, request *modelsApiV1.PANSearchRequest) (*modelsApiV1.PANSearchResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var clientDetailsPANBusinessLogic ClientDetailsFromPANBusinessLogic

func SetSearchBusinessLogicBusinessLogic(apiLogic CommonAPILogic, businessLogic ClientDetailsFromPANBusinessLogic) {
	commonAPILogic = apiLogic
	clientDetailsPANBusinessLogic = businessLogic
}
