package profiledetails

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/gin-gonic/gin"
)

// HandleProfileDetails godoc
// @Summary ProfileDetails Route
// @ID HandleProfileDetails
// @Tags userDetails
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/profile/profile-details [get].
func HandleProfileDetails(ctx *gin.Context) {
	request := modelsV1.ProfileDetailsAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("HandleProfileDetails: attempt")

	// validate the request
	err := request.Validate() // now process the request.
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating profile bulk request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleProfileDetails - request validated successfully")

	// process request
	response, err := profileBulkDetailsBusinessLogic.ProcessProfileDetailsRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while processing get profile details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

// HandleProfileDetailsFromCLCM godoc
// @Summary ProfileDetailsFromCLCM Route
// @ID HandleProfileDetailsFromCLCM
// @Tags userDetails
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /internal/v1/profile-clcm [post].
func HandleProfileDetailsFromCLCM(ctx *gin.Context) {
	request, err := ParseProfileDetailsRequest(ctx)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while validating profile details from clcm request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("Processing Profile Details Request")

	// Process request
	response, err := profileBulkDetailsBusinessLogic.ProcessProfileDetailsFromCLCM(ctx, request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("Error while processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	// Success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}

func ParseProfileDetailsRequest(ctx *gin.Context) (*modelsV1.ProfileDetailsCallToCLCMRequest, error) {
	var request modelsV1.ProfileDetailsCallToCLCMRequest

	// Bind JSON body
	if err := ctx.ShouldBindJSON(&request); err != nil {
		log.Error(ctx).Stack().Err(err).Msg("Failed to bind request body")
		return nil, err
	}

	// Set Headers and Meta
	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// Validate the request
	if err := request.Validate(); err != nil {
		log.Error(ctx).Stack().Err(err).Msg("Validation failed for profile details request")
		return nil, err
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("Request parsed and validated successfully")

	return &request, nil
}
