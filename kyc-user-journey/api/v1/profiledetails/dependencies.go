package profiledetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/gin-gonic/gin"
)

type ProfileBulkDetailsBusinessLogic interface {
	ProcessProfileDetailsRequest(ctx context.Context, request *modelsExternals.ProfileDetailsAPIRequest) (modelsExternals.ProfileDetailsAPIResponse, error)
	ProcessProfileDetailsFromCLCM(ctx context.Context, request *modelsExternals.ProfileDetailsCallToCLCMRequest) (modelsExternals.ProfileDetailsFromCLCM, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var profileBulkDetailsBusinessLogic ProfileBulkDetailsBusinessLogic

func SetProfileBulkDetailsBusinessLogic(apiLogic CommonAPILogic, businessLogic ProfileBulkDetailsBusinessLogic) {
	commonAPILogic = apiLogic
	profileBulkDetailsBusinessLogic = businessLogic
}

type ClcmExternalProvider interface {
	FetchProfileDetailsByClientCodes(ctx context.Context, clientCodes, attributes []string) (*modelsExternals.GetClientDetailsByAttributesResponse, error)
	GetProfileDetailsFromCLCM(ctx context.Context, request *modelsExternals.ProfileDetailsFromCLCMRequest) (*modelsExternals.GetClientDetailsByAttributesResponse, error)
}
