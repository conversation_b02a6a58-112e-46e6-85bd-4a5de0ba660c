package onboarding

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingTradeReadyStatusRoute godoc
// @Summary Onboarding Trade Ready Status Route
// @Description API to fetch status of Onboarding Trade Ready
// @ID HandleOnboardingTradeReadyStatusRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/trade-ready/status/onboarding [GET].
func HandleOnboardingTradeReadyStatusRoute(ctx *gin.Context) {
	request := modelsV1.OnboardingTradeReadyStatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleOnboardingTradeReadyStatusRoute- error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleOnboardingTradeReadyStatusRoute- request validated successfully")

	// process the request.
	response, err := onboardingTradeReadyStatusBusinessLogic.OnboardingTradeReadyStatus(ctx, &request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleOnboardingTradeReadyStatusRoute- error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
