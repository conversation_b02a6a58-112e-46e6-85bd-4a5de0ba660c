package onboarding

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type OnboardingTradeReadyStatusBusinessLogic interface {
	OnboardingTradeReadyStatus(ctx context.Context, request *modelsV1.OnboardingTradeReadyStatusAPIRequest) (*modelsV1.OnboardingTradeReadyStatusResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var onboardingTradeReadyStatusBusinessLogic OnboardingTradeReadyStatusBusinessLogic

func SetOnboardingTradeReadyStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic OnboardingTradeReadyStatusBusinessLogic) {
	commonAPILogic = apiLogic
	onboardingTradeReadyStatusBusinessLogic = businessLogic
}
