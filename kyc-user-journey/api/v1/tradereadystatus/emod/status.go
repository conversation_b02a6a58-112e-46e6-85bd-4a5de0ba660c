package emod

import (
	"strconv"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleEmodTradeReadyStatusRoute godoc
// @Summary Emod Trade Ready Status Route
// @Description API to fetch status of Emod Trade Ready
// @ID HandleEmodTradeReadyStatusRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "spark"
// @Param X-platform header string true "ios"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v1/trade-ready/status/emod [GET].
func HandleEmodTradeReadyStatusRoute(ctx *gin.Context) {
	request := modelsV1.EmodTradeReadyStatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}

	request.IsDormant, _ = strconv.ParseBool(ctx.Query(constants.IsDormantQueryParam))
	request.KRAActionEnabled, _ = strconv.ParseBool(ctx.Query(constants.KRAActionEnabledQueryParam))
	request.NotPermittedToTrade, _ = strconv.ParseBool(ctx.Query(constants.NotPermittedToTradeQueryParam))
	request.KraTRS02, _ = strconv.ParseBool(ctx.Query(constants.KraTRS02QueryParam))

	err := request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodTradeReadyStatusRoute: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodTradeReadyStatusRoute - request validated successfully")
	response, err := emodTradeReadyStatusBusinessLogic.EmodTradeReadyStatus(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodTradeReadyStatusRoute: error getting response")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
