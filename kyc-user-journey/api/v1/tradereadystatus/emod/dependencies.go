package emod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type EmodTradeReadyStatusBusinessLogic interface {
	EmodTradeReadyStatus(ctx context.Context, req *modelsAPIV1.EmodTradeReadyStatusAPIRequest) (
		*modelsAPIV1.EmodTradeReadyStatusResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodTradeReadyStatusBusinessLogic EmodTradeReadyStatusBusinessLogic

func SetEmodTradeReadyStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodTradeReadyStatusBusinessLogic) {
	commonAPILogic = apiLogic
	emodTradeReadyStatusBusinessLogic = businessLogic
}
