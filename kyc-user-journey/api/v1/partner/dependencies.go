package partner

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type PartnerBusinessLogic interface {
	GetKYPDetails(ctx context.Context, request *modelsAPIv1.KYPRequest) (*modelsAPIv1.KYPResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var partnerBusinessLogic PartnerBusinessLogic

func SetPartnerBusinessLogic(commonAPIlogic CommonAPILogic, businessLogic PartnerBusinessLogic) {
	commonAPILogic = commonAPIlogic
	partnerBusinessLogic = businessLogic
}
