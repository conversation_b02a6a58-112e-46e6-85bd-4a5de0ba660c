package partner

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleKYP godoc
// @Summary Fetches sub broker details from NXT
// @Description Fetches sub broker details from NXT
// @ID HandleKYP
// @Tags partner
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/kyp [POST].
func HandleKYP(ctx *gin.Context) {
	request := modelsAPIv1.KYPRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleKYP - Invalid HandleKYP request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleKYP - error validating request ")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := partnerBusinessLogic.GetKYPDetails(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleKYP - error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
