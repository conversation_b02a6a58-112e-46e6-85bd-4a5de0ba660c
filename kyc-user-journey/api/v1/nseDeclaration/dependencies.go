package nsedeclaration

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

type NseDeclarationBusinessLogic interface {
	NseDeclaration(ctx context.Context, request *modelsV1.NseDeclarationAPIRequest) (*modelsV1.NseDeclarationAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var nseDeclarationBusinessLogic NseDeclarationBusinessLogic

func SetNseDeclarationBusinessLogic(apiLogic CommonAPILogic, businessLogic NseDeclarationBusinessLogic) {
	commonAPILogic = apiLogic
	nseDeclarationBusinessLogic = businessLogic
}
