package nsedeclaration

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

// HandleNseDeclarationRoute godoc
// @Summary NseDeclartion Route
// @Description API to post nse declaration in onboarding flow
// @ID HandleNseDeclarationRoute
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV1.EmailAPIRequest true "email request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v1/onboarding/nse-declaration [POST].
func HandleNseDeclarationRoute(ctx *gin.Context) {
	handleGenericNseDeclarationRoute(ctx)
}

func handleGenericNseDeclarationRoute(ctx *gin.Context) {
	request := modelsV1.NseDeclarationAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating nse declaration request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	response, err := nseDeclarationBusinessLogic.NseDeclaration(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing nse declaration request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
