package journeydecider

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/gin-gonic/gin"
)

type JourneyDeciderBusinessLogic interface {
	JourneyDecider(ctx context.Context, request *modelsApiV1.JourneyDeciderRequest) (*modelsApiV1.JourneyDeciderResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var journeyDeciderBusinessLogic JourneyDeciderBusinessLogic
var commonAPILogic CommonAPILogic

func SetJourneyDeciderBusinessLogic(commonLogic CommonAPILogic, businessLogic JourneyDeciderBusinessLogic) {
	commonAPILogic = commonLogic
	journeyDeciderBusinessLogic = businessLogic
}
