package journeydecider

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/gin-gonic/gin"
)

func HandleJourneyDeciderRoute(ctx *gin.Context) {
	var request modelsApiV1.JourneyDeciderRequest
	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)

	// validate the request.
	err := request.Validate()
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("HandleJourneyDecider: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	resp, err := journeyDeciderBusinessLogic.JourneyDecider(ctx, &request)
	if err != nil {
		log.Error(ctx).Err(err).Stack().Msg("HandleJourneyDecider: error processing request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, *resp))
}
