package emod

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

// HandleEmodStatusRoute  godoc
// @Summary EMOD Status Route
// @Description API to give status of emod applications
// @ID HandleEmodStatusRoute
// @Tags emod
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Authorization"
// @Param emod_type query string false "Emod intents"
// @Param status_type query string false "Application status"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v2/emod/status [GET].
func HandleEmodStatusRoute(ctx *gin.Context) {
	request := modelsV2.EmodStatusAPIRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindQuery(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodStatusRouteV2- error binding emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("HandleEmodStatusRouteV2- error validating emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Msg("HandleEmodStatusRouteV2- request validated successfully")

	// process the request.
	response, err := emodStatusBusinessLogic.EmodActiveApplicationsStatus(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleEmodStatusRouteV2- error processing emod status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
