package emod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

type EmodStatusBusinessLogic interface {
	EmodActiveApplicationsStatus(ctx context.Context, request *modelsV2.EmodStatusAPIRequest) (*modelsV2.EmodStatusAPIResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var emodStatusBusinessLogic EmodStatusBusinessLogic

func SetEmodStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic EmodStatusBusinessLogic) {
	commonAPILogic = apiLogic
	emodStatusBusinessLogic = businessLogic
}
