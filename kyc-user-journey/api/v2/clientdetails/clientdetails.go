package clientdetails

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

// HandleOnboardingClientDetailsRouteV2 godoc
// @Summary ClientDetails V2 Route
// @Description API to post client details in onboarding flow
// @ID HandleOnboardingClientDetailsRouteV2
// @Tags onboarding
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "Bearer token"
// @Param request body modelsV2.ClientDetailsV2APIRequest true "client details request"
// @Success 200 {object} models.Response
// @Failure 400 {object} models.Response
// @Failure 500 {object} models.Response
// @Router /v2/onboarding/client-details [POST].
func HandleOnboardingClientDetailsRouteV2(ctx *gin.Context) {
	handleGenericClientDetailsRouteV2(ctx)
}

func handleGenericClientDetailsRouteV2(ctx *gin.Context) {
	request := modelsV2.ClientDetailsAPIV2Request{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("Invalid Request Payload")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = request.Validate()
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error validating onboarding client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	err = clientDetailsV2BusinessLogic.ClientDetails(ctx, &request)
	if err != nil {
		log.ErrorWarn(ctx, err).Msg("error processing client details request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, nil))
}
