package clientdetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

type ClientDetailsV2BusinessLogic interface {
	ClientDetails(ctx context.Context, request *modelsV2.ClientDetailsAPIV2Request) error
}

var commonAPILogic CommonAPILogic
var clientDetailsV2BusinessLogic ClientDetailsV2BusinessLogic

func SetClientDetailsV2BusinessLogic(apiLogic CommonAPILogic, businessLogic ClientDetailsV2BusinessLogic) {
	commonAPILogic = apiLogic
	clientDetailsV2BusinessLogic = businessLogic
}
