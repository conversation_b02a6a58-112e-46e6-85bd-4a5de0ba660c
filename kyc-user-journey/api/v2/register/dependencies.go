package register

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

type RegisterV2BusinessLogic interface {
	Register(ctx context.Context, request *modelsV2.RegisterAPIV2Request) error
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var registerV2BusinessLogic RegisterV2BusinessLogic

func SetRegisterV2BusinessLogic(apiLogic CommonAPILogic, businessLogic RegisterV2BusinessLogic) {
	commonAPILogic = apiLogic
	registerV2BusinessLogic = businessLogic
}
