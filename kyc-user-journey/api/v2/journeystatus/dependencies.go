package journeystatus

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

type JourneyStatusBusinessLogic interface {
	GetJourneyStatus(ctx context.Context, request *modelsV2.JourneyStatusRequest) (*modelsV2.JourneyStatusResponse, error)
}

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var journeyStatusBusinessLogic JourneyStatusBusinessLogic

func SetJourneyStatusBusinessLogic(apiLogic CommonAPILogic, businessLogic JourneyStatusBusinessLogic) {
	commonAPILogic = apiLogic
	journeyStatusBusinessLogic = businessLogic
}
