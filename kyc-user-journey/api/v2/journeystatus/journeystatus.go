package journeystatus

import (
	"net/http"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsV2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

// HandleJourneyStatus godoc
// @Summary Handle Journey Status
// @Message Handle Journey Status
// @ID HandleJourneyStatus
// @Tags ui
// @Produce json
// @Param Accept-Language header string true "Accept Language"
// @Param X-source header string true "source"
// @Param AppVersion header string true "appVersion"
// @Param Authorization header string true "Bearer ${JWT}"
// @Success 200 {object} modelsV2.JourneyStatusResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /v2/ui/journey-status [get].
func HandleJourneyStatus(ctx *gin.Context) {
	// get the request
	request := modelsV2.JourneyStatusRequest{
		Headers: commonAPILogic.GetHeaders(ctx),
		Meta:    commonAPILogic.GetMeta(ctx),
	}
	// now validate the request
	err := request.Validate(ctx)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleJourneyStatus - error validating the request body")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	log.Debug(ctx).Interface(constants.LogRequestBodyKey, request).Interface(constants.Mobile, request.Meta.Mobile).Msg("HandleJourneyStatus - request validated successfully")
	// now process the request
	response, err := journeyStatusBusinessLogic.GetJourneyStatus(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleJourneyStatus - error processing journey status request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}
	ctx.JSON(http.StatusOK, response)
}
