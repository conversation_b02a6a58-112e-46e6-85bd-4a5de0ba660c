package accountclosure

import (
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/models"
	emodmodel2 "github.com/angel-one/kyc-user-journey/models/api/v2"
	"github.com/gin-gonic/gin"
)

// HandleAccountClosureStartV2 godoc
// @Summary Start account closure v2
// @Description account closure with S2S token generation
// @ID HandleAccountClosureStartV2
// @Tags accountclosure
// @Accept json
// @Produce json
// @Param X-source header string true "source"
// @Param X-appVersion header string true "appVersion"
// @Param X-platform header string true "platform"
// @Param Authorization header string true "authorization"
// @Success 200 {object} models.Response
// @Success 500 {object} models.Response
// @Router /v2/emod/acc-closure/start [POST].
func HandleAccountClosureStartV2(ctx *gin.Context) {
	var request emodmodel2.AccountClosureStartV2Request

	request.Headers = commonAPILogic.GetHeaders(ctx)
	request.Meta = commonAPILogic.GetMeta(ctx)
	log.Info(ctx).Str("clientCode", request.Meta.ClientCode).Msg("AccountClosureStartV2: attempt")

	// bind the request.
	err := ctx.ShouldBindJSON(&request)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountClosureStartV2: error binding request body")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// validate request.
	err = request.Validate()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureStartV2: error validating request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// process request.
	response, err := accountClosureStartLogic.ProcessRequest(ctx, &request)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("AccountClosureStartV2: error processing the request")
		ctx.JSON(models.GetErrorResponse(err))
		return
	}

	// success response
	ctx.JSON(models.GetOKSuccessResponseWithMasking(ctx, response))
}
