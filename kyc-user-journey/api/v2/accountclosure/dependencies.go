package accountclosure

import (
	"context"

	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	emodmodel2 "github.com/angel-one/kyc-user-journey/models/api/v2"

	"github.com/angel-one/kyc-user-journey/models"
	"github.com/gin-gonic/gin"
)

type CommonAPILogic interface {
	GetHeaders(ctx *gin.Context) models.Headers
	GetMeta(ctx *gin.Context) models.Meta
}

var commonAPILogic CommonAPILogic
var accountClosureStartLogic AccountClosureStartV2BusinessLogic

type AccountClosureStartV2BusinessLogic interface {
	ProcessRequest(ctx context.Context,
		request *emodmodel2.AccountClosureStartV2Request) (*emodmodel.AccountClosureStartResponse, error)
	ValidateRequest(ctx context.Context, request *emodmodel.ValidateAccountClosureRequest) error
}

func SetAccountClosureStartV2Logic(apiLogic CommonAPILogic,
	businessLogic AccountClosureStartV2BusinessLogic) {
	commonAPILogic = apiLogic
	accountClosureStartLogic = businessLogic
}
