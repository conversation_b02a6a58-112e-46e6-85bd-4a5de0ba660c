// externals/hypervergeEsign.go

package externals

import (
	"context"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils/http"
)

type HypervergeESignExternalProvider struct{}

func NewHypervergeESignExternalProvider() *HypervergeESignExternalProvider {
	return &HypervergeESignExternalProvider{}
}

func (h *HypervergeESignExternalProvider) CallInputAPI(ctx context.Context, req *modelsExternals.HypervergeInputRequest) (*modelsExternals.HypervergeInputResponse, error) {
	var resp modelsExternals.HypervergeInputResponse
	httpResp := http.Request(ctx, constants.HypervergeESignInputAPI).
		SetBodyJsonMarshal(req).
		SetSuccessResult(&resp).
		Do()
	if httpResp.IsSuccessState() {
		return &resp, nil
	}
	return nil, httpResp.Err
}

func (h *HypervergeESignExternalProvider) CallOutputAPI(ctx context.Context, req *modelsExternals.HypervergeOutputRequest) (*modelsExternals.HypervergeOutputResponse, error) {
	var resp modelsExternals.HypervergeOutputResponse
	httpResp := http.Request(ctx, constants.HypervergeESignOutputAPI).
		SetBodyJsonMarshal(req).
		SetSuccessResult(&resp).
		Do()
	if httpResp.IsSuccessState() {
		return &resp, nil
	}
	return nil, httpResp.Err
}
