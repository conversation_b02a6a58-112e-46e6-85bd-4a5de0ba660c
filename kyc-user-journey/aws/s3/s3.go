package s3

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/url"
	"time"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

var s3Client = &Client{}

// Config is the set of configurable for s3.
type Config struct {
	Bucket string
}

type Client struct {
	config *Config
	*s3.Client
	*s3.PresignClient
}

func Init(ctx context.Context) {
	s3Config := Config{Bucket: flags.AWSBucket()}
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}
	s3Client.initialiseClient(&s3Config, &awsConf)
}

func (c *Client) initialiseClient(s3Config *Config, awsConf *aws.Config) {
	client := s3.NewFromConfig(*awsConf)
	c.config = s3Config
	c.Client = client
	c.PresignClient = s3.NewPresignClient(client)
}

func GetS3Client() *Client {
	return s3Client
}

func (c *Client) GetSignedURL(ctx context.Context, objectKey string, expiryDuration time.Duration) (string, error) {
	result, err := c.PresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(objectKey),
	}, func(o *s3.PresignOptions) {
		o.Expires = expiryDuration
	})
	if err != nil {
		return constants.Empty, err
	}
	return result.URL, nil
}

// Upload is used to upload a file.
func (c *Client) Upload(ctx context.Context, key string, file io.Reader) error {
	_, err := c.PutObject(ctx, &s3.PutObjectInput{
		Body:   file,
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) UploadBase64File(ctx context.Context, path string, data []byte) error {
	decode := make([]byte, base64.StdEncoding.DecodedLen(len(data)))
	_, err := base64.StdEncoding.Decode(decode, data)
	if err != nil {
		return err
	}
	return c.Upload(ctx, path, bytes.NewReader(decode))
}

// Copy is used to copy a file.
func (c *Client) Copy(ctx context.Context, source, destination string) error {
	_, err := c.Client.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(c.config.Bucket),
		CopySource: aws.String(url.PathEscape(fmt.Sprintf("%s/%s", c.config.Bucket, source))),
		Key:        aws.String(destination),
	})
	return err
}

func (c *Client) Download(ctx context.Context, key string) (*s3.GetObjectOutput, error) {
	resp, err := c.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) Delete(ctx context.Context, objectKeys []string) error {
	if len(objectKeys) == 0 {
		return nil
	}
	objectsToDelete := make([]types.ObjectIdentifier, 0, len(objectKeys))
	for _, key := range objectKeys {
		objectsToDelete = append(objectsToDelete, types.ObjectIdentifier{Key: aws.String(key)})
	}
	_, err := c.Client.DeleteObjects(ctx, &s3.DeleteObjectsInput{
		Bucket: aws.String(c.config.Bucket),
		Delete: &types.Delete{
			Objects: objectsToDelete,
			Quiet:   true,
		},
	})
	if err != nil {
		return fmt.Errorf("S3Delete: error deleting objects from S3: %w", err)
	}
	return nil
}
func (c *Client) ListObjectsWithPrefix(ctx context.Context, prefix string) ([]string, error) {
	output, err := c.ListObjectsV2(ctx, &s3.ListObjectsV2Input{
		Bucket: aws.String(c.config.Bucket),
		Prefix: aws.String(prefix),
	})
	if err != nil {
		return nil, fmt.Errorf("S3ListObjectsWithPrefix: error listing S3 objects: %w", err)
	}
	objectKeys := make([]string, 0, len(output.Contents))
	for _, obj := range output.Contents {
		objectKeys = append(objectKeys, *obj.Key)
	}
	return objectKeys, nil
}
