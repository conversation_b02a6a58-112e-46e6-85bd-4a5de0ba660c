package s3

import (
	"context"
	"fmt"
	"time"

	"github.com/angel-one/kyc-user-journey/config"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

var s3FulfilmentClient = &FulfilmentClient{}

type FulfilmentClient struct {
	config *FulfilmentConfig
	*s3.Client
	*s3.PresignClient
}

// FulfilmentConfig Config is the set of configurable for s3.
type FulfilmentConfig struct {
	Bucket      string
	AccessKeyID string
	SecretKey   string
	Region      string
}

func FulfilmentInit(ctx context.Context) {
	s3FulfilmentConfig := getS3FulfilmentConfig()
	awsConf, err := awsconfig.LoadDefaultConfig(ctx, awsconfig.WithRegion(s3FulfilmentConfig.Region))
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}
	s3FulfilmentClient.initialiseFulfilmentClient(&s3FulfilmentConfig, &awsConf)
}

func (c *FulfilmentClient) initialiseFulfilmentClient(s3Config *FulfilmentConfig, awsConf *aws.Config) {
	client := s3.NewFromConfig(*awsConf)
	c.config = s3Config
	c.Client = client
	c.PresignClient = s3.NewPresignClient(client)
}

func GetFulfilmentS3Client() *FulfilmentClient {
	return s3FulfilmentClient
}

func (c *FulfilmentClient) GetFulfilmentSignedURL(ctx context.Context, objectKey string, expiryDuration time.Duration) (string, error) {
	result, err := c.PresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.config.Bucket),
		Key:    aws.String(objectKey),
	}, func(o *s3.PresignOptions) {
		o.Expires = expiryDuration
	})
	if err != nil {
		return constants.Empty, err
	}
	return result.URL, nil
}

func getS3FulfilmentConfig() FulfilmentConfig {
	// Improvements : read these from env variables
	return FulfilmentConfig{
		Bucket:      config.S3().S3Fulfilment.Bucket,
		AccessKeyID: config.S3().S3Fulfilment.AccessKeyID,
		SecretKey:   config.S3().S3Fulfilment.SecretKey,
		Region:      config.S3().S3Fulfilment.Region,
	}
}
