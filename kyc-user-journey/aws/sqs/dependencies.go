package sqs

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type LSQBusinessLogic interface {
	TriggerLSQ(ctx context.Context, lsqMessage *business.LSQMessage) error
}

type PrismSQSBusinessLogic interface {
	HandlePrismEvent(ctx context.Context, prismEventMessage *business.PrismEventMessage) error
}

type OnboardingCompleteBusinessLogic interface {
	HandleOnboardingCompleteMessage(ctx context.Context, onboardingCompleteMessage *business.OnboardingCompleteMessage) error
	HandleNriOnboardingCompleteMessage(ctx context.Context, nriOnboardingCompleteMessage *business.NriOnboardingCompleteMessage) error
}

func (c *KycUserJourneyClient) SetSQSBusinessLogic(lsqBusinessLogic LSQBusinessLogic, onboardingCompleteBusinessLogic OnboardingCompleteBusinessLogic) {
	c.lsq = lsqBusinessLogic
	c.onboardingComplete = onboardingCompleteBusinessLogic
}

func (c *PrismClient) SetSQSBusinessLogic(prismSQSBusinessLogic PrismSQSBusinessLogic) {
	c.prismSQSBusinessLogic = prismSQSBusinessLogic
}
