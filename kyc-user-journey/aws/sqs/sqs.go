package sqs

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	goUtils "github.com/angel-one/go-utils"
	userJourneyConfig "github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

var sqsClients = &Clients{
	KycUserJourney: &KycUserJourneyClient{},
	Attribution:    &AttributionClient{},
	Prism:          &PrismClient{},
}

type Clients struct {
	KycUserJourney *KycUserJourneyClient
	Attribution    *AttributionClient
	Prism          *PrismClient
}

type KycUserJourneyClient struct {
	Client
	lsq                LSQBusinessLogic
	onboardingComplete OnboardingCompleteBusinessLogic
}

type PrismClient struct {
	Client
	prismSQSBusinessLogic PrismSQSBusinessLogic
}

type AttributionClient struct {
	Client
}

// Client is the client for rekognition.
type Client struct {
	config *Config
	*sqs.Client
}

// Config is the set of configurable for sqs.
type Config struct {
	QueueURL string
	Receiver ReceiverConfig
}

// ReceiverConfig is the config for message receiver.
type ReceiverConfig struct {
	TickerDuration             time.Duration
	MaxNumberOfMessages        int32 // this also denotes the batch size for the processing
	VisibilityTimeoutInSeconds int32
	WaitTimeInSeconds          int32
	NumberOfReceivers          int // this denotes number of receivers to receive the messages
}

func GetClients() *Clients {
	return sqsClients
}

func Init(ctx context.Context) {
	initKycUserJourneyClient(ctx)
	initAttributionClient(ctx)
	initPrismClient(ctx)
}

func initKycUserJourneyClient(ctx context.Context) {
	userJourneySqsConfig := userJourneyConfig.SQS().KycUserJourney
	sqsConfig := Config{
		QueueURL: userJourneySqsConfig.QueueURL,
		Receiver: ReceiverConfig{
			TickerDuration:             time.Duration(userJourneySqsConfig.Receiver.TickerDurationInSeconds) * time.Second,
			MaxNumberOfMessages:        userJourneySqsConfig.Receiver.MaxNumberOfMessages,
			VisibilityTimeoutInSeconds: userJourneySqsConfig.Receiver.VisibilityTimeoutInSeconds,
			WaitTimeInSeconds:          userJourneySqsConfig.Receiver.WaitTimeInSeconds,
			NumberOfReceivers:          userJourneySqsConfig.Receiver.NumberOfReceivers,
		},
	}
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}

	sqsClients.KycUserJourney.initialiseClient(&sqsConfig, &awsConf)
	sqsClients.KycUserJourney.startReceivers(ctx)
}

func initPrismClient(ctx context.Context) {
	prismSqsConfig := userJourneyConfig.SQS().Prism
	sqsConfig := Config{
		QueueURL: prismSqsConfig.QueueURL,
		Receiver: ReceiverConfig{
			TickerDuration:             time.Duration(prismSqsConfig.Receiver.TickerDurationInSeconds) * time.Second,
			MaxNumberOfMessages:        prismSqsConfig.Receiver.MaxNumberOfMessages,
			VisibilityTimeoutInSeconds: prismSqsConfig.Receiver.VisibilityTimeoutInSeconds,
			WaitTimeInSeconds:          prismSqsConfig.Receiver.WaitTimeInSeconds,
			NumberOfReceivers:          prismSqsConfig.Receiver.NumberOfReceivers,
		},
	}
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}

	sqsClients.Prism.initialiseClient(&sqsConfig, &awsConf)
	sqsClients.Prism.startReceivers(ctx)
}

func initAttributionClient(ctx context.Context) {
	attributionSqsConfig := userJourneyConfig.SQS().Attribution
	sqsConfig := Config{
		QueueURL: attributionSqsConfig.QueueURL,
		Receiver: ReceiverConfig{
			TickerDuration:             time.Duration(attributionSqsConfig.Receiver.TickerDurationInSeconds) * time.Second,
			MaxNumberOfMessages:        attributionSqsConfig.Receiver.MaxNumberOfMessages,
			VisibilityTimeoutInSeconds: attributionSqsConfig.Receiver.VisibilityTimeoutInSeconds,
			WaitTimeInSeconds:          attributionSqsConfig.Receiver.WaitTimeInSeconds,
			NumberOfReceivers:          attributionSqsConfig.Receiver.NumberOfReceivers,
		},
	}
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}

	sqsClients.Attribution.initialiseClient(&sqsConfig, &awsConf)
}

func (c *Client) initialiseClient(sqsConfig *Config, awsConf *aws.Config) {
	c.config = sqsConfig
	c.Client = sqs.NewFromConfig(*awsConf)
}

func (c *KycUserJourneyClient) startReceivers(ctx context.Context) {
	i := sqs.ReceiveMessageInput{
		AttributeNames: []types.QueueAttributeName{
			types.QueueAttributeNameAll,
		},
		MessageAttributeNames: []string{
			"All",
		},
		MaxNumberOfMessages: c.config.Receiver.MaxNumberOfMessages,
		QueueUrl:            aws.String(c.config.QueueURL),
		VisibilityTimeout:   c.config.Receiver.VisibilityTimeoutInSeconds,
		WaitTimeSeconds:     c.config.Receiver.WaitTimeInSeconds,
	}

	for ctr := 1; ctr <= c.config.Receiver.NumberOfReceivers; ctr++ {
		go c.startReceiver(ctx, &i, fmt.Sprintf("%s-%s", "kyc-user-journey-sqs-receiver", strconv.Itoa(ctr)))
	}
}

func (c *KycUserJourneyClient) startReceiver(ctx context.Context, i *sqs.ReceiveMessageInput, receiverID string) {
	t := time.NewTicker(c.config.Receiver.TickerDuration)
	defer t.Stop()
	rateLimiter := t.C

	for {
		<-rateLimiter // Wait for the next tick

		output, err := c.Client.ReceiveMessage(ctx, i)
		if err != nil {
			log.Error(ctx).Str("receiverID", receiverID).Err(err).Msg("error receiving messages")
			continue
		}

		wg := sync.WaitGroup{}
		for _, m := range output.Messages {
			wg.Add(1)
			go c.processMessage(ctx, &wg, m, receiverID)
		}
		wg.Wait()
	}
}

func (c *KycUserJourneyClient) processMessage(ctx context.Context, wg *sync.WaitGroup, m types.Message, receiverID string) {
	defer func() {
		if r := recover(); r != nil {
			log.Error(ctx).Str("receiverID", receiverID).Interface("recover", r).Msg("unhandled error")
		}
	}()
	defer wg.Done()
	log.Debug(ctx).Interface("message", m).Str("receiverID", receiverID).Msg("processing SQS message")

	messageTypeAttribute, ok := m.MessageAttributes["MessageType"]
	if ok {
		messageType := *messageTypeAttribute.StringValue
		switch messageType {
		case "LSQ":
			messageDTO, err := getLSQMessageData(&m)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error getting lsq message DTO for sqs processing")
				return
			}
			log.Info(ctx).Interface("receiptHandle", m.ReceiptHandle).Msg("processing LSQ SQS message")
			err = c.lsq.TriggerLSQ(ctx, messageDTO)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error in processing LSQ SQS message")
				return
			}
		case "OnboardingComplete":
			messageDTO, err := getOnboardingCompleteMessageData(&m)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error getting onboarding complete message DTO for sqs processing")
				return
			}
			log.Info(ctx).Interface("receiptHandle", m.ReceiptHandle).Msg("processing onboarding complete SQS message")
			err = c.onboardingComplete.HandleOnboardingCompleteMessage(ctx, messageDTO)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error in processing onboarding complete SQS message")
				return
			}
		case "NRIOnboardingComplete":
			messageDTO, err := getNriOnboardingCompleteMessage(&m)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error getting NRI onboarding complete message DTO for sqs processing")
				return
			}
			log.Info(ctx).Interface("receiptHandle", m.ReceiptHandle).Msg("processing NRI onboarding complete SQS message")
			err = c.onboardingComplete.HandleNriOnboardingCompleteMessage(ctx, messageDTO)
			if err != nil {
				log.Error(ctx).Err(err).Interface("receiptHandle", m.ReceiptHandle).Msg("Error in processing NRI onboarding complete SQS message")
				return
			}
		}

		// delete the message to avoid re-push
		log.Info(ctx).Str("receiverID", receiverID).Interface("receiptHandle", m.ReceiptHandle).Msg("Deleting the sqs message")
		_, err := c.DeleteMessage(ctx, &sqs.DeleteMessageInput{
			QueueUrl:      aws.String(c.config.QueueURL),
			ReceiptHandle: m.ReceiptHandle,
		})
		if err != nil {
			log.Error(ctx).Err(err).Str("receiverID", receiverID).Msgf("error deleting message %v", m)
		}
		log.Info(ctx).Str("receiverID", receiverID).Interface("receiptHandle", m.ReceiptHandle).Msg("Successfully Deleted SQS message")
	}
}

func getLSQMessageData(m *types.Message) (*business.LSQMessage, error) {
	var data business.LSQMessage
	if m == nil || m.Body == nil {
		return nil, constants.ErrNoMessageBody
	}
	err := goUtils.UnmarshalJSON([]byte(*m.Body), &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}
func getOnboardingCompleteMessageData(m *types.Message) (*business.OnboardingCompleteMessage, error) {
	var data business.OnboardingCompleteMessage
	if m == nil || m.Body == nil {
		return nil, constants.ErrNoMessageBody
	}
	err := goUtils.UnmarshalJSON([]byte(*m.Body), &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func getNriOnboardingCompleteMessage(m *types.Message) (*business.NriOnboardingCompleteMessage, error) {
	var data business.NriOnboardingCompleteMessage
	if m == nil || m.Body == nil {
		return nil, constants.ErrNoMessageBody
	}
	err := goUtils.UnmarshalJSON([]byte(*m.Body), &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (c *KycUserJourneyClient) SendMessage(ctx context.Context, queueURL, messageBody, groupID, dedupID string, messageAttributes map[string]types.MessageAttributeValue) error {
	_, err := c.Client.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:               aws.String(queueURL),
		MessageBody:            aws.String(messageBody),
		MessageAttributes:      messageAttributes,
		MessageDeduplicationId: aws.String(dedupID),
		MessageGroupId:         aws.String(groupID),
	})
	return err
}

func (c *AttributionClient) SendMessage(ctx context.Context, queueURL, messageBody string, messageAttributes map[string]types.MessageAttributeValue) error {
	_, err := c.Client.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:          aws.String(queueURL),
		MessageBody:       aws.String(messageBody),
		MessageAttributes: messageAttributes,
	})
	return err
}

func (c *PrismClient) startReceivers(ctx context.Context) {
	i := sqs.ReceiveMessageInput{
		AttributeNames: []types.QueueAttributeName{
			types.QueueAttributeNameAll,
		},
		MessageAttributeNames: []string{
			"All",
		},
		MaxNumberOfMessages: c.config.Receiver.MaxNumberOfMessages,
		QueueUrl:            aws.String(c.config.QueueURL),
		VisibilityTimeout:   c.config.Receiver.VisibilityTimeoutInSeconds,
		WaitTimeSeconds:     c.config.Receiver.WaitTimeInSeconds,
	}

	for ctr := 1; ctr <= c.config.Receiver.NumberOfReceivers; ctr++ {
		go c.startReceiver(ctx, &i, fmt.Sprintf("%s-%s", "prism-sqs-receiver", strconv.Itoa(ctr)))
	}
}

func (c *PrismClient) startReceiver(ctx context.Context, i *sqs.ReceiveMessageInput, receiverID string) {
	t := time.NewTicker(c.config.Receiver.TickerDuration)
	defer t.Stop()
	rateLimiter := t.C

	for {
		<-rateLimiter // Wait for the next tick

		output, err := c.Client.ReceiveMessage(ctx, i)
		if err != nil {
			log.Error(ctx).Str("receiverID", receiverID).Err(err).Msg("error receiving messages")
			continue
		}

		wg := sync.WaitGroup{}
		for _, m := range output.Messages {
			wg.Add(1)
			go c.processMessage(ctx, &wg, m, receiverID)
		}
		wg.Wait()
	}
}

func (c *PrismClient) processMessage(ctx context.Context, wg *sync.WaitGroup, m types.Message, receiverID string) {
	log.Debug(ctx).Interface("message", m).Str("receiverID", receiverID).Msg("processing SQS message")
	defer func() {
		if r := recover(); r != nil {
			log.Error(ctx).Str("receiverID", receiverID).Interface("recover", r).Msg("unhandled error")
		}
	}()
	defer wg.Done()

	if _, ok := m.MessageAttributes["processInstanceId"]; ok {
		messageDTO, err := getPrismMessageData(&m)
		if err != nil {
			return
		}
		err = c.prismSQSBusinessLogic.HandlePrismEvent(ctx, messageDTO)
		if err != nil {
			return
		}

		// delete the message to avoid re-push
		_, err = c.DeleteMessage(ctx, &sqs.DeleteMessageInput{
			QueueUrl:      aws.String(c.config.QueueURL),
			ReceiptHandle: m.ReceiptHandle,
		})
		if err != nil {
			log.Error(ctx).Err(err).Str("receiverID", receiverID).Msgf("error deleting message %v", m)
		}
	} else if messageTypeAttribute, ok := m.MessageAttributes["MessageType"]; ok {
		messageType := *messageTypeAttribute.StringValue
		if messageType == "prism" {
			messageDTO, err := getPrismMessageData(&m)
			if err != nil {
				return
			}
			err = c.prismSQSBusinessLogic.HandlePrismEvent(ctx, messageDTO)
			if err != nil {
				return
			}

			// delete the message to avoid re-push
			_, err = c.DeleteMessage(ctx, &sqs.DeleteMessageInput{
				QueueUrl:      aws.String(c.config.QueueURL),
				ReceiptHandle: m.ReceiptHandle,
			})
			if err != nil {
				log.Error(ctx).Err(err).Str("receiverID", receiverID).Msgf("error deleting message %v", m)
			}
		}
	}
}

func getPrismMessageData(m *types.Message) (*business.PrismEventMessage, error) {
	var data business.PrismEventMessage
	if m == nil || m.Body == nil {
		return nil, constants.ErrNoMessageBody
	}
	err := goUtils.UnmarshalJSON([]byte(*m.Body), &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (c *PrismClient) SendMessage(ctx context.Context, queueURL, messageBody, groupID, dedupID string, messageAttributes map[string]types.MessageAttributeValue) error {
	_, err := c.Client.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:               aws.String(queueURL),
		MessageBody:            aws.String(messageBody),
		MessageAttributes:      messageAttributes,
		MessageDeduplicationId: aws.String(dedupID),
		MessageGroupId:         aws.String(groupID),
	})
	return err
}
