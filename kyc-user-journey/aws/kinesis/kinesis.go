package kinesis

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/kinesis"
)

// Config is the set of configurations for Kinesis.
type Config struct {
	StreamName string
}

// Message is the details for the message to be sent to Kinesis.
type Message struct {
	StreamName   string
	PartitionKey string
	EventData    any
}

// Client is the client for Kinesis.
type Client struct {
	*kinesis.Client
}

var client = &Client{}

func Init(ctx context.Context) {
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}
	client.initialiseClient(&awsConf)
}

func (c *Client) initialiseClient(awsConf *aws.Config) {
	client := kinesis.NewFromConfig(*awsConf)
	c.Client = client
}

// Get returns the Kinesis client.
func Get() *Client {
	return client
}

// PublishMessage sends an event message to the specified Kinesis stream.
func (c *Client) PublishMessage(ctx context.Context, message Message) error {
	// Marshal the EventData into JSON.
	jsonData, err := json.Marshal(message.EventData)
	if err != nil {
		return fmt.Errorf("error marshaling event data: %w", err)
	}

	// Send the message to the Kinesis stream.
	input := &kinesis.PutRecordInput{
		Data:         jsonData,
		StreamName:   aws.String(message.StreamName),
		PartitionKey: aws.String(message.PartitionKey),
	}

	result, err := c.PutRecord(ctx, input)
	if err != nil {
		return fmt.Errorf("error publishing message to Kinesis: %w", err)
	}
	log.Info(ctx).Msgf("Successfully sent event to Kinesis with sequence number: %+v, event data: %+v, shardId: %+v", *result.SequenceNumber, message.EventData, *result.ShardId)

	return nil
}
