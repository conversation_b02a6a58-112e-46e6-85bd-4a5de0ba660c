package rekognition

import (
	"context"
	"fmt"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/rekognition"
	"github.com/aws/aws-sdk-go-v2/service/rekognition/types"
)

var rekognitionClient = &Client{}

// Config is the set of configurable for rekognition.
type Config struct {
	Bucket string
}

// Client is the client for rekognition.
type Client struct {
	config *Config
	*rekognition.Client
}

// Label is the identity for a label.
type Label struct {
	Name       string
	Confidence float32
}

type FaceMatchResponse struct {
	Similarity float32
	Confidence float32
}

func Init(ctx context.Context) {
	rekognitionConfig := Config{Bucket: flags.AWSBucket()}
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}
	rekognitionClient.initialiseClient(&rekognitionConfig, &awsConf)
}

func (c *Client) initialiseClient(rekognitionConfig *Config, awsConf *aws.Config) {
	c.config = rekognitionConfig
	c.Client = rekognition.NewFromConfig(*awsConf)
}

func GetRekognitionClient() *Client {
	return rekognitionClient
}

// DetectLabelsInImage is used to detect labels.
func (c *Client) DetectLabelsInImage(ctx context.Context, key string, minConfidence float32) ([]Label, error) {
	o, err := c.DetectLabels(ctx, &rekognition.DetectLabelsInput{
		Image: &types.Image{
			S3Object: &types.S3Object{
				Bucket: aws.String(c.config.Bucket),
				Name:   aws.String(key),
			},
		},
		MinConfidence: aws.Float32(minConfidence),
	})
	if err != nil {
		return nil, err
	}
	labels := make([]Label, 0)
	for _, l := range o.Labels {
		if l.Name != nil {
			labels = append(labels, Label{
				Name:       *l.Name,
				Confidence: *l.Confidence,
			})
		}
	}
	return labels, nil
}

// FaceComparisonForImage will compare the face and return the confidence and similarity.
func (c *Client) FaceComparisonForImage(ctx context.Context, sourceFilePath, destinationFilePath string, minSimilarity int64) (FaceMatchResponse, error) {
	o, err := c.CompareFaces(ctx, &rekognition.CompareFacesInput{
		SourceImage: &types.Image{
			S3Object: &types.S3Object{
				Bucket: aws.String(c.config.Bucket),
				Name:   aws.String(sourceFilePath),
			},
		},
		TargetImage: &types.Image{
			S3Object: &types.S3Object{
				Bucket: aws.String(c.config.Bucket),
				Name:   aws.String(destinationFilePath),
			},
		},
		SimilarityThreshold: aws.Float32(float32(minSimilarity)),
	})
	if err != nil {
		return FaceMatchResponse{}, err
	}
	var faceMatchResponse FaceMatchResponse
	// loop over through face matches and extract the similarity value.
	if len(o.FaceMatches) == 0 {
		return FaceMatchResponse{}, constants.ErrAWSRekognitionEmpty.WithDetails("No faces are found")
	}
	faceMatchResponse.Similarity = *o.FaceMatches[0].Similarity
	faceMatchResponse.Confidence = *o.SourceImageFace.Confidence
	return faceMatchResponse, nil
}
