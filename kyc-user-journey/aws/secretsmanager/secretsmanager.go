package secretsmanager

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"gopkg.in/yaml.v3"
)

var secretsManagerClient = &Client{}

// Client is the client for Secrets Manager.
type Client struct {
	*secretsmanager.Client
	PIIEncryptionKeys map[string]string
}

// InitTestMode loads PII encryption keys from secret.yml for testing.
func InitSecretsManagerTestMode(ctx context.Context, directory string) {
	secretName := fmt.Sprintf(constants.PIIEncryptionKeysSecretName, flags.Env())
	file, err := os.ReadFile(directory + "/" + secretName + ".yml")
	if err != nil {
		log.Fatal(ctx).Err(err).Stack().Msg("error initialising secrets manager")
	}

	var secrets map[string]map[string]string
	if err = yaml.Unmarshal([]byte(os.ExpandEnv(string(file))), &secrets); err != nil {
		log.Fatal(ctx).Err(err).Stack().Msg("error initialising secrets manager")
	}

	secretValue, exists := secrets[secretName]
	if !exists {
		log.Fatal(ctx).Err(err).Stack().Msgf("secret %s not found in YAML", secretName)
	}

	secretsManagerClient.PIIEncryptionKeys = secretValue
}

func InitSecretsManager(ctx context.Context) {
	awsConf, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		fmt.Println("Error loading AWS config:", err)
		panic(err)
	}

	secretsManagerClient.initialiseClient(&awsConf)

	// Now, initialise the PII encryption keys
	if err := secretsManagerClient.initEncryptionKeys(ctx, fmt.Sprintf(constants.PIIEncryptionKeysSecretName, flags.Env())); err != nil {
		log.Fatal(ctx).Stack().Err(err).Msg("error getting PII Encryption Keys")
	}
}

// Init initializes the Secrets Manager client.
func Init(ctx context.Context) {
	if flags.Mode() == constants.TestMode {
		InitSecretsManagerTestMode(ctx, flags.BaseConfigPath())
	} else if flags.Mode() == constants.ReleaseMode {
		InitSecretsManager(ctx)
	}
}

func (c *Client) initialiseClient(awsConf *aws.Config) {
	c.Client = secretsmanager.NewFromConfig(*awsConf)
}

// GetSecretsManagerClient returns the singleton instance of the SecretsManager client.
func GetSecretsManagerClient() *Client {
	return secretsManagerClient
}

func GetPIIEncryptionKey(keyID string) (string, bool) {
	key, exists := secretsManagerClient.PIIEncryptionKeys[keyID]
	return key, exists
}

// GetSecretValue retrieves a secret value from AWS Secrets Manager.
func (c *Client) GetSecretValue(ctx context.Context, secretName string) (map[string]string, error) {
	secretsMap := make(map[string]string)

	// Call Secrets Manager to retrieve the secret value.
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}

	result, err := c.Client.GetSecretValue(ctx, input)
	if err != nil {
		log.Error(ctx).Err(err).Msgf("failed to retrieve secret: %v", err)
		return nil, err
	}

	// Check if the secret is in string format.
	if result.SecretString != nil {
		secretString := *result.SecretString
		if err = json.Unmarshal([]byte(secretString), &secretsMap); err != nil {
			log.Error(ctx).Err(err).Msgf("failed to unmarshal secret string: %v", err)
			return nil, err
		}
	} else {
		log.Error(ctx).Err(err).Msg("secret is not in string format")
		return nil, constants.ErrInValidSecret.Value()
	}

	return secretsMap, nil
}

// InitEncryptionKeys initializes the encryption keys from Secrets Manager.
func (c *Client) initEncryptionKeys(ctx context.Context, secretName string) error {
	keys, err := c.GetSecretValue(ctx, secretName)
	if err != nil {
		return err
	}

	// Save the encryption keys into the client.
	c.PIIEncryptionKeys = keys
	return nil
}
