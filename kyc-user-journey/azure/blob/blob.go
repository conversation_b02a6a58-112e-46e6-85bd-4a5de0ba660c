package blob

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/angel-one/kyc-user-journey/config"

	"github.com/angel-one/kyc-user-journey/constants"

	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/sas"
)

var blobClient = &Client{}

type Client struct {
	*azblob.SharedKeyCredential
	*azblob.Client
	accountURL string
}

func Init() {
	blobClient.initialiseClient(config.Azure().AccountName, config.Azure().AccountKey)
}

func (c *Client) initialiseClient(accountName, accountKey string) {
	credential, err := azblob.NewSharedKeyCredential(accountName, accountKey)
	if err != nil {
		fmt.Printf("failed to obtain a credential: %v\n", err)
		return
	}
	c.SharedKeyCredential = credential
	c.accountURL = fmt.Sprintf(config.Azure().BlobURL, accountName)
	serviceClient, err := azblob.NewClientWithSharedKeyCredential(c.accountURL, credential, nil)
	if err != nil {
		fmt.Println("Error creating azure blob client :", err)
		// do nothing, can add panic
		// panic(err)
	}
	c.Client = serviceClient
}

func GetBlobClient() *Client {
	return blobClient
}

func (c *Client) GetSignedBlobURL(ctx context.Context, containerName, blobPrefix string, expiryInSeconds int) (string, error) {
	// List blobs
	pager := c.Client.NewListBlobsFlatPager(containerName, &azblob.ListBlobsFlatOptions{
		Include: container.ListBlobsInclude{},
		Prefix:  &blobPrefix,
	})
	var blobs []*container.BlobItem
	for pager.More() {
		resp, err := pager.NextPage(ctx)
		if err != nil {
			return constants.Empty, err
		}
		blobs = append(blobs, resp.Segment.BlobItems...)
	}

	urls := []string{}
	if len(blobs) == 0 {
		return constants.Empty, constants.ErrBlobNotFound.Value()
	}
	for _, blob := range blobs {
		blobName := *blob.Name
		// Check and encode blob name
		encodedBlobName := url.PathEscape(blobName)
		blobURL := c.accountURL + containerName + "/" + encodedBlobName
		sasTokenEncode, err := c.getBlobReadSASToken(containerName, expiryInSeconds)
		if err != nil {
			return constants.Empty, err
		}
		blobURLWithSAS := fmt.Sprintf("%s?%s", blobURL, sasTokenEncode)
		urls = append(urls, blobURLWithSAS)
	}
	return urls[0], nil
}

func (c *Client) getBlobReadSASToken(containerName string, expiryInSeconds int) (string, error) {
	startTime := time.Now().UTC().Add(-constants.TWENTYFIVE * time.Minute)
	endTime := time.Now().UTC().Add(time.Duration(expiryInSeconds) * time.Second)
	sasPermission := sas.ContainerPermissions{
		Read: true,
	}
	sasValues := sas.BlobSignatureValues{
		StartTime:     startTime,
		ExpiryTime:    endTime,
		Permissions:   sasPermission.String(),
		ContainerName: containerName,
	}
	sasToken, err := sasValues.SignWithSharedKey(c.SharedKeyCredential)
	if err != nil {
		return constants.Empty, err
	}
	return sasToken.Encode(), nil
}
