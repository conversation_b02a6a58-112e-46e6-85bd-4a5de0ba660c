package models

type HypervergeESign struct {
	//Redirect                     HypervergeESignRedirect `json:"redirect" validate:"required"`
	//ESignReason                  string                  `json:"eSignReason" validate:"required"`
	//DefaultESignLocation         string                  `json:"defaultESignLocation" validate:"required"`
	ErrorCodeToFailureMessageMap map[string]string   `json:"errorCodeToFailureMessageMap" validate:"required"`
	Flow                         HypervergeESignFlow `json:"flow" validate:"required"`
}

type HypervergeESignRedirect struct {
	URL            string `json:"url" validate:"required"`
	FailureMessage string `json:"failureMessage" validate:"required"`
}

type HypervergeESignFlow struct {
	Onboarding HypervergeESignFlowConfig `json:"onboarding" validate:"required"`
}
type HypervergeESignFlowConfig struct {
	FileName            string                         `json:"fileName" validate:"required"`
	SigningConfigID     HypervergeESignSigningConfigID `json:"signingConfigID" validate:"required"`
	GeolocationFlowName string                         `json:"geolocationFlowName" validate:"required"`
	CallBackURL         string                         `json:"callbackURL" validate:"required"`
	Redirect            HypervergeESignRedirectConfig  `json:"redirect" validate:"required"`
	RedirectAdmin       HypervergeESignRedirectConfig  `json:"redirectAdmin" validate:"required"`
}

type HypervergeESignSigningConfigID struct {
	Default string            `json:"default" validate:"required"`
	Others  map[string]string `json:"others"`
}

type HypervergeESignRedirectConfig struct {
	URL     string                  `json:"url" validate:"required"`
	Message HypervergeESignMessages `json:"message" validate:"required"`
}

type HypervergeESignMessages struct {
	Success         string `json:"success" validate:"required"`
	Failure         string `json:"failure" validate:"required"`
	AadhaarMismatch string `json:"aadhaarMismatch" validate:"required"`
}
