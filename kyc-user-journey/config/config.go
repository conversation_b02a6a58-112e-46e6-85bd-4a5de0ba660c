package config

import (
	"context"
	"fmt"
	"strings"
	"sync"

	configsClient "github.com/angel-one/go-config-client"
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/go-playground/mold/v4/modifiers"
	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
)

var configLoaders = []Config{
	&databaseConfigLoader{},
	&loggerConfigLoader{},
	&httpConfigLoader{},
	&jwtConfigLoader{},
	&applicationConfigLoader{},
	&correctionConfigLoader{},
	&hyperVergeConfigLoader{},
	&hypervergeESignConfigLoader{},
	&appLoggingConfigLoader{},
	&emailConfigLoader{},
	&nsdlConfigLoader{},
	&s3ConfigLoader{},
	&featureConfigLoader{},
	&sqsConfigLoader{},
	&kinesisConfigLoader{},
	&lsqConfigLoader{},
	&kraConfigLoader{},
	&flowsConfigLoader{},
	&stepperConfigLoader{},
	&digioConfigLoader{},
	&digilockerConfigLoader{},
	&securityConfigLoader{},
	&smsConfigLoader{},
	&notificationConfigLoader{},
	&setuConfigLoader{},
	&pdfConfigLoader{},
	&piiConfigLoader{},
	&camsConfigLoader{},
	&referralConfigLoader{},
	&onboardingConfigLoader{},
	&clientCommunicationConfigLoader{},
	&s2sConfigLoader{},
	&ssoConfigLoader{},
	&dataAPIClientsConfigLoader{},
	&azureConfigLoader{},
	&cmlConfigLoader{},
	&diyConfigLoader{},
	&experianConfigLoader{},
	&apiPathConfigLoader{},
	&daeConfigLoader{},
	&ledgerConfigLoader{},
	&prismConfigLoader{},
	&journeyStatusConfigLoader{},
	&authenticationConfigLoader{},
	&nriConfigLoader{},
	&piiConfigLoader{},
	&redisConfigLoader{},
}

const (
	configMapLogKey  = "configMap"
	configNameLogKey = "configName"
	rootKeyLogKey    = "rootKey"
)

var sm = &sync.Map{}
var validate = validator.New()
var modifier = modifiers.New()

type Config interface {
	Load() func(any)
	Name() string
	RootKey() string
}

func Init(ctx context.Context) {
	log.Info(ctx).Msgf("Initializing config in %s mode", flags.Mode())

	configsToBeLoaded := lo.Map(configLoaders, func(x Config, _ int) string {
		return x.Name()
	})

	var err error
	if flags.Mode() == constants.TestMode {
		configsToBeLoaded = append(configsToBeLoaded, "global-login-configuration") // for performing common-auth in local.
		err = initializeFromFiles(flags.BaseConfigPath(), configsToBeLoaded...)
	} else if flags.Mode() == constants.ReleaseMode {
		err = initializeFromAppConfig(configsToBeLoaded...)
	}

	if err != nil {
		log.Fatal(ctx).Err(err).Msg("error initializing configs")
		panic(err) // Stop if config loading is failed.
	}

	for _, loader := range configLoaders {
		load(loader.Name(), loader.RootKey(), loader.Load())
	}
}

func loadConfig(name, rootKey string, loadFunc func(any)) error {
	var err error
	if configMap, errRead := client.GetMap(name, rootKey); errRead == nil {
		loadFunc(configMap)
	} else if errRead != nil {
		err = errRead
	}
	if err != nil {
		return err
	}
	return nil
}

func load(configName, rootKey string, loadFunc func(any)) {
	if _, setupAlreadyDone := sm.LoadOrStore(configName, true); !setupAlreadyDone {
		if err := loadConfig(configName, rootKey, loadFunc); err != nil {
			log.Error(context.Background()).
				Err(err).
				Str(configNameLogKey, configName).
				Str(rootKeyLogKey, rootKey).
				Msg("Error While Loading Config")
			panic(err) // intentionally added panic to stop instance if loading at startup fails
		}
		if err := client.AddChangeListener(configName, func(_ ...any) {
			_ = loadConfig(configName, rootKey, loadFunc)
		}); err != nil {
			log.Error(context.Background()).
				Err(err).Str(configNameLogKey, configName).
				Str(rootKeyLogKey, rootKey).
				Msg("Error while adding change Listener")
		}
	}
}

// Client is the instance of the config client to be used by the application.
var client configsClient.Client

// InitializeFromFiles is used to initialize the configs.
func initializeFromFiles(directory string, configNames ...string) error {
	c, err := configsClient.New(configsClient.Options{
		Provider: configsClient.FileBased,
		Params: map[string]any{
			"configsDirectory": directory,
			"configNames":      configNames,
			"configType":       "yaml",
			"secretNames":      []string{"kyc-user-journey-qa", "kyc-user-journey-prod", "global-login-secret"},
			"secretsDirectory": directory,
			"secretType":       "yaml",
		},
	})
	if err != nil {
		return err
	}
	client = c
	return nil
}

// InitializeFromAppConfig is used to initialize the configs.
func initializeFromAppConfig(configNames ...string) error {
	c, err := configsClient.New(configsClient.Options{
		Provider: configsClient.AWSAppConfig,
		Params: map[string]any{
			constants.ConfigIDKey:              constants.ApplicationName,
			constants.ConfigRegionKey:          flags.AWSRegion(),
			constants.ConfigAppKey:             constants.ApplicationName,
			constants.ConfigEnvKey:             flags.Env(),
			constants.ConfigTypeKey:            "yaml",
			constants.ConfigNamesKey:           configNames,
			constants.ConfigCredentialsModeKey: configsClient.AppConfigSharedCredentialMode,
			constants.AuthenticationConfigName: constants.AuthenticationConfig,
			constants.ConfigSecretNamesKey:     []string{"kyc-user-journey-qa", "kyc-user-journey-prod"},
		},
	})
	if err != nil {
		log.Error(context.Background()).Err(err).Stack().Msg("config initialization failed.")
		return err
	}
	client = c
	return nil
}

func GetOptionsForCommonAuth(configNames ...string) configsClient.Options {
	return configsClient.Options{
		Provider: configsClient.AWSAppConfig,
		Params: map[string]any{
			constants.ConfigIDKey:              constants.ApplicationName,
			constants.ConfigRegionKey:          flags.AWSRegion(),
			constants.ConfigAppKey:             constants.ApplicationName,
			constants.ConfigEnvKey:             getCommonAuthEnvKey(),
			constants.ConfigTypeKey:            "yaml",
			constants.ConfigNamesKey:           configNames,
			constants.ConfigCredentialsModeKey: configsClient.AppConfigSharedCredentialMode,
			constants.AuthenticationConfigName: constants.AuthenticationConfig,
			constants.ConfigSecretNamesKey:     []string{GetCommonAuthSecretName()},
		},
	}
}

func getCommonAuthEnvKey() string {
	if flags.Env() == "qa" || flags.Env() == "qa2" || flags.Env() == constants.Empty {
		return "uat"
	}
	return flags.Env()
}

// GetCommonAuthSecretName is used to get the secret name.
func GetCommonAuthSecretName() string {
	return strings.ToLower(fmt.Sprintf("%s-%s", constants.ApplicationName, flags.Env()))
}

// GetClient is used to get the client.
func GetClient() configsClient.Client {
	return client
}

func GetCommonAuthSecretID(targets []string) string {
	if len(targets) == 0 {
		return strings.ToLower(fmt.Sprintf("%s-%s", constants.ApplicationName, flags.Env()))
	}

	val, exists := S2S().TargetToKeyID[targets[0]]
	if exists {
		return val
	}

	return strings.ToLower(fmt.Sprintf("%s-%s", constants.ApplicationName, flags.Env()))
}
