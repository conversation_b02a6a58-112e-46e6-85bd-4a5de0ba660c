package config

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/utils"
)

var hypervergeESignConfig = &models.HypervergeESign{}

type hypervergeESignConfigLoader struct{}

func (hypervergeESignConfigLoader) Name() string {
	return constants.HyperVergeConfig
}

func (hypervergeESignConfigLoader) RootKey() string {
	return constants.HyperVergeConfigRootKey
}

func (c hypervergeESignConfigLoader) Load() func(any) {
	ctx := context.Background()
	return func(configMap any) {
		if err := utils.Convert(configMap, &hyperVergeConfig); err != nil {
			log.Error(ctx).
				Err(err).
				Interface(configMap<PERSON><PERSON><PERSON>ey, configMap).
				Str(config<PERSON><PERSON><PERSON><PERSON>, c.<PERSON>()).
				Str(root<PERSON><PERSON><PERSON><PERSON><PERSON>, c.<PERSON>()).
				Msg("Error converting configuration to struct")
		}
		if modifyError := modifier.Struct(ctx, hyperVergeConfig); modifyError != nil {
			log.Error(ctx).
				Err(modifyError).
				Interface(configMapLogKey, configMap).
				Str(configNameLogKey, c.Name()).
				Str(rootKeyLogKey, c.RootKey()).
				Msg("Unable to Modify configuration")
		}

		if configErrors := validate.Struct(hyperVergeConfig); configErrors != nil {
			log.Error(ctx).
				Interface(configMapLogKey, configMap).
				Str(configNameLogKey, c.Name()).
				Str(rootKeyLogKey, c.RootKey()).
				Msg("Configuration is invalid")
		}
	}
}

func HyperVergeESign() models.HypervergeESign {
	return *hypervergeESignConfig
}
