package task

import (
	"context"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

type HypervergeEsignTask interface {
	// CreateESignPDF fetches all data from DB and generates pdf via external call.
	CreateESignPDF(ctx context.Context, header *models.Headers, meta *models.Meta) (*business.BasicHypervergeEsignDetails, error)
	// CheckEsignEligibility returns nil if user is allowed to do eSign.
	CheckEsignEligibility(ctx context.Context, appNumber, mobile, source string) error
	// GetBasicHypervergeEsignDetails fetches basic esign details from DB like esign-path, signer DOB, city.
	GetBasicHypervergeEsignDetails(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest) (*business.BasicHypervergeEsignDetails, error)
	// GetRedirectRequestData returns data to be sent to Hyperverge as query param `requestData`.
	GetRedirectRequestData(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, esignFormat string) (*business.HypervergeESignRedirectURLData, error)
	// GetSigningConfigID returns Hyperverge config for signature location.
	GetSigningConfigID(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, basicHypervergeDetails *business.BasicHypervergeEsignDetails) (string, error)
	// SaveHypervergeInitiateDetails stores esign data like Hyperverge.
	SaveHypervergeInitiateDetails(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, basicHypervergeDetails *business.BasicHypervergeEsignDetails, HypervergeEsignRequestID string) error
	// SaveSignedDocumentDetails save signer details for esign success cases.
	SaveSignedDocumentDetails(ctx context.Context, appNumber, esignType, signerAadhaarName, filePath, submitterCode string) error
}

// ValidateAppNumberAndRequestID checks if hyperverge request id is correct against given application no.
// ValidateAppNumberAndRequestID(ctx context.Context, appNumber string, hypervergeRequestID string) (string, error)

// DoCompletionTask(ctx context.Context, req *modelsAPIV1.ESignHypervergeRedirectAPIRequest, queryParams *url.Values) error
