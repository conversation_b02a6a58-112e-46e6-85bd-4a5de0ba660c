package api

import (
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	"strings"
)

type ESignHypervergeAPIRequest struct {
	models.Headers `json:"-"`
	models.Meta    `json:"-"`
}

type ESignHypervergeAPIResponse struct {
	RedirectURL string `json:"redirectURL"`
}

type HypervergeRedirectAPIResponse struct {
	RedirectURL string `json:"redirectURL"`
}

type ESignHypervergeRedirectAPIRequest struct {
	models.Headers `json:"-"`
	models.Meta    `json:"-"`
	Data           ESignHypervergeRedirectAPIRequestData
}

type ESignHypervergeRedirectAPIRequestData struct {
	Success            string                         `json:"success" kycmapper:"hyperverge_success"` // can be "true" or "false"
	ID                 string                         `json:"id" kycmapper:"hyperverge_esign_request_id"`
	SignerIdentifier   string                         `json:"signerIdentifier" kycmapper:"hyperverge_signer_identifier"`
	ErrorCode          string                         `json:"errCode" kycmapper:"hyperverge_error_code"`
	ErrorMessage       string                         `json:"errorMessage" kycmapper:"hyperverge_error_message"`
	RequestData        string                         `json:"requestData" kycmapper:"hyperverge_request_data"`
	Source             string                         `json:"source" kycmapper:"hyperverge_source"`
	ESignProvider      string                         `json:"esp" kycmapper:"hyperverge_esign_provider"`
	DecodedRequestData ESignHypervergeRedirectURLData `json:"-"`
}

type ESignHypervergeRedirectURLData struct {
	Source        string `json:"source"`
	AppNumber     string `json:"app_number"`
	AgentID       string `json:"agentId"`
	Mobile        string `json:"mobile"`
	LSQHash       string `json:"hash"`
	Intent        string `json:"intent"`
	ClientCode    string `json:"clientCode"`
	Platform      string `json:"platform"`
	AppVersion    string `json:"appVersion"`
	CorrelationID string `json:"correlationID"`
	Device        string `json:"device"`
	DeviceOS      string `json:"deviceOS"`
	JourneyType   string `json:"journeyType"`
	RejectionType string `json:"rejectionType"`
}

func (req *ESignHypervergeRedirectAPIRequest) Clean() {
	req.Data.ESignProvider = strings.ToLower(strings.TrimSpace(req.Data.ESignProvider))
	req.Data.Success = strings.TrimSpace(req.Data.Success)
	req.Data.ID = strings.TrimSpace(req.Data.ID)
	req.Data.RequestData = strings.TrimSpace(req.Data.RequestData)
	req.Data.SignerIdentifier = strings.TrimSpace(req.Data.SignerIdentifier)
	req.Data.ErrorCode = strings.TrimSpace(req.Data.ErrorCode)
	req.Data.ErrorMessage = strings.TrimSpace(req.Data.ErrorMessage)
}

func (req *ESignHypervergeRedirectAPIRequest) Validate() error {
	req.Clean()

	if req.Data.Success == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty status")
	}
	if req.Data.ID == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty e-sign request id")
	}
	if req.Data.RequestData == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty request data")
	}
	if req.Data.Source == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty source")
	}
	if req.Data.ESignProvider == constants.Empty {
		return constants.ErrHypervergeAPIValidationFailed.WithDetails("empty esign provider")
	}
	return nil
}
