package v1

import "time"

// Input API

type HypervergeInputRequest struct {
	WorkflowId      string `json:"workflowId"`
	TransactionId   string `json:"transactionId"`
	RedirectUrl     string `json:"redirectUrl"`
	ForceCreateLink string `json:"forceCreateLink"`
	Inputs          Inputs `json:"inputs"`
}

type Inputs struct {
	InviteeName  string        `json:"inviteeName"`
	InviteeEmail string        `json:"inviteeEmail"`
	InviteePhone string        `json:"inviteePhone"`
	Coordinates  []Coordinates `json:"coordinates"`
	File         string        `json:"file"`
}

type Coordinates struct {
	X1   string `json:"x1"`
	Y1   string `json:"y1"`
	X2   string `json:"x2"`
	Y2   string `json:"y2"`
	Page string `json:"page"`
}

type HypervergeInputResponse struct {
	Status   string                          `json:"status"`
	Result   HypervergeInputResponseResult   `json:"result"`
	MetaData HyperVergeInputResponseMetaData `json:"metaData"`
}

type HyperVergeInputResponseMetaData struct {
	RequestId string `json:"requestId"`
}

type HypervergeInputResponseResult struct {
	StartKycUrl string `json:"startKycUrl"`
}

type HypervergeOutputRequest struct {
	TransactionId string `json:"transactionId"`
}

// Output API

type HypervergeOutputResponse struct {
	Status   string                           `json:"status"`
	Result   HypervergeOutputResponseResult   `json:"result"`
	MetaData HyperVergeOutputResponseMetaData `json:"metaData"`
}

type HyperVergeOutputResponseMetaData struct {
	RequestId string `json:"requestId"`
}

type HypervergeOutputResponseResult struct {
	Flags         []string                            `json:"flags"`
	UserDetails   HypervergeOutputResponseUserDetails `json:"userDetails"`
	Status        string                              `json:"status"`
	TransactionId string                              `json:"transactionId"`
}

type HypervergeOutputResponseUserDetails struct {
	DocumentId    string `json:"documentId"`
	Expired       bool   `json:"expired"`
	FailureReason string `json:"failureReason"`
	Signed        bool   `json:"signed"`
	Name          string `json:"name"`
	Gender        string `json:"gender"`
	Yob           string `json:"yob"`
	AadharNum     string `json:"aadhar_num"`
	Pincode       string `json:"pincode"`
	State         string `json:"state"`
	S3Url         string `json:"s3Url"`
}

// External APIs

type HypervergeRedirectExternalRequest struct {
	Success                  string `json:"success" kycmapper:"hyperverge_success"` // can be "true" or "false"
	HypervergeESignRequestID string `json:"id" kycmapper:"hyperverge_esign_request_id"`
	SignerIdentifier         string `json:"signerIdentifier" kycmapper:"hyperverge_signer_identifier"`
	ErrorCode                string `json:"errCode" kycmapper:"hyperverge_error_code"`
	ErrorMessage             string `json:"errorMessage" kycmapper:"hyperverge_error_message"`
	RequestData              string `json:"requestData" kycmapper:"hyperverge_request_data"`
	Source                   string `json:"source" kycmapper:"hyperverge_source"`
	ESignProvider            string `json:"esp" kycmapper:"hyperverge_esign_provider"`
}

type HypervergeSignatureStatusResponse struct {
	DocumentID  string           `json:"documentId"`
	ID          string           `json:"id"`
	RedirectURL string           `json:"redirectUrl"`
	Signers     []SignerResponse `json:"signers"`
	Status      string           `json:"status"`
	TraceID     string           `json:"traceId"`
}

type HypervergeDocumentDownloadResponse struct {
	DownloadURL string    `json:"downloadUrl"`
	ID          string    `json:"id"`
	ValidUpto   time.Time `json:"validUpto"`
	TraceID     string    `json:"traceId"`
}

type ChannelHypervergeDocumentDownload struct {
	Error    error
	Response *HypervergeDocumentDownloadResponse
}

type ChannelHypervergeSignatureStatus struct {
	Error    error
	Response *HypervergeSignatureStatusResponse
}
