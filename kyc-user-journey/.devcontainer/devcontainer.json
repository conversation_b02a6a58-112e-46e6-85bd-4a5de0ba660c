// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/go-postgres
{
	"name": "KYC2 User Journey",
	"dockerComposeFile": "docker-compose.yml",
	"service": "app",
	"workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
	"features": {
		"ghcr.io/devcontainers/features/go:1": {},
		"ghcr.io/guiyomh/features/golangci-lint:0": {}
	},
	"forwardPorts": [
		8080,
		3306
	],
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-mssql.data-workspace-vscode",
				"vscjava.vscode-java-debug",
				"dbaeumer.vscode-eslint",
				"ms-toolsai.vscode-jupyter-cell-tags",
				"ms-toolsai.vscode-jupyter-slideshow",
				"DavidAnson.vscode-markdownlint",
				"christian-kohler.npm-intellisense",
				"mtxr.sqltools",
				"TabNine.tabnine-vscode",
				"Gruntfuggly.todo-tree",
				"formulahendry.auto-close-tag",
				"formulahendry.auto-rename-tag",
				"aaron-bond.better-comments",
				"streetsidesoftware.code-spell-checker",
				"kamikillerto.vscode-colorize",
				"mikestead.dotenv",
				"GitHub.vscode-pull-request-github",
				"eamodio.gitlens",
				"oderwat.indent-rainbow",
				"VisualStudioExptTeam.vscodeintellicode",
				"VisualStudioExptTeam.intellicode-api-usage-examples",
				"cweijan.vscode-mysql-client2",
				"esbenp.prettier-vscode",
				"DotJoshJohnson.xml",
				"ms-ossdata.vscode-postgresql",
				"ms-azuretools.vscode-docker"
			]
		}
	}
}