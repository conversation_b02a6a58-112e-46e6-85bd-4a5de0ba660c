package depository

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
)

func (d *CommonDepositoryBusinessLogic) HandleDepository(ctx context.Context, appNumber, mobile, source string) (string, error) {
	dpID, err := d.getDepositoryID(ctx, appNumber)
	if err != nil {
		return constants.Empty, err
	}

	// compute boID.
	boIDLength := config.Application().Defaults.BOIDLength
	boIDStartIndex := config.Application().Defaults.BOIDStartIndex
	boID := constants.Empty
	if len(dpID) >= boIDStartIndex+boIDLength {
		boID = dpID[boIDStartIndex:(boIDStartIndex + boIDLength)]
	}

	// store in depository.
	depositoryDTO := modelsBusiness.Depository{
		AppNumber:        appNumber,
		BoID:             boID,
		ID:               dpID,
		Type:             config.Application().Defaults.Depository,
		Name:             config.Application().Defaults.DepositoryDisplayName,
		Mobile:           mobile,
		CreateSource:     source,
		LastUpdateSource: source,
	}

	err = d.depositoryRepository.UpsertNewDepositoryDetails(ctx, &depositoryDTO)
	if err != nil {
		return constants.Empty, err
	}

	return dpID, nil
}

func (d *CommonDepositoryBusinessLogic) getDepositoryID(ctx context.Context, appNumber string) (string, error) {
	var dpID string
	var err error
	for i := 0; i <= 2; i++ {
		dpID, err = d.instaDPDetailsRepository.GetInstaDPIdForAppNumber(ctx, appNumber)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error getting depository id")
			continue
		}

		if dpID != constants.Empty {
			return dpID, nil
		}

		dpID, err = d.instaDPDetailsRepository.AssignInstaDPIdToAppNumber(ctx, appNumber)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error assigning depository id")
		}

		if dpID != constants.Empty {
			return dpID, nil
		}
	}

	return dpID, err
}
