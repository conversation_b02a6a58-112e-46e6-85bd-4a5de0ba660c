package depository

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type InstaDPDetailsRepository interface {
	GetInstaDPIdForAppNumber(ctx context.Context, appNumber string) (string, error)
	AssignInstaDPIdToAppNumber(ctx context.Context, appNumber string) (string, error)
}

type DepositoryRepository interface {
	UpsertNewDepositoryDetails(ctx context.Context, depositoryDTO *business.Depository) error
}

type CommonDepositoryBusinessLogic struct {
	instaDPDetailsRepository InstaDPDetailsRepository
	depositoryRepository     DepositoryRepository
}

func NewCommonDepositoryBusinessLogic(instaDPDetailsRepository InstaDPDetailsRepository, depositoryRepository DepositoryRepository) *CommonDepositoryBusinessLogic {
	return &CommonDepositoryBusinessLogic{
		instaDPDetailsRepository: instaDPDetailsRepository,
		depositoryRepository:     depositoryRepository,
	}
}
