package azure

import (
	"context"
	"fmt"

	"github.com/angel-one/kyc-user-journey/azure/blob"
	"github.com/angel-one/kyc-user-journey/config"
)

func GetDocAzureBlobURL(ctx context.Context, userID, containerName string) (string, error) {
	userIDSearchPrefix := fmt.Sprintf("%s/", userID)
	return blob.GetBlobClient().GetSignedBlobURL(ctx, containerName, userIDSearchPrefix, config.Azure().ESignURLExpiryInSeconds)
}
