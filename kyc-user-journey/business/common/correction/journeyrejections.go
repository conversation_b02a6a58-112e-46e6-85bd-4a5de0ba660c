package correction

import (
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func (c *CommonJourneyRejectionBusinessLogic) GetFlowToRejectionsMap(stpRejections []business.Rejection) (map[string][]string, error) {
	// add error here in return, in case any if the reasons doesn't have
	// identify flows to be re-done
	flowToRejectionsMap := make(map[string][]string)
	for index := range stpRejections {
		rejectedReason := RemoveNumericsFromString(stpRejections[index].Reason)
		if rejectedFlowsForReason, ok := config.Correction().ReasonToFlowMap[rejectedReason]; ok {
			humanReadableRejectionReason := config.Correction().HumanReadableRejectionReasonMap[rejectedReason]
			for index := range rejectedFlowsForReason {
				rejectedFlow := rejectedFlowsForReason[index]
				flowToRejectionsMap[rejectedFlow] = append(flowToRejectionsMap[rejectedFlow], humanReadableRejectionReason)
			}
		} else {
			return nil, constants.ErrIneligibleForCorrectionJourney.Value()
		}
	}
	return flowToRejectionsMap, nil
}

func (c *CommonJourneyRejectionBusinessLogic) GetDIYFlowsFromReasons(reasons []business.Rejection) []string {
	rejectedFlows := make([]string, 0, len(reasons))
	for _, reason := range reasons {
		rejectedFlows = append(rejectedFlows, reason.Reason)
	}
	return rejectedFlows
}
