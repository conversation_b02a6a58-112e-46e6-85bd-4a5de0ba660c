package aadhaar

import (
	"bytes"
	"context"

	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/fetch"
)

// ModifyOcrResponse
// Map Hyperverge responses to Aadhaar Parts (like FrontTop, FrontBottom, Back).
// Further Aadhaar Parts are consolidated to form Aadhaar Data.
func (a *CommonAadhaarBusinessLogic) ModifyOcrResponse(responses []*modelsExternal.OCRAadharResponse) (*modelsBusiness.AadhaarData, error) {
	var frontTop modelsBusiness.AadhaarFrontTop
	var frontBottom modelsBusiness.AadhaarFrontBottom
	var aadhaarBack modelsBusiness.AadhaarBack

	for _, r := range responses {
		for _, component := range r.Result {
			switch component.Type {
			case constants.AadhaarImageFrontTop:
				err := utils.ConvertStruct(component.Details, &frontTop)
				if err != nil {
					return nil, err
				}
				frontTop.IsPresent = true
			case constants.AadhaarImageFrontBottom:
				err := utils.ConvertStruct(component.Details, &frontBottom)
				if err != nil {
					return nil, err
				}
				frontBottom.IsPresent = true
			case constants.AadhaarImageBack:
				err := utils.ConvertStruct(component.Details, &aadhaarBack)
				if err != nil {
					return nil, err
				}
				aadhaarBack.IsPresent = true
			}
		}
	}

	aadharData, err := consolidateAadharParts(&frontTop, &frontBottom, &aadhaarBack)
	if err != nil {
		return nil, err
	}

	return &aadharData, nil
}

func (a *CommonAadhaarBusinessLogic) DownloadAndUploadMaskedAadhaar(ctx context.Context, s3Path, downloadURL string, isFront bool) (string, error) {
	if s3Path == constants.Empty || downloadURL == constants.Empty {
		if isFront {
			return constants.Empty, constants.ErrMissingAadhaarFrontImage.Value()
		} else {
			return constants.Empty, constants.ErrMissingAadhaarBackImage.Value()
		}
	}
	imageBytes, err := fetch.GetFileBytes(ctx, downloadURL)
	if err != nil {
		return constants.Empty, err
	}
	// upload file.
	client := s3.GetS3Client()
	err = client.Upload(ctx, s3Path, bytes.NewReader(imageBytes))
	if err != nil {
		return constants.Empty, constants.ErrUploadingFile.WithDetails(err.Error())
	}
	// Get Signed S3 path after upload.
	signedPath, err := getSignedS3Path(ctx, s3Path)
	if err != nil {
		return constants.Empty, err
	}

	return signedPath, nil
}
