package aadhaar

import (
	"context"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/aws/s3"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
)

func setAddressData(back *modelsBusiness.BackImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Address data.
	if frontTop.Address.Value != "" && frontTop.Address.Conf > aadhaarBack.Address.Conf {
		back.Address = frontTop.Address.Value
		back.AddressOne = frontTop.Address.Line1
		back.AddressTwo = frontTop.Address.Line2
		back.City = frontTop.Address.City
		back.PinCode = frontTop.Pin.Value
		back.State = frontTop.Address.State
		confidenceScore["address"] = frontTop.Address.Conf
	} else if aadhaarBack.Address.Value != "" {
		back.Address = aadhaarBack.Address.Value
		back.AddressOne = aadhaarBack.Address.Line1
		back.AddressTwo = aadhaarBack.Address.Line2
		back.City = aadhaarBack.Address.City
		back.PinCode = aadhaarBack.Pin.Value
		back.State = aadhaarBack.Address.State
		confidenceScore["address"] = aadhaarBack.Address.Conf
	}
}

func setNameData(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom) {
	// Set Name data.
	if frontTop.Name.Value != "" && frontTop.Name.Conf > frontBottom.Name.Conf {
		front.Name = frontTop.Name.Value
		confidenceScore["name"] = frontTop.Name.Conf
	} else if frontBottom.Name.Value != "" {
		front.Name = frontBottom.Name.Value
		confidenceScore["name"] = frontBottom.Name.Conf
	}
}

func setPersonalInfoData(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontBottom *modelsBusiness.AadhaarFrontBottom) {
	// Set Personal Info data.
	if frontBottom.Aadhaar.Value != "" || frontBottom.Dob.Value != "" || frontBottom.Gender.Value != "" || frontBottom.Mother.Value != "" {
		front.Gender = frontBottom.Gender.Value
		front.URL = frontBottom.URL
		front.DateOfBirth = frontBottom.Dob.Value
		front.MotherName = frontBottom.Mother.Value
		confidenceScore["mother"] = frontBottom.Mother.Conf
		confidenceScore["dob"] = frontBottom.Dob.Conf
		confidenceScore["gender"] = frontBottom.Gender.Conf
	}
}

func setAadhaarNumber(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) error {
	// Set Aadhaar Number data.
	// first get the possible aadhaar numbers.
	aadhaars := make([]string, 0)
	if frontBottom.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, frontBottom.Aadhaar.Value)
		confidenceScore["aadhaar"] = frontBottom.Aadhaar.Conf
	}
	if frontTop.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, frontTop.Aadhaar.Value)
		if confidenceScore["aadhaar"] < frontTop.Aadhaar.Conf {
			confidenceScore["aadhaar"] = frontTop.Aadhaar.Conf
		}
	}
	if aadhaarBack.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, aadhaarBack.Aadhaar.Value)
		if confidenceScore["aadhaar"] < aadhaarBack.Aadhaar.Conf {
			confidenceScore["aadhaar"] = aadhaarBack.Aadhaar.Conf
		}
	}
	if len(aadhaars) > 0 {
		aadhaar := aadhaars[0]
		for i := 1; i < len(aadhaars); i += 1 {
			if aadhaars[i] != aadhaar {
				// different value.
				return constants.ErrDifferentFrontAndBackAadhaar.Value()
			}
		}
		front.AadhaarNo = aadhaar
	}
	// set AadhaarNo.
	if frontBottom.Aadhaar.Value != "" && frontBottom.Aadhaar.Conf > frontTop.Aadhaar.Conf && frontBottom.Aadhaar.Conf > aadhaarBack.Aadhaar.Conf {
		front.AadhaarNo = frontBottom.Aadhaar.Value
		confidenceScore["aadhaar"] = frontBottom.Aadhaar.Conf
	} else if frontTop.Aadhaar.Value != "" && frontTop.Aadhaar.Conf > aadhaarBack.Aadhaar.Conf {
		front.AadhaarNo = frontTop.Aadhaar.Value
		confidenceScore["aadhaar"] = frontTop.Aadhaar.Conf
	} else {
		front.AadhaarNo = aadhaarBack.Aadhaar.Value
		confidenceScore["aadhaar"] = aadhaarBack.Aadhaar.Conf
	}
	return nil
}

func setSpouseAndFatherName(front *modelsBusiness.FrontImageData, back *modelsBusiness.BackImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Spouse and Father Name data.
	if aadhaarBack.Husband.Value != "" && aadhaarBack.Husband.Conf > frontTop.Husband.Conf {
		back.SpouseName = aadhaarBack.Husband.Value
		confidenceScore["spouse"] = aadhaarBack.Husband.Conf
	} else if frontTop.Husband.Value != "" {
		back.SpouseName = frontTop.Husband.Value
		confidenceScore["spouse"] = frontTop.Husband.Conf
	}

	if aadhaarBack.Father.Value != "" && aadhaarBack.Father.Conf > frontTop.Father.Conf {
		front.FatherName = aadhaarBack.Father.Value
		confidenceScore["father"] = aadhaarBack.Father.Conf
	} else if frontTop.Father.Value != "" {
		front.FatherName = frontTop.Father.Value
		confidenceScore["father"] = frontTop.Father.Conf
	}
}

func setURLData(front *modelsBusiness.FrontImageData, back *modelsBusiness.BackImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set URL data.
	if frontBottom.URL != "" {
		front.URL = frontBottom.URL
	} else if frontTop.URL != "" {
		front.URL = frontTop.URL
	}
	if aadhaarBack.URL != "" {
		back.URL = aadhaarBack.URL
	} else if frontTop.URL != "" {
		back.URL = frontTop.URL
	}
}

func setFaceData(front *modelsBusiness.FrontImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Face data.
	if strings.TrimSpace(strings.ToLower(frontTop.Face.Present)) == constants.Yes {
		front.Face = frontTop.Face.FaceString
	} else if strings.TrimSpace(strings.ToLower(frontBottom.Face.Present)) == constants.Yes {
		front.Face = frontBottom.Face.FaceString
	} else if strings.TrimSpace(strings.ToLower(aadhaarBack.Face.Present)) == constants.Yes {
		front.Face = aadhaarBack.Face.FaceString
	}
}

func setQRData(front *modelsBusiness.FrontImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set QR data.
	if frontTop.Qr.Value != "" || frontBottom.Qr.Value != "" || aadhaarBack.Qr.Value != "" {
		front.QR = true
	}
}

func consolidateAadharParts(frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom,
	aadhaarBack *modelsBusiness.AadhaarBack) (modelsBusiness.AadhaarData, error) {
	var front modelsBusiness.FrontImageData
	var back modelsBusiness.BackImageData
	var areAadhaarsMasked = true
	confidenceScore := make(map[string]int)
	// set response into front and back and set into aadhaar response.
	// set maximum confidence score into response and send minimum value in response.
	// set Address and find maximum confidence score value and set into address model.
	setAddressData(&back, confidenceScore, frontTop, aadhaarBack)
	setNameData(&front, confidenceScore, frontTop, frontBottom)
	setPersonalInfoData(&front, confidenceScore, frontBottom)
	err := setAadhaarNumber(&front, confidenceScore, frontTop, frontBottom, aadhaarBack)
	if err != nil {
		return modelsBusiness.AadhaarData{}, err
	}
	setSpouseAndFatherName(&front, &back, confidenceScore, frontTop, aadhaarBack)
	setURLData(&front, &back, frontTop, frontBottom, aadhaarBack)
	setFaceData(&front, frontTop, frontBottom, aadhaarBack)
	setQRData(&front, frontTop, frontBottom, aadhaarBack)

	// get minimum confidence score.
	score := getConfidenceMinimumScore(confidenceScore)

	// are all aadhaars masked.
	if frontTop.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && frontTop.MaskingInfo.IsMaskingDone == constants.Yes
	}
	if frontBottom.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && frontBottom.MaskingInfo.IsMaskingDone == constants.Yes
	}
	if aadhaarBack.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && aadhaarBack.MaskingInfo.IsMaskingDone == constants.Yes
	}

	// set value into aadhaar response.
	aadhaarData := modelsBusiness.AadhaarData{
		FrontData:            front,
		BackData:             back,
		ConfidenceScore:      score,
		AreAllAadhaarsMasked: areAadhaarsMasked,
	}

	return aadhaarData, nil
}

func getConfidenceMinimumScore(score map[string]int) int {
	confidenceList := config.Application().Validations.Aadhaar.ConfidenceScoreParams

	minConfidence := 100
	for _, key := range confidenceList {
		if s, ok := score[key]; ok {
			if s < minConfidence {
				minConfidence = s
			}
		}
	}
	return minConfidence
}

func getSignedS3Path(ctx context.Context, path string) (string, error) {
	client := s3.GetS3Client()
	signedPath, err := client.GetSignedURL(ctx, path, time.Duration(config.S3().SignURLExpiryInSeconds)*time.Second)
	if err != nil {
		return constants.Empty, err
	}
	return signedPath, nil
}
