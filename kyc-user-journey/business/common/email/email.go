package email

import (
	"bytes"
	"context"
	"slices"
	"strings"
	"text/template"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/entities"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (c *CommonEmailBusinessLogic) CheckIfDuplicateEmail(ctx context.Context, email, normalizedEmail, appNumber string, isGoogleVerified bool, skipAbleClientCode string) (*business.CheckIfDuplicateEmailResponse, error) {
	rs, err := c.clcmExternalProvider.GetProfileDetailsFromCLCM(ctx, &modelsExternals.ProfileDetailsFromCLCMRequest{Email: email, Fields: constants.CLCMProfileAttributesForEmail})
	if err != nil {
		log.Error(ctx).Err(err).Msgf("CheckIfDuplicateEmail: error checking if email already exists %v", rs)
		return nil, err
	}
	var profileRep modelsExternals.SearchClientResponse
	for i := range rs.Data {
		isActive, deactivationReason, inactiveFrom, errs := modelsExternals.GetSegmentValue(rs.Data[i].SegmentDetails, rs.Data[i].MfSegmentDetails)
		if errs != nil {
			log.Error(ctx).Err(err).Msgf("CheckIfDuplicateEmail: error checking isActive from clcm response %s", email)
			return nil, errs
		}
		profileRep.Data = append(profileRep.Data, modelsExternals.Profile{
			InactiveDate:   inactiveFrom,
			Active:         isActive,
			AppNumber:      rs.Data[i].ClientDetails.ApplicationNo,
			ClientID:       rs.Data[i].ClientDetails.ClientID,
			DeactiveReason: deactivationReason,
		})
	}

	checkIfDuplicateEmailResponse, err := c.shouldBlockAccount(ctx, email, appNumber, profileRep.Data, skipAbleClientCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CheckIfDuplicateEmail: error checking if email already exists")
		return nil, err
	}

	if checkIfDuplicateEmailResponse.DuplicateEmailStatus != entities.EmailDuplicateDefaultStatus {
		return checkIfDuplicateEmailResponse, nil
	}

	// Check if any sub broker is using the email.
	partnerProfileResponse, err := c.clcmExternalProvider.GetPartnerProfileFromClcm(ctx, modelsExternals.GetCLCMPartnerProfileRequest{
		Email:  email,
		Fields: constants.PartnerProfileAttributes,
	})

	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("CheckIfDuplicateEmail: error checking if  email is of sub-broker")
		return nil, err
	}

	if len(partnerProfileResponse.PartnerProfiles) > 0 {
		log.Info(ctx).Str(constants.LogEmailKey, email).Msg("CheckIfDuplicateEmail:partner profile with same email found")
		return &business.CheckIfDuplicateEmailResponse{DuplicateEmailStatus: entities.EmailPartnerProfileExists}, nil
	}

	count, err := c.emailRepository.GetCountOfEmodApplicationsWithEmail(ctx, email)
	if err != nil {
		return nil, err
	}

	if count > 0 {
		return &business.CheckIfDuplicateEmailResponse{DuplicateEmailStatus: entities.EmailDuplicateEmodRequestExistStatus}, nil
	}

	conflictingAppNumbers, err := c.getConflictingAppNumbersForDuplicates(ctx, email, normalizedEmail, appNumber, isGoogleVerified)
	if err != nil {
		return nil, err
	}

	if conflictingAppNumbers != constants.Empty {
		log.Info(ctx).Str(constants.LogEmailKey, email).Msgf("CheckIfDuplicateEmail: conflicting app numbers found. %s", conflictingAppNumbers)
		return &business.CheckIfDuplicateEmailResponse{
			DuplicateEmailStatus:  entities.EmailDuplicateMiscStatus,
			ConflictingAppNumbers: conflictingAppNumbers,
		}, nil
	}
	log.Info(ctx).Str(constants.LogEmailKey, email).Msgf("This email is not duplicate")
	return &business.CheckIfDuplicateEmailResponse{DuplicateEmailStatus: entities.EmailNotDuplicateStatus}, nil
}

func (c *CommonEmailBusinessLogic) shouldBlockAccount(ctx context.Context, email, appNumber string, profiles []modelsExternals.Profile, skipableClientCode string) (*business.CheckIfDuplicateEmailResponse, error) {
	log.Info(ctx).Interface("existingProfiles", profiles).Str(constants.LogEmailKey, email).
		Msg("CheckIfDuplicateEmail: all existing profiles for email")

	// active profile cases.
	exists, existingClientID := c.commonBlockLeadBusinessLogic.CheckIfActiveProfileExists(ctx, email, profiles, skipableClientCode)
	if exists {
		log.Info(ctx).Str(constants.LogEmailKey, email).Str(constants.LogClientCodeKey, existingClientID).
			Msg("CheckIfDuplicateEmail: active profile found for email with client Code")
		return &business.CheckIfDuplicateEmailResponse{
			DuplicateEmailStatus: entities.EmailDuplicateActiveProfileExistStatus,
			BoUserID:             existingClientID,
		}, nil
	}

	var blocked bool
	// check if wait period since a/c closure is over.
	if len(profiles) > 0 {
		blocked, existingClientID = c.commonBlockLeadBusinessLogic.CheckIfRecentClosedProfileExists(ctx, profiles)
		if blocked {
			log.Info(ctx).Str(constants.LogEmailKey, email).Str(constants.LogClientCodeKey, existingClientID).
				Msg("CheckIfDuplicateEmail: closed, <90 days profile found for email ")
			return &business.CheckIfDuplicateEmailResponse{
				DuplicateEmailStatus: entities.EmailDuplicateClosedProfileBlockedStatus,
				BoUserID:             existingClientID,
			}, nil
		}
	}
	// party code exists case.
	existingPartycode, err := c.checkIfPartyCodeCasesExistsForEmail(ctx, profiles, email, appNumber)
	if err != nil {
		return nil, err
	}

	if existingPartycode != constants.Empty {
		log.Info(ctx).Str(constants.LogEmailKey, email).Str(constants.LogClientCodeKey, existingPartycode).
			Msg("CheckIfDuplicateEmail: party code gen, vsp pending case")
		return &business.CheckIfDuplicateEmailResponse{
			DuplicateEmailStatus: entities.EmailDuplicatePartyCodeExistStatus,
			BoUserID:             existingClientID,
		}, nil
	}

	return &business.CheckIfDuplicateEmailResponse{}, nil
}

func (c *CommonEmailBusinessLogic) checkIfPartyCodeCasesExistsForEmail(ctx context.Context, existingProfiles []modelsExternals.Profile,
	email, appNumber string) (string, error) {
	// get all applications mapped against the incoming bank acc from DB.
	kycApplications, err := c.kycRepository.FetchPartyCodeGeneratedApplicationsAgainstEmail(ctx, email)
	if err != nil {
		return constants.Empty, err
	}

	// check if newLead's email entry is also present; if yes, remove it before proceeding.
	kycApplications = slices.DeleteFunc(kycApplications, func(k business.KYC) bool {
		return k.AppNumber == appNumber
	})

	// remove BO pushed closed a/c.
	if len(existingProfiles) > 0 {
		kycApplications = slices.DeleteFunc(kycApplications, func(k business.KYC) bool {
			if i := slices.IndexFunc(existingProfiles, func(p modelsExternals.Profile) bool {
				return p.AppNumber == k.AppNumber
			}); i != -1 {
				return true
			}
			return false
		})
	}

	if len(kycApplications) > 0 {
		return kycApplications[0].UserID, nil
	}

	// return non-party code generated cases.
	return constants.Empty, nil
}

func (c *CommonEmailBusinessLogic) getConflictingAppNumbersForDuplicates(ctx context.Context, email, normalizedEmail, appNumber string, isGoogleVerified bool) (string, error) {
	var conflictingAppNumbers []string
	var err error
	if !isGoogleVerified {
		log.Error(ctx).Err(err).Msg("Error getting conflicting app numbers with normalized email")
		conflictingAppNumbers, err = c.kycRepository.FetchESignedApplicationsWithNormalizedEmail(ctx, normalizedEmail, appNumber)
	} else {
		log.Error(ctx).Err(err).Msg("Error getting conflicting app numbers with email")
		conflictingAppNumbers, err = c.kycRepository.FetchESignedApplicationsWithEmail(ctx, email, appNumber)
	}
	if err != nil {
		return constants.Empty, err
	}

	if len(conflictingAppNumbers) > 0 {
		return strings.Join(conflictingAppNumbers, constants.Slash), nil
	}

	return constants.Empty, nil
}

func (c *CommonEmailBusinessLogic) SendEmailForNri(ctx context.Context, appNumber string, emailOtherDetails *models.Otp) error {
	// get Email ID and register name for email
	emailID, registerName, err := c.getemailIDAndNameForAppNumber(ctx, appNumber)
	if err != nil {
		return err
	}
	// send email to user
	emailRequest, err := generateEmailRequestForNriScrutiny(ctx, emailID, registerName, appNumber, emailOtherDetails)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", emailID).Interface("RequestId", emailRequest.RequestID).Msg("sendEmailForNri: failed to generate email request ")
		return err
	}
	err = c.notificationExternalProvider.SendEmailSync(ctx, emailRequest)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", emailID).Interface("RequestId", emailRequest.RequestID).Msg("sendEmailForNri: Error while sending Email to user")
		return err
	}

	// Check if customer is B2B
	refferal, err := c.referralRepository.GetReferralWithAppNumber(ctx, appNumber)
	if err != nil {
		log.Error(ctx).Err(err).Interface("RequestId", appNumber).Msg("sendEmailForNri: Error while fetching referral")
		return err
	}
	// If lead is b2b then send email to sub-broker
	if refferal.Type == constants.B2B {
		sbDetails, err := c.subBrokerRepository.GetSubBrokerCommunicationDetails(ctx, refferal.SBTag, refferal.Branch)
		if err != nil {
			log.Warn(ctx).Err(err).Msg("sendEmailForNri : error fetching sb dra details for " + refferal.SBTag)
			return err
		}

		if sbDetails.Email == constants.Empty {
			log.Warn(ctx).Msg("sendEmailForNri: fetchSubBrokerCommDetails nil response for " + refferal.SBTag)
		} else {
			emailRequest, err := generateEmailRequestForNriScrutiny(ctx, sbDetails.Email, registerName, appNumber, emailOtherDetails)
			if err != nil {
				log.Error(ctx).Err(err).Interface("Email", sbDetails.Email).Interface("RequestId", emailRequest.RequestID).Msg("sendEmailForNri: failed to generate for b2b email request ")
				return err
			}
			err = c.notificationExternalProvider.SendEmailSync(ctx, emailRequest)
			if err != nil {
				log.Error(ctx).Err(err).Interface("Email", sbDetails.Email).Interface("RequestId", emailRequest.RequestID).Msg("sendEmailForNri: Error while sending Email to subbroker")
				return err
			}
		}
	}
	return nil
}

func generateEmailRequestForNriScrutiny(ctx context.Context, email, userName, appNumber string, emailOtherDetails *models.Otp) (*modelsExternals.SendEmailRequest, error) {
	emailContent := business.NriScrutinyMailContent{
		UserName:                    userName,
		SupportEmail:                config.NRI().Defaults.SupportEmail,
		NriAccountLink:              config.NRI().Defaults.NriLoginLink,
		WorkingDayToActivateAccount: config.NRI().Defaults.WorkingDayToActivateNriAccount,
	}

	// Create Email Body.
	body, err := generateEmailContentForNriScrutiny(ctx, emailContent, appNumber)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).
			Msg("generateEmailRequestForNriScrutiny: Error while creating the email body")
		return nil, err
	}

	emailData := modelsExternals.EmailData{
		FromEmail:        emailOtherDetails.Sender,
		DestinationEmail: email,
		Body:             body,
		Title:            emailOtherDetails.Title,
	}

	req := modelsExternals.SendEmailRequest{
		RequestID:        utils.GenerateUUID(),
		Priority:         emailOtherDetails.Priority,
		NotificationType: emailOtherDetails.NotificationType,
		Email:            emailData,
	}

	return &req, nil
}

func generateEmailContentForNriScrutiny(ctx context.Context, emailContent business.NriScrutinyMailContent, appNumber string) (string, error) {
	content := config.Email().SubmitToScrutinyOne.Template

	buf := new(bytes.Buffer)
	t, err := template.New(appNumber).Parse(content)
	if err != nil {
		log.Error(ctx).Interface(constants.LogRequestKey, emailContent).Err(err).Msg("generateEmailContentForNriScrutiny: error parsing email content")
		return constants.Empty, err
	}

	err = t.Execute(buf, emailContent)
	if err != nil {
		log.Error(ctx).Interface(constants.LogRequestKey, emailContent).Err(err).Msg("generateEmailContentForNriScrutiny: error processing email content")
		return constants.Empty, err
	}

	return buf.String(), nil
}

func (c *CommonEmailBusinessLogic) getemailIDAndNameForAppNumber(ctx context.Context, appNumber string) (string, string, error) {
	emailData, err := c.emailRepository.GetEmailAndNameFromAppNumber(ctx, appNumber)
	if err != nil {
		log.Error(ctx).Err(err).Interface("appNumber", appNumber).Msg("getemailIDAndNameForAppNumber: error while fethcing email id")
		return constants.Empty, constants.Empty, err
	}

	return emailData.Email, emailData.FullName, nil
}
