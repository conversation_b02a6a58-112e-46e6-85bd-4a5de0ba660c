package email

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type CommonBlockLeadBusinessLogic interface {
	CheckIfActiveProfileExists(ctx context.Context, attribute string, existingProfiles []modelsV1.Profile, skipableClientCode string) (bool, string)
	CheckIfRecentClosedProfileExists(ctx context.Context, existingProfiles []modelsV1.Profile) (bool, string)
}

/*
type ProfileExternalProvider interface {
	// GetPartnerProfile(ctx context.Context, request modelsV1.GetPartnerProfileRequest) (*modelsV1.GetPartnerProfileResponse, error)
	SearchClient(ctx context.Context, query map[string]any) (*modelsV1.SearchClientResponse, error)
}
*/

type KYCRepository interface {
	FetchPartyCodeGeneratedApplicationsAgainstEmail(ctx context.Context, email string) ([]business.KYC, error)
	FetchESignedApplicationsWithNormalizedEmail(ctx context.Context, email, appNumber string) ([]string, error)
	FetchESignedApplicationsWithEmail(ctx context.Context, email, appNumber string) ([]string, error)
}

type EmailRepository interface {
	GetCountOfEmodApplicationsWithEmail(ctx context.Context, email string) (int64, error)
	GetEmailAndNameFromAppNumber(ctx context.Context, appNumber string) (*business.EmailContent, error)
}

type NotificationExternalProvider interface {
	SendEmailSync(ctx context.Context, rq *modelsV1.SendEmailRequest) error
}

type ReferralRepository interface {
	GetReferralWithAppNumber(ctx context.Context, appNumber string) (*business.Referral, error)
}

type SubBrokerRepository interface {
	GetSubBrokerCommunicationDetails(ctx context.Context, subBrokerTag string, subBrokerBranch string) (*business.SubBroker, error)
}

type ClcmExternalProvider interface {
	GetPartnerProfileFromClcm(ctx context.Context, request modelsV1.GetCLCMPartnerProfileRequest) (*modelsV1.GetPartnerProfileResponse, error)
	GetProfileDetailsFromCLCM(ctx context.Context, request *modelsV1.ProfileDetailsFromCLCMRequest) (*modelsV1.ProfileDetailsFromCLCM, error)
}

type CommonEmailBusinessLogic struct {
	kycRepository                KYCRepository
	commonBlockLeadBusinessLogic CommonBlockLeadBusinessLogic
	// profileExternalProvider      ProfileExternalProvider
	emailRepository              EmailRepository
	notificationExternalProvider NotificationExternalProvider
	referralRepository           ReferralRepository
	subBrokerRepository          SubBrokerRepository
	clcmExternalProvider         ClcmExternalProvider
}

func NewCommonBankBusinessLogic(kycRepository KYCRepository, commonBlockLeadBusinessLogic CommonBlockLeadBusinessLogic, emailRepository EmailRepository, referralRepository ReferralRepository, subBrokerRepository SubBrokerRepository, notificationExternalProvider NotificationExternalProvider,
	clcmExternalProvider ClcmExternalProvider) *CommonEmailBusinessLogic {
	return &CommonEmailBusinessLogic{
		kycRepository:                kycRepository,
		commonBlockLeadBusinessLogic: commonBlockLeadBusinessLogic,
		//profileExternalProvider:      profileExternalProvider,
		emailRepository:              emailRepository,
		referralRepository:           referralRepository,
		subBrokerRepository:          subBrokerRepository,
		notificationExternalProvider: notificationExternalProvider,
		clcmExternalProvider:         clcmExternalProvider,
	}
}
