package digio

import (
	"strings"

	"github.com/angel-one/kyc-user-journey/constants"
)

func (c *CommonDigioBusinessLogic) GetGenderValueForAadhaarFromDigioGenderString(gender string) string {
	gender = strings.ToUpper(strings.TrimSpace(gender))
	switch gender {
	case constants.DigioSingleCharacterMaleGender, constants.DigioMaleGender:
		return constants.KYC2MaleGender
	case constants.DigioSingleCharacterFemaleGender, constants.DigioFemaleGender:
		return constants.KYC2FemaleGender
	case constants.DigioSingleCharacterOthersGender, constants.DigioOthersGender:
		return constants.KYC2OthersGender
	}
	return constants.Empty
}
