package emod

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/angel-one/kyc-user-journey/models/business"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	kycUtils "github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/nsdl"
)

// ProceedToEmod  returns error if application is not eligible for emod
// (true, "") indicates no existing application in created state
// (false, string x) indicates application exist in created state with appNumber 'x'.
func (c *CommonEmodBusinessLogic) ProceedToEmod(ctx context.Context, currentIntent, journeyType string,
	profileResponse *modelsExternal.GetProfileResponse, clientSegments *modelsExternal.FetchSegmentsResponseV2, isMfOnlyUser bool) (bool, string, error) {
	if len(profileResponse.Profiles) == 0 {
		return false, constants.Empty, constants.ErrKYCNotCompleted.WithDetails("kyc is not completed for the user")
	}
	clientCode := profileResponse.Profiles[0].ClientID

	err := c.checkClientSegments(ctx, clientSegments)
	if err != nil {
		return false, constants.Empty, err
	}

	if !isMfOnlyUser && !isKycCompleted(profileResponse) {
		return false, constants.Empty, constants.ErrKYCNotCompleted.WithDetails("kyc is not completed for the user")
	}
	err = c.ValidateAccountType(ctx, clientCode)
	if err != nil {
		return false, constants.Empty, err
	}

	inactiveIntents := []string{constants.EmodReKycIntent, constants.EmodReactivationIntent, constants.EmodDeactivationIntent, constants.EmodAccountTransmissionIntent, constants.EmodAccountTransferIntent, constants.EmodAccountClosureIntent}
	if !slices.Contains(inactiveIntents, currentIntent) && !isProfileActive(profileResponse) {
		return false, constants.Empty, constants.ErrInactiveProfile.Value()
	}

	// notPhysicalEmod := isPhysicalEmod == nil || !*isPhysicalEmod
	// Checking if the account is Non Individual account
	if currentIntent != constants.EmodReKycIntent && profileResponse.Profiles[0].PAN[3] != 'P' && journeyType != constants.EmodOfflineJourneyType {
		// 4th character of the pan represents the type of account
		log.Debug(ctx).Msg(fmt.Sprintf("EModifiction: Account is Non individual Account %s", profileResponse.Profiles[0].ClientID))
		return false, constants.Empty, constants.ErrEModNotAllowedForNonIndividual.Value()
	}

	return c.ShouldProceedEmodRequest(ctx, currentIntent, clientCode, true)
}

func (c *CommonEmodBusinessLogic) IsMultipleHolderAccount(ctx context.Context, clientCode, dpID string) (bool, error) {
	holder, err := c.holderRepository.FetchHolderDetailsForClientCode(ctx, clientCode, dpID)
	if err != nil {
		return false, err
	}
	if holder.SecondHolderName == constants.Empty &&
		holder.ThirdHolderName == constants.Empty {
		return false, nil
	}
	return true, nil
}

func (c *CommonEmodBusinessLogic) ValidateAccountType(ctx context.Context, clientCode string) error {
	clcmRes, err := c.clcmExternalProvider.QueryProfileDetailsFromClientCode(ctx, clientCode, constants.CLCMAttributesForAccountTypeAndSubTypeFetch)
	if err != nil {
		log.Error(ctx).Err(err).Msg("ValidateAccountType: error getting clcm data from clientCode")
		return err
	}
	if len(clcmRes.Data) == 0 {
		log.Error(ctx).Err(err).Msg("ValidateAccountType: no clcm record found against clientCode")
		return constants.ErrClcmClientDetailsFetch.WithDetails("ValidateAccountType: no clcm record found against clientCode")
	}
	if len(clcmRes.Data) > 1 {
		log.Error(ctx).Err(err).Msg("ValidateAccountType: more than one record found against clientCode")
		return constants.ErrClcmClientDetailsFetch.WithDetails("ValidateAccountType: more than one record found against clientCode")
	}
	if clcmRes.Data[0].ClientDetails.ClientAccountType == constants.AccountTypeForeignNational &&
		clcmRes.Data[0].ClientDetails.ClientAccountSubType == constants.AccountTypeForeignNational {
		log.Error(ctx).Err(err).Msg("ValidateAccountType: Foreign National Account")
		return constants.ErrEModNotAllowedForFNUser.Value()
	}
	return nil
}

func GetEmailMobileAuthToken(tokenData map[string]string) (string, error) {
	jwtSigningAlgorithmKey := config.JWT().Realms.EmailMobile.SigningAlgorithm
	jwtIdentityKey := config.JWT().Realms.EmailMobile.Identity
	jwtSymmetricKey := config.JWT().Realms.EmailMobile.SymmetricKey
	tokenValidity := config.JWT().Realms.EmailMobile.TimeoutInMinutes

	return kycUtils.GetJWToken(jwtSigningAlgorithmKey,
		jwtSymmetricKey,
		jwtIdentityKey,
		tokenData,
		time.Minute*time.Duration(tokenValidity),
	)
}

func (c *CommonEmodBusinessLogic) GetAppNumberForEmodIncomeUpdate(ctx context.Context, profileResponse *modelsExternal.GetProfileResponse, clientSegments *modelsExternal.FetchSegmentsResponseV2, intent, journeyType string) (string, error) {
	proceedEmod, appNumber, err := c.ProceedToEmod(ctx, intent, journeyType, profileResponse, clientSegments, false)
	if err != nil {
		log.Error(ctx).Err(err).Msg("getAppNumberForEmodIncomeUpdateRequest: error in ProceedToEmod")
		return constants.Empty, err
	} else if !proceedEmod {
		log.Info(ctx).Str(constants.LogAppNumberKey, appNumber).Msg("getAppNumberForEmodIncomeUpdateRequest: existing emod request")
	}

	if appNumber == constants.Empty {
		appNumber, err = c.emodStartRepository.GetNextAppNumberForJourneyType(constants.EmodJourneyType)
		if err != nil {
			log.Error(ctx).Err(err).Msg("getAppNumberForEmodIncomeUpdateRequest: error generating app number for emod journey")
			return constants.Empty, err
		}
	}

	return appNumber, nil
}

// GetITName validates given name with NSDL, and if unsuccessful fetches name from cvlkra.
// returns (true,matched_name) if name is matched with NSDL.
// returns (false,clcm_name) if name is not matched.
func (c *CommonEmodBusinessLogic) GetITName(ctx context.Context, clientCode string, clcmPanDetails *modelsExternal.PanDetails) (bool, string, error) {
	isNameMatched, _, err := c.ValidateNameAndDoBWithNSDL(ctx, clcmPanDetails.Pan, clcmPanDetails.FullName, clcmPanDetails.Dob)
	if err != nil {
		return false, clcmPanDetails.FullName, err
	}

	if isNameMatched {
		return true, clcmPanDetails.FullName, err
	}

	// get details from kra.
	cvlkraPanStatusResponse, err := c.kraExternalProvider.GetCVLKRAStatus(ctx, clcmPanDetails.Pan, clcmPanDetails.Dob)
	if err != nil {
		log.Error(ctx).Err(err).Msg("Error getting status response from KRA")
		return false, clcmPanDetails.FullName, nil
	}
	// validate kra name.
	isNameMatched, nsdlRes, _ := c.ValidateNameAndDoBWithNSDL(ctx, clcmPanDetails.Pan, cvlkraPanStatusResponse.Name, clcmPanDetails.Dob)
	if isNameMatched {
		// update kra name in clcm.
		req := business.EmodNameDoB{
			ClientCode:     clientCode,
			DOB:            clcmPanDetails.Dob,
			FullName:       cvlkraPanStatusResponse.Name,
			Pan:            clcmPanDetails.Pan,
			IsSeededByNSDL: nsdlRes.Data[0].IsAadhaarSeeded,
		}
		err = c.UpdateNameAndDoB(ctx, &req)
		if err != nil {
			log.Error(ctx).Err(err).Msg("GetITName: error starting name update workflow")
			return false, clcmPanDetails.FullName, err
		}
		return true, cvlkraPanStatusResponse.Name, nil
	}
	return false, clcmPanDetails.FullName, nil
}

func (c *CommonEmodBusinessLogic) ValidateNameAndDoBWithNSDL(ctx context.Context, pan, name, dob string) (bool, *modelsExternal.ValidatePanWithNSDLV2FinalResponse, error) {
	nsdlResponse, nsdlPanValidationErr := c.nsdlExternalProvider.ValidatePANV2(
		ctx, &modelsExternal.NSDLVerifyPanV2Request{
			InputData: []modelsExternal.NSDLVerifyPanV2InputData{{Pan: pan, Name: name, DOB: dob}},
		},
		nsdl.RoundRobinV2.Next(),
	)
	if nsdlPanValidationErr != nil || len(nsdlResponse.Data) == 0 {
		return false, nil, constants.ErrNSDLPanValidation.WithDetails("Invalid NSDL Response")
	} else if !nsdlResponse.Data[0].IsValid {
		return false, nsdlResponse, constants.ErrInValidPAN.WithMessage("Incorrect PAN. Enter valid PAN")
	}

	if nsdlResponse.Data[0].IsNameMatch && nsdlResponse.Data[0].ISDoBMatch {
		log.Info(ctx).Msg("details matched")
		return true, nsdlResponse, nil
	}
	return false, nsdlResponse, nil
}

func (c *CommonEmodBusinessLogic) checkClientSegments(ctx context.Context, clientSegments *modelsExternal.FetchSegmentsResponseV2) error {
	if clientSegments == nil || (len(clientSegments.Data.MF) == 0 && len(clientSegments.Data.Equity) == 0) {
		log.Info(ctx).Msg("ProceedToEmod: No segments data found in clcm")
		return constants.ErrClcmSegmentsFetch.WithDetails("No segments found")
	}

	return nil
}

func (c *CommonEmodBusinessLogic) UpdateNameAndDoB(ctx context.Context, req *business.EmodNameDoB) error {
	_, err := c.workflowExternalProvider.StartProcessInstance(ctx, constants.EmodNameDoBChangeWorkflow, modelsExternal.ProcessStartRequest{
		BusinessKey: req.ClientCode,
		Variables:   getProcessVariables(req),
	})
	// create app_number and make entry in pan table with the app_number
	if err != nil {
		log.Error(ctx).Err(err).Msg("UpdateNameAndDoB: error while starting workflow")
		return err
	}

	return nil
}
func (c *CommonEmodBusinessLogic) ShouldProceedEmodRequest(ctx context.Context, currentIntent, clientCode string, checkForSameIntent bool) (bool, string, error) {
	intentList := config.Application().Validations.Emod.AllowedIntents
	statusList := []int{constants.EmodStatusCreated, constants.EmodStatusSubmitted}

	// fetch active application.
	emodStarts, err := c.emodStartRepository.FetchAllEmodApplicationsForClientCodeWithStatus(clientCode, intentList, statusList)

	if err != nil {
		log.Error(ctx).Err(err).Msg("ProceedToEmod: error getting emod status from db")
		return false, constants.Empty, err
	}

	// no active submitted application should be present of any other intent
	for i := range emodStarts {
		activeApplication := &emodStarts[i]
		if activeApplication.Status == constants.EmodStatusSubmitted {
			return false, constants.Empty, constants.ErrDuplicateEmodRequest.WithDetails(
				fmt.Sprintf("ProceedToEmod: Emod request is active with intent : %v", activeApplication.Intent))
		}
	}

	if checkForSameIntent {
		// Check if a emod request already exists with the same intent
		for i := range emodStarts {
			activeApplication := &emodStarts[i]
			if activeApplication.Intent == currentIntent {
				return false, activeApplication.AppNumber, nil
			}
		}
	}

	return true, constants.Empty, nil
}
