package emod

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

type EmodStartRepository interface {
	FetchAllEmodApplicationsForClientCodeWithStatus(clientCode string, intentList []string, statusList []int) ([]business.EmodStart, error)
	GetNextAppNumberForJourneyType(journeyType string) (string, error)
}

type EmodActiveJourneysRepository interface {
	CheckActiveEmodJourneyExistsForClientCode(clientCode string) (bool, error)
}

type KycCiRepository interface {
	IsActiveEmod1Application(ctx context.Context, clientCode string) (bool, error)
}
type HolderRepository interface {
	FetchHolderDetailsForClientCode(ctx context.Context, clientCode, dpID string) (*business.Holder, error)
}

type NsdlExternalProvider interface {
	ValidatePANV2(ctx context.Context, request *modelsExternals.NSDLVerifyPanV2Request, userName string) (*modelsExternals.ValidatePanWithNSDLV2FinalResponse, error)
}

type KRAExternalProvider interface {
	GetCVLKRAStatus(ctx context.Context, pan, dob string) (*modelsExternals.CvlKRAPanStatusResponse, error)
}

type ClcmExternalProvider interface {
	FetchSegmentDetailsV2(ctx context.Context, clientCode string) (*modelsExternals.FetchSegmentsResponseV2, error)
	QueryProfileDetailsFromClientCode(ctx context.Context, clientCode string, attributes []string) (*modelsExternals.QueryProfileDetailsFromCLCMResponse, error)
}

type WorkflowExternalProvider interface {
	StartProcessInstance(ctx context.Context, workflowName string, request modelsExternals.ProcessStartRequest) (*modelsExternals.ProcessStartResponse, error)
}

type CommonEmodBusinessLogic struct {
	emodStartRepository          EmodStartRepository
	emodActiveJourneysRepository EmodActiveJourneysRepository
	kycCiRepository              KycCiRepository
	holderRepository             HolderRepository
	nsdlExternalProvider         NsdlExternalProvider
	kraExternalProvider          KRAExternalProvider
	clcmExternalProvider         ClcmExternalProvider
	workflowExternalProvider     WorkflowExternalProvider
}

func NewCommonEmodBusinessLogic(emodStartRepository EmodStartRepository,
	emodActiveJourneysRepository EmodActiveJourneysRepository, kycCiRepository KycCiRepository, holderRepository HolderRepository,
	nsdlExternalProvider NsdlExternalProvider, kraExternalProvider KRAExternalProvider, clcmExternalProvider ClcmExternalProvider,
	workflowExternalProvider WorkflowExternalProvider) *CommonEmodBusinessLogic {
	return &CommonEmodBusinessLogic{
		emodStartRepository:          emodStartRepository,
		emodActiveJourneysRepository: emodActiveJourneysRepository,
		kycCiRepository:              kycCiRepository,
		holderRepository:             holderRepository,
		nsdlExternalProvider:         nsdlExternalProvider,
		kraExternalProvider:          kraExternalProvider,
		clcmExternalProvider:         clcmExternalProvider,
		workflowExternalProvider:     workflowExternalProvider,
	}
}
