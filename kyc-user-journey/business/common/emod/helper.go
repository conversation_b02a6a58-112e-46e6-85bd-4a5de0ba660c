package emod

import (
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func isKycCompleted(profileResponse *modelsExternal.GetProfileResponse) bool {
	// If profile not found
	if len(profileResponse.Profiles) == 0 {
		return false
	}
	if utils.IsValidAlphaNumericString(profileResponse.Profiles[0].DpDetails.DpIDNo) {
		return true
	} else {
		return false
	}
}

func IsMfOnlyUser(clcmRes *modelsExternal.FetchSegmentsResponseV2) bool {
	if len(clcmRes.Data.MF) > 0 && clcmRes.Data.MF[0].IsActive && len(clcmRes.Data.Equity) == 0 {
		return true
	}
	return false
}

func isProfileActive(profileResponse *modelsExternal.GetProfileResponse) bool {
	return profileResponse.Profiles[0].Active
}

func IsMinorAccount(dob string) (bool, error) {
	isMinor, err := utils.IsAgeLesserThanThreshold(dob, constants.GuardianMinorAgeThreshold)
	if err != nil {
		return false, err
	}
	if isMinor {
		return true, nil
	}
	return false, nil
}

func getProcessVariables(request *business.EmodNameDoB) map[string]modelsExternal.VariableObject {
	variables := map[string]modelsExternal.VariableObject{}
	variables[constants.ClientCodeWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.ClientCode,
	}
	variables[constants.NameWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.FullName,
	}
	variables[constants.DobWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.DOB,
	}
	variables[constants.PanWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Pan,
	}
	variables[constants.IsNSDLSeededWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableBooleanType,
		Value: request.IsSeededByNSDL,
	}
	return variables
}
