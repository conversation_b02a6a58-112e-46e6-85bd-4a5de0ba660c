package segments

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

type DerivativesRepository interface {
	Save(ctx context.Context, derivativesDTO *business.Derivatives) error
}

type EmodRepository interface {
	FreshInsertSegment(ctx context.Context, emodStart *business.EmodStart, panDTO *business.Pan, emailDTO *business.Email) error
}

type CommonEmodBusinessLogic interface {
	ProceedToEmod(ctx context.Context, currentIntent, journeyType string, profileResponse *modelsExternals.GetProfileResponse, clientSegments *modelsExternals.FetchSegmentsResponseV2, isMfOnlyUser bool) (bool, string, error)
	GetITName(ctx context.Context, clientCode string, clcmPanDetails *modelsExternals.PanDetails) (bool, string, error)
}

type ClcmExternalProvider interface {
	FetchPanDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelFetchPanFromCLCMResponse)
	FetchSegmentDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelFetchSegmentsResponse)
	GetClientMetadataWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelGetClientMetadataResponse)
	GetClientBasicDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelGetClientBasicDetailsResponse)
	FetchSegmentDetailsV2(ctx context.Context, clientCode string) (*modelsExternals.FetchSegmentsResponseV2, error)
	GetCLCMProfilesWithChannel(ctx context.Context, request *modelsExternals.ProfileDetailsFromCLCMRequest, ch chan modelsExternals.ChannelGetProfileResponse)
}

type PortfolioExternalProvider interface {
	GetHoldingDetailsWithChannel(ctx context.Context, clientCode, nonTradeToken string, ch chan modelsExternals.ChannelGetHoldingsResponse)
}

type ProfileExternalProvider interface {
	GetProfilesWithChannel(ctx context.Context, request *modelsExternals.GetProfileRequest, ch chan modelsExternals.ChannelGetProfileResponse)
}
