package segments

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/angel-one/kyc-user-journey/aws/s3"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
	"github.com/angel-one/kyc-user-journey/utils/pdf"
)

func GenerateAndSaveHoldingsPdf(ctx context.Context, appNum string, mobile string, clientCode string, source string,
	holdingDetails []modelsExternals.HoldingDetails,
	panCLCMData *modelsExternals.PanCLCMData,
	profileData *modelsExternals.Profile) (*business.Derivatives, error) {
	// get data for pdf from various responses.
	holdingsData, err := getHoldingsPDFData(mobile, clientCode, holdingDetails, panCLCMData, profileData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("generateAndSaveHoldingsPdf: error while getting data for holdings pdf")
		return nil, err
	}

	// generate holdings pdf.
	templateStr := config.PDF().Holdings.Template
	data, err := pdf.GeneratePDF(ctx, templateStr, appNum, holdingsData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("generateAndSaveHoldingsPdf: error generating holdings pdf")
		return nil, err
	}

	// upload pdf to s3.
	filePath := fmt.Sprintf("%s/%s/%s%s", appNum, constants.Derivatives, constants.HoldingsProof, constants.PDFExtension)
	err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(data))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("generateAndSaveHoldingsPdf: error uploading file to s3")
		return nil, constants.ErrUploadingFile.WithDetails([]string{constants.Derivatives, err.Error()})
	}

	// save path in DB.
	derivativesDTO := getDerivativesDTO(appNum, source, filePath)
	return derivativesDTO, nil
}

func getHoldingsPDFData(mobile string, clientCode string,
	holdingDetails []modelsExternals.HoldingDetails,
	panCLCMData *modelsExternals.PanCLCMData,
	profileData *modelsExternals.Profile) (*business.HoldingsPDFData, error) {
	var holdings []business.HoldingsPDFDetails
	totalHolding := 0
	maxHoldingLength := config.Application().Thresholds.MaxHoldingsLengthForPDF

	// use limited holding details for pdf.
	for i := 0; i < min(maxHoldingLength, len(holdingDetails)); i++ {
		totalHolding += holdingDetails[i].CurMktValue
		var holdingsDTO business.HoldingsPDFDetails
		err := objectmapper.GetMapperInstance().AutoMapper(&holdingDetails[i], &holdingsDTO)
		if err != nil {
			return nil, constants.ErrMappingFailed.WithDetails(err)
		}
		holdings = append(holdings, holdingsDTO)
	}

	return &business.HoldingsPDFData{
		HoldingAsOn:  time.Now().Format(constants.HoldingsPDFDateFormat),
		Mobile:       mobile,
		ClientID:     clientCode,
		PanNo:        panCLCMData.Pan,
		Name:         panCLCMData.FullName,
		Address:      profileData.ClientDetails.Email,
		TotalHolding: totalHolding,
		Holdings:     holdings,
		Status:       "ACTIVE",
		SubType:      constants.Empty,
		Phone:        constants.Empty,
	}, nil
}

func getDerivativesDTO(appNum, source, filePath string) *business.Derivatives {
	return &business.Derivatives{
		AppNumber:           appNum,
		Type:                constants.DerivativesTypeDematHoldingStatement,
		Path:                filePath,
		ActivationStatus:    constants.DerivativesStatusPending,
		CreateSource:        source,
		LastUpdateSource:    source,
		IsPasswordProtected: false,
	}
}

// CheckActiveSegments checks if user selected segments are already active.
func CheckActiveSegments(ctx context.Context, pref *business.UserOptedSegments, clcmRes *modelsExternals.FetchSegmentsResponse) error {
	currentlyActiveSegments := getSegmentsMapFromCLCMData(clcmRes)
	segmentsConfig := config.Application().Validations.Segments
	if pref.IsFnO {
		isActive := checkIfAlreadyActiveSegment(segmentsConfig.Fno, currentlyActiveSegments)
		if isActive {
			log.Warn(ctx).Msg("checkActiveSegments: fno already active")
			return constants.ErrFnOAlreadyActive.Value()
		}
	}
	if pref.IsCurrency {
		isActive := checkIfAlreadyActiveSegment(segmentsConfig.Currency, currentlyActiveSegments)
		if isActive {
			log.Warn(ctx).Msg("checkActiveSegments: currency already active")
			return constants.ErrCurrencyAlreadyActive.Value()
		}
	}
	if pref.IsCommodity {
		isActive := checkIfAlreadyActiveSegment(segmentsConfig.Commodity, currentlyActiveSegments)
		if isActive {
			log.Warn(ctx).Msg("checkActiveSegments: commodity already active")
			return constants.ErrCommodityAlreadyActive.Value()
		}
	}
	return nil
}

func checkIfAlreadyActiveSegment(configs []models.ApplicationSegmentsAndExchanges, currentlyActiveSegments map[string]bool) bool {
	for _, config := range configs {
		key := fmt.Sprintf(constants.SegmentKeyFormat, config.Segment, config.Exchange)
		isActive, exists := currentlyActiveSegments[key]
		if exists && isActive {
			return true
		}
	}

	return false
}

// getSegmentsMapFromCLCMData converts clcm data array into re-usable map format.
func getSegmentsMapFromCLCMData(clcmRes *modelsExternals.FetchSegmentsResponse) map[string]bool {
	segment := make(map[string]bool)
	for _, details := range clcmRes.Data {
		key := fmt.Sprintf(constants.SegmentKeyFormat, details.Segment, details.Exchange)
		segment[key] = details.IsActive
	}
	return segment
}
