package prism

import (
	"time"

	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func (p *CommonPrismBusinessLogic) GetPrismSuccessEventData(appNumber, eventName, subEventName, activityID string) *business.PrismEventMessage {
	eventData := business.PrismEventMessage{
		Source:         constants.ApplicationName,
		EventName:      eventName,
		EventSubtype:   subEventName,
		ActivityID:     activityID,
		EventStartTime: time.Now().UTC().UnixMilli(),
		EventEmitTime:  time.Now().UTC().UnixMilli(),
		Workflow:       constants.ApplicationName,
		AppNumber:      appNumber,
		RetryCount:     1,
		EventEndTime:   time.Now().UTC().UnixMilli(),
	}

	return &eventData
}

func (p *CommonPrismBusinessLogic) GetPrismErrorEventData(appNumber, eventName, subEventName, activityID, remark string) *business.PrismEventMessage {
	eventData := business.PrismEventMessage{
		Source:         constants.ApplicationName,
		EventName:      eventName,
		EventSubtype:   subEventName,
		ActivityID:     activityID,
		EventStartTime: time.Now().UTC().UnixMilli(),
		EventEmitTime:  time.Now().UTC().UnixMilli(),
		Workflow:       constants.ApplicationName,
		AppNumber:      appNumber,
		RetryCount:     1,
		Remark:         remark,
	}
	return &eventData
}

func (p *CommonPrismBusinessLogic) GetPrismAppAddressMetadata(eventMsg *business.PrismEventMessage) *business.PrismEventMessage {
	eventData := business.PrismEventMessage{
		AppNumber:     eventMsg.AppNumber,
		EventEmitTime: time.Now().UTC().UnixMilli(),
		EventName:     constants.Metadata,
		EventSubtype:  constants.Metadata,
	}
	assignIfNotEmpty(&eventData.Pincode, eventMsg.Pincode)
	assignIfNotEmpty(&eventData.City, eventMsg.City)
	assignIfNotEmpty(&eventData.State, eventMsg.State)
	return &eventData
}

func (p *CommonPrismBusinessLogic) GetPrismAppLeadMetaData(eventMsg *business.PrismEventMessage) *business.PrismEventMessage {
	if eventMsg.ClientType == constants.Empty {
		eventMsg.ClientType = constants.B2C
	}
	if eventMsg.PlanName == constants.Empty {
		eventMsg.PlanName = constants.ITradePremier
	}
	eventData := business.PrismEventMessage{
		ClientType:    eventMsg.ClientType,
		AppNumber:     eventMsg.AppNumber,
		EventEmitTime: time.Now().UnixMilli(),
		EventName:     constants.Metadata,
		EventSubtype:  constants.Metadata,
	}
	assignIfNotEmpty(&eventData.ReferralSBTag, eventMsg.ReferralSBTag)
	assignIfNotEmpty(&eventData.ReferralCode, eventMsg.ReferralCode)
	assignIfNotEmpty(&eventData.DeviceName, eventMsg.DeviceName)
	assignIfNotEmpty(&eventData.Platform, eventMsg.Platform)
	assignIfNotEmpty(&eventData.AppVersion, eventMsg.AppVersion)
	assignIfNotEmpty(&eventData.ClientName, eventMsg.ClientName)
	assignIfNotEmpty(&eventData.ClientCode, eventMsg.ClientCode)
	assignIfNotEmpty(&eventData.PlanName, eventMsg.PlanName)

	return &eventData
}
