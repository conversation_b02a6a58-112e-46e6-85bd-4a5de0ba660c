package bank

import (
	"strings"

	"github.com/angel-one/kyc-user-journey/constants"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

func getInvalidNames() []string {
	return []string{constants.Unregistered, constants.NoName,
		constants.RNSB, constants.BCB, constants.PJSB, constants.IMPSCustomer,
		constants.IMPSUser, constants.ECollectAccount, constants.DNSB, constants.Cosmos}
}

func getBeneficiaryNameFromIMPSHyperVergeResponse(irs *modelsExternals.IMPSHyperVergeResponse) string {
	if !strings.EqualFold(irs.Status, constants.Success) || !isNameValid(irs.Result.AccountName) {
		return constants.Empty
	}
	return irs.Result.AccountName
}

func isNameValid(name string) bool {
	invalidNames := getInvalidNames()
	for i := range invalidNames {
		if name == invalidNames[i] || name == constants.Empty {
			return false
		}
	}
	return true
}
