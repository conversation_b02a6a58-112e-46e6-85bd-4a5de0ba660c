package bank

import (
	"context"
	"slices"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsCommon "github.com/angel-one/kyc-user-journey/models/business/common"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

func (c *CommonBankBusinessLogic) CheckIfBankAccountExists(ctx context.Context, accountNumber, appNumber, clientCode string) (bool, bool, error) {
	accountNumbersToCheckForDuplicates := getAccountNumberListToCheck(accountNumber)
	clcmClientDetails, err := c.clcmExternalProvider.FetchClientDetailsWithBankAccountNumberList(ctx, accountNumbersToCheckForDuplicates, []string{"client_segments"})
	if err != nil {
		log.Error(ctx).Err(err).Msg("checkIfBankAccountExists: error getting segments from clcm for duplicate bank check")
		return false, false, err
	}

	blockingClientIDs, allClientIds := c.commonBlockLeadBusinessLogic.GetBlockingClientCodeFromClientDetails(ctx, clcmClientDetails)
	vspPendingPartyCodesCount, fulfillmentCount, err := c.getCountOfPartyCodeAndFulfilmentCases(allClientIds, accountNumbersToCheckForDuplicates, appNumber)
	if err != nil {
		return false, false, err
	}

	if len(blockingClientIDs)+vspPendingPartyCodesCount >= config.Application().Thresholds.MaxDematAccountsWithBankAccount {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: bank account used by maximum number of clients")
		return true, false, nil
	}

	if fulfillmentCount >= config.Application().Thresholds.MaximumBankAccountsLinked {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: max cap exceeded")
		return true, false, nil
	}

	if len(blockingClientIDs)+vspPendingPartyCodesCount > 0 {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: joint account confirmation needed as account number is already used")
		return false, true, nil
	}

	emodApplicationsCount, err := c.emodBankRepository.GetCountOfEmodApplicationsWithAccountNumberList(ctx, accountNumbersToCheckForDuplicates, clientCode)
	if err != nil {
		return false, false, err
	}

	if emodApplicationsCount > 0 {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: bank account already used in emod")
		return true, false, nil
	}

	return false, false, nil
}

func (c *CommonBankBusinessLogic) CheckIfBankAccountExistsWithoutJointAccount(ctx context.Context, accountNumber, appNumber, clientCode string) (bool, error) {
	accountNumbersToCheckForDuplicates := getAccountNumberListToCheck(accountNumber)
	clcmClientDetails, err := c.clcmExternalProvider.FetchClientDetailsWithBankAccountNumberList(ctx, accountNumbersToCheckForDuplicates, []string{"client_segments"})
	if err != nil {
		return false, err
	}

	exists, err := c.shouldBlockAccount(ctx, appNumber, accountNumbersToCheckForDuplicates, clcmClientDetails)
	if err != nil {
		log.Error(ctx).Err(err).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: error checking if the account is blocked")
		return false, err
	}

	if exists {
		return true, nil
	}

	count, err := c.bankRepository.GetCountOfBankAccountLinkedApps(ctx, accountNumbersToCheckForDuplicates, appNumber)
	if err != nil {
		return true, err
	}

	if count >= int64(config.Application().Thresholds.MaximumBankAccountsLinked) {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Msg("checkIfBankAccountExists: max cap exceeded")
		return true, nil
	}

	count, err = c.emodBankRepository.GetCountOfEmodApplicationsWithAccountNumberList(ctx, accountNumbersToCheckForDuplicates, clientCode)
	if err != nil {
		return true, err
	}

	if count > 0 {
		return true, nil
	}

	return false, nil
}

func (c *CommonBankBusinessLogic) shouldBlockAccount(ctx context.Context, appNumber string, accountNumberList []string, clientDetails *modelsExternals.GetClientDetailsByAttributesResponse) (bool, error) {
	blockingClientIDs, allClientIds := c.commonBlockLeadBusinessLogic.GetBlockingClientCodeFromClientDetails(ctx, clientDetails)
	if len(blockingClientIDs) > 0 {
		return true, nil
	}

	vspPendingCasesCount, _, err := c.getCountOfPartyCodeAndFulfilmentCases(allClientIds, accountNumberList, appNumber)
	if err != nil {
		return true, err
	}

	if vspPendingCasesCount > 0 {
		log.Info(ctx).Msg("checkIfBankAccountExists: party code gen, vsp pending case")
		return true, nil
	}
	return false, nil
}

func (c *CommonBankBusinessLogic) GetPrimaryBankIFSCCodeFromBO(ctx context.Context, clientCode string) (string, error) {
	ifscCode, err := c.rtgsRepository.GetPrimaryBankIFSCCode(ctx, clientCode)
	if err != nil {
		return constants.Empty, err
	}

	if ifscCode != constants.Empty {
		return ifscCode, nil
	}

	ifscCode, err = c.clientBankMappRepository.GetPrimaryBankIFSCCode(ctx, clientCode)
	if err != nil {
		return constants.Empty, err
	}
	return ifscCode, nil
}

func (c *CommonBankBusinessLogic) GetPrimaryBankIFSCCodeFromBOWithChannel(ctx context.Context, clientCode string, ch chan modelsCommon.ChannelPrimaryBankFromBOResponse) {
	ifscCode, err := c.GetPrimaryBankIFSCCodeFromBO(ctx, clientCode)
	res := modelsCommon.ChannelPrimaryBankFromBOResponse{IFSCCode: ifscCode, Error: err}
	ch <- res
}

func (c *CommonBankBusinessLogic) GetIMPSBeneficiaryName(ctx context.Context, accountNumber, ifsc string) (string, error) {
	impsHyperVergeResponse, err := c.impsExternalProvider.GetImpsHyperVergeInfo(ctx, &modelsExternals.IMPSHyperVergeRequest{
		AccountNumber: accountNumber,
		Ifsc:          ifsc,
	})
	if err != nil {
		log.Error(ctx).Err(err).Str(constants.LogBankAccountNumberKey, accountNumber).Str("ifsc", ifsc).Msg("GetIMPSInfo: Received error from imps hyper verge")
		return constants.Empty, err
	}

	hyperVergeBeneficiaryName := getBeneficiaryNameFromIMPSHyperVergeResponse(impsHyperVergeResponse)
	if hyperVergeBeneficiaryName == constants.Empty {
		log.Error(ctx).Str(constants.LogBankAccountNumberKey, accountNumber).Str("ifsc", ifsc).Msg("GetIMPSInfo: Received empty beneficiary name from imps hyper verge")
		return constants.Empty, constants.ErrGettingIMPSInfo.WithDetails("Failed to verify bank details")
	}

	return hyperVergeBeneficiaryName, nil
}

func (c *CommonBankBusinessLogic) getCountOfPartyCodeAndFulfilmentCases(existingPartyCodes, accountNumberList []string, appNumber string) (int, int, error) {
	// fetch all applications against the Account Number.
	kycApplications, err := c.kycRepository.FetchApplicationsAgainstAccountNumberList(accountNumberList)
	if err != nil {
		return 0, 0, err
	}

	// check if newLead's entry is also present; if yes, remove it before proceeding.
	kycApplications = slices.DeleteFunc(kycApplications, func(k business.KYC) bool {
		return k.AppNumber == appNumber
	})
	// remove BO pushed closed a/c.
	if len(existingPartyCodes) > 0 {
		kycApplications = slices.DeleteFunc(kycApplications, func(k business.KYC) bool {
			if i := slices.IndexFunc(existingPartyCodes, func(p string) bool {
				return p == k.UserID
			}); i != -1 {
				return true
			}
			return false
		})
	}

	vspPendingCasesCount := 0
	fulfillmentCases := 0
	// check for party code generated, VSP pending cases.
	for i := 0; i < len(kycApplications); i++ {
		if kycApplications[i].UserID != constants.Empty {
			vspPendingCasesCount++
		} else if kycApplications[i].IsESign {
			fulfillmentCases++
		}
	}

	return vspPendingCasesCount, fulfillmentCases, nil
}

func (c *CommonBankBusinessLogic) IsCamsEligible(bankCode string) bool {
	if _, exists := config.Cams().SupportedBanks[strings.ToLower(bankCode)]; exists {
		return true
	}
	return false
}

func getAccountNumberListToCheck(accountNumber string) []string {
	modifiedAccountNumber := strings.TrimLeft(accountNumber, "0")
	var accountNumberList []string
	accountNumberList = append(accountNumberList, modifiedAccountNumber)
	for len(modifiedAccountNumber) < 18 {
		modifiedAccountNumber = "0" + modifiedAccountNumber
		accountNumberList = append(accountNumberList, modifiedAccountNumber)
	}
	return accountNumberList
}
