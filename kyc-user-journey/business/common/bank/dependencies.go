package bank

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

type KYCRepository interface {
	FetchApplicationsAgainstAccountNumberList(accountNumberList []string) ([]business.KYC, error)
}

type BankRepository interface {
	GetCountOfBankAccountLinkedApps(ctx context.Context, accountNumberList []string, appNumber string) (int64, error)
}

type RtgsRepository interface {
	GetPrimaryBankIFSCCode(ctx context.Context, clientCode string) (string, error)
}

type ClientBankMappRepository interface {
	GetPrimaryBankIFSCCode(ctx context.Context, clientCode string) (string, error)
}

type CommonBlockLeadBusinessLogic interface {
	GetBlockingClientCodeFromClientDetails(ctx context.Context, clientDetails *modelsExternals.GetClientDetailsByAttributesResponse) ([]string, []string)
}

type EmodBankRepository interface {
	GetCountOfEmodApplicationsWithAccountNumberList(ctx context.Context, accountNumberList []string, clientCode string) (int64, error)
}

type ClcmExternalProvider interface {
	FetchClientDetailsWithBankAccountNumberList(ctx context.Context, bankList, attributes []string) (*modelsExternals.GetClientDetailsByAttributesResponse, error)
}

type ImpsExternalProvider interface {
	GetInfo(ctx context.Context, request *modelsExternals.IMPSInfoRequest) (*modelsExternals.IMPSInfoCallResponse, error)
	GetImpsHyperVergeInfo(ctx context.Context, request *modelsExternals.IMPSHyperVergeRequest) (*modelsExternals.IMPSHyperVergeResponse, error)
}

type CommonBankBusinessLogic struct {
	kycRepository                KYCRepository
	bankRepository               BankRepository
	rtgsRepository               RtgsRepository
	clientBankMappRepository     ClientBankMappRepository
	commonBlockLeadBusinessLogic CommonBlockLeadBusinessLogic
	emodBankRepository           EmodBankRepository
	clcmExternalProvider         ClcmExternalProvider
	impsExternalProvider         ImpsExternalProvider
}

func NewCommonBankBusinessLogic(kycRepository KYCRepository, bankRepository BankRepository, rtgsRepository RtgsRepository, clientBankMappRepository ClientBankMappRepository,
	commonBlockLeadBusinessLogic CommonBlockLeadBusinessLogic, emodBankRepository EmodBankRepository, clcmExternalProvider ClcmExternalProvider, impsExternalProvider ImpsExternalProvider) *CommonBankBusinessLogic {
	return &CommonBankBusinessLogic{
		kycRepository:                kycRepository,
		bankRepository:               bankRepository,
		rtgsRepository:               rtgsRepository,
		clientBankMappRepository:     clientBankMappRepository,
		commonBlockLeadBusinessLogic: commonBlockLeadBusinessLogic,
		emodBankRepository:           emodBankRepository,
		clcmExternalProvider:         clcmExternalProvider,
		impsExternalProvider:         impsExternalProvider,
	}
}
