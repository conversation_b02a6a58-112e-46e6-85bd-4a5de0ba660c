package journeydecider

import (
	"context"
	"slices"
	"strings"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"
)

func (c *CommonJourneyDeciderBusinessLogic) OneKYCJourneyDecider(ctx context.Context, appNumber, mobile string) (*modelsApiV1.JourneyDeciderResponse, error) {
	// Check if the appNumber starts with EQ.
	if strings.HasPrefix(appNumber, constants.EQ) {
		isFeatureRolloutEnabled := percentagerollout.IsTimeBasedPercentageRolloutEnabled(ctx, appNumber, constants.ApplicationRolloutOnekycV2ConfigKey, config.Feature().Enable.OnekycV2, false)
		isWhitelisted := slices.Contains(config.Feature().Whitelist.OnekycV2, mobile)

		if isWhitelisted || isFeatureRolloutEnabled {
			return &modelsApiV1.JourneyDeciderResponse{
				JourneyVersion: "v2",
			}, nil
		}

		// If application is already migrated, then always return v2. This will remove the dependencies from camunda.
		isMigrated, err := c.oneKYCMigratedAppsRepository.CheckIfAppNumberExists(appNumber)
		if isMigrated {
			return &modelsApiV1.JourneyDeciderResponse{
				JourneyVersion: "v2",
			}, nil
		}
		if err != nil {
			return nil, err
		}
	}

	// Default to v1.
	return &modelsApiV1.JourneyDeciderResponse{
		JourneyVersion: "v1",
	}, nil
}
