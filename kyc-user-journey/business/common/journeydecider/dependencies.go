package journeydecider

type CommonJourneyDeciderBusinessLogic struct {
	oneKYCMigratedAppsRepository OneKYCMigratedAppsRepository
}

type OneKYCMigratedAppsRepository interface {
	CheckIfAppNumberExists(appNumber string) (bool, error)
}

func NewCommonJourneyDeciderBusinessLogic(oneKYCMigratedAppsRepository OneKYCMigratedAppsRepository) *CommonJourneyDeciderBusinessLogic {
	return &CommonJourneyDeciderBusinessLogic{
		oneKYCMigratedAppsRepository: oneKYCMigratedAppsRepository,
	}
}
