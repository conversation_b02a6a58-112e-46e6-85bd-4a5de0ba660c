package blocklead

import (
	"context"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func getOverallBlockStatus(ctx context.Context, segments []modelsV1.SegmentData, clientCode string) (bool, []modelsV1.SegmentData) {
	for k, v := range segments {
		reason := strings.ToUpper(strings.TrimSpace(v.DeactivationReason))
		switch {
		// active clients exist.
		case segments[k].InactiveFrom.After(utils.GetCurrentTime()):
			log.Info(ctx).Str(constants.LogClientCodeKey, clientCode).Str("reason", reason).Msg("GetBlockingClientCodeFromClientDetails: active clients exist")
			segments[k].IsActive = true
			segments[k].BlockNewLead = true
			return true, segments

		// closed clients exist.
		case reason == "C":
			log.Info(ctx).Str("reason", reason).Str(constants.LogClientCodeKey, clientCode).Msg("GetBlockingClientCodeFromClientDetails: closed clients exist")
			segments[k].IsClosed = true
			segments[k].BlockNewLead = shouldBlockNewLead(ctx, segments[k].InactiveFrom, clientCode)
			if segments[k].BlockNewLead {
				log.Info(ctx).Str(constants.LogClientCodeKey, clientCode).Msg("GetBlockingClientCodeFromClientDetails: blocking closed clients exist")
				return true, segments
			}

		// non-closed clients exist.
		case reason != constants.Empty && reason != "C":
			log.Info(ctx).Str("reason", reason).
				Str(constants.LogClientCodeKey, clientCode).Msg("GetBlockingClientCodeFromClientDetails: non-closed clients exist")
			segments[k].BlockNewLead = true
			return true, segments
		}
	}

	return false, segments
}

func shouldBlockNewLead(ctx context.Context, inactiveFrom time.Time, clientCode string) bool {
	waitPeriod := config.Application().Thresholds.WaitDaysForAccountClosure
	// Been less than 90d since a/c closure => block new lead.
	currentTime := utils.GetCurrentTime()
	inactivePeriod := int(currentTime.Sub(inactiveFrom).Hours()) / 24
	log.Info(ctx).Interface("currentTime", currentTime).
		Interface("inactivePeriod", inactivePeriod).
		Interface("waitPeriod", waitPeriod).
		Int("allowAfter", waitPeriod-inactivePeriod).
		Msg("GetBlockingClientCodeFromClientDetails: wait period for " + clientCode)

	return inactivePeriod <= waitPeriod
}

func IsNonClosedExist(ctx context.Context, segments []modelsV1.SegmentData, clientCode string) bool {
	for k, v := range segments {
		reason := strings.ToUpper(strings.TrimSpace(v.DeactivationReason))
		if segments[k].InactiveFrom.After(utils.GetCurrentTime()) || reason != "C" {
			log.Info(ctx).Str(constants.LogReasonKey, reason).
				Str(constants.LogClientCodeKey, clientCode).Msg("non-closed clients exist")
			return true
		}
	}

	return false
}

func GetTimeFromRecentClosedSegment(customerData []*modelsV1.ClientData) (int, string) {
	timeFromRecentClosed := -1
	var recentClosedClientID string
	for _, v := range customerData {
		for k := range v.ClientSegments {
			timeFromAccountClosure := int(utils.GetCurrentTime().Sub(v.ClientSegments[k].InactiveFrom).Hours()) / 24
			if timeFromAccountClosure < timeFromRecentClosed || timeFromRecentClosed == -1 {
				timeFromRecentClosed = timeFromAccountClosure
				recentClosedClientID = v.ClientID
			}
		}
	}

	return timeFromRecentClosed, recentClosedClientID
}
