package blocklead

import (
	"context"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (c *CommonBlockLeadBusinessLogic) GetBlockingClientCodeFromClientDetails(ctx context.Context, clientDetails *modelsV1.GetClientDetailsByAttributesResponse) ([]string, []string) {
	blockingClientCodes := make([]string, 0)
	clientCodes := make([]string, 0)
	for i, v := range clientDetails.Data.CustomerData {
		var blockStatus bool
		blockStatus, clientDetails.Data.CustomerData[i].ClientSegments = getOverallBlockStatus(ctx, v.ClientSegments, v.ClientID)
		log.Info(ctx).Str("client code", v.ClientID).
			Interface("segment status", clientDetails.Data.CustomerData[i].ClientSegments).
			Msg("GetBlockingClientCodeFromClientDetails: overall client status")
		if blockStatus {
			blockingClientCodes = append(blockingClientCodes, v.ClientID)
		}
		clientCodes = append(clientCodes, v.ClientID)
	}
	log.Info(ctx).Strs("clientCodes", clientCodes).Msg("GetBlockingClientCodeFromClientDetails: client codes")
	return blockingClientCodes, clientCodes
}

func (c *CommonBlockLeadBusinessLogic) CheckIfActiveProfileExists(ctx context.Context, attribute string,
	existingProfiles []modelsV1.Profile, skipableClientCode string) (bool, string) {
	if len(existingProfiles) == 0 {
		log.Info(ctx).Msg("no existing profiles found for " + attribute)
		return false, constants.Empty
	}

	// if an active profile exists.
	for i := 0; i < len(existingProfiles); i++ {
		v := &existingProfiles[i]
		if (skipableClientCode == constants.Empty || skipableClientCode != v.ClientID) && (v.Active || v.DeactiveReason == "D" || v.DeactiveReason == "I") {
			return true, v.ClientID
		}
	}

	// No active profiles found.
	log.Info(ctx).Msg("only closed profiles found for " + attribute)
	return false, constants.Empty
}

func (c *CommonBlockLeadBusinessLogic) CheckIfRecentClosedProfileExists(ctx context.Context, existingProfiles []modelsV1.Profile) (bool, string) {
	utcTimeFormat := config.Application().Validations.UTCFormat
	waitDaysForAccountClosure := config.Application().Thresholds.WaitDaysForAccountClosure

	for i := 0; i < len(existingProfiles); i++ {
		v := &existingProfiles[i]
		inactiveFrom, err := time.Parse(utcTimeFormat, v.InactiveDate)
		if err != nil {
			log.Error(ctx).Err(err).Msg("error parsing inactive date")
			continue
		}
		// Been less than 90 days since a/c closure => block new lead.
		currentTime := utils.GetCurrentTime()
		inactivePeriod := int(currentTime.Sub(inactiveFrom).Hours()) / 24
		if inactivePeriod <= waitDaysForAccountClosure {
			log.Info(ctx).Interface("currentTime", currentTime).
				Interface("inactivePeriod", inactivePeriod).
				Interface("waitDaysForAccountClosure", waitDaysForAccountClosure).
				Int("allowAfter", waitDaysForAccountClosure-inactivePeriod).
				Msg("ongoing wait period for closed profile: " + v.ClientID)
			return true, v.ClientID
		}
	}
	return false, constants.Empty
}
