package journeydecider

import (
	"context"

	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

type JourneyDeciderBusinessLogic struct {
	statusBusinessLogic               StatusBusinessLogic
	resetBusinessLogic                OnboardingResetBusinessLogic
	commonJourneyDeciderBusinessLogic CommonJourneyDeciderBusinessLogic
	oneKYCMigratedAppsRepository      OneKYCMigratedAppsRepository
	stpRejectionsRepository           STPRejectionsRepository
	scrutinyRejectionsRepository      ScrutinyRejectionsRepository
	startRepository                   StartRepository
	bankRepository                    BankRepository
}
type BankRepository interface {
	UpdateJointAccountConfirmationExempt(ctx context.Context, appNumber string, isJointAccountConfirmationExempt bool) error
}

type STPRejectionsRepository interface {
	FetchActiveStpRejectionsForAppNumber(appNumber string) ([]business.Rejection, error)
}

type ScrutinyRejectionsRepository interface {
	FetchActiveScrutinyRejectionsForAppNumber(appNumber string) ([]business.Rejection, error)
}
type StartRepository interface {
	UpdateApplicationStatusForSpecifcAppStatus(ctx context.Context, appNumber, source string, applicationStatus, currentApplicationStatus int) error
}

type CommonJourneyDeciderBusinessLogic interface {
	OneKYCJourneyDecider(ctx context.Context, appNumber, mobile string) (*modelsAPIV1.JourneyDeciderResponse, error)
}

type StatusBusinessLogic interface {
	GetStatusResponse(ctx context.Context, appNumber, mobile, kycJourneyType string, skipClientData, includeOneKYCStatus, ignoreRejections bool) (*modelsAPIV1.StatusAPIResponse, error)
}
type OnboardingResetBusinessLogic interface {
	Reset(ctx context.Context, request *modelsAPIV1.ResetAPIRequest) error
}
type OneKYCMigratedAppsRepository interface {
	FreshUpsert(ctx context.Context, onekycMigratedAppsDTO *business.OneKYCMigratedApps) error
}

func NewJourneyDeciderBusinessLogic(
	commonJourneyDeciderBusinessLogic CommonJourneyDeciderBusinessLogic,
	statusBusinessLogic StatusBusinessLogic,
	resetBusinessLogic OnboardingResetBusinessLogic,
	oneKYCMigratedAppsRepository OneKYCMigratedAppsRepository,
	stpRejectionsRepository STPRejectionsRepository,
	scrutinyRejectionsRepository ScrutinyRejectionsRepository,
	startRepository StartRepository,
	bankRepository BankRepository,
) *JourneyDeciderBusinessLogic {
	return &JourneyDeciderBusinessLogic{
		commonJourneyDeciderBusinessLogic: commonJourneyDeciderBusinessLogic,
		statusBusinessLogic:               statusBusinessLogic,
		resetBusinessLogic:                resetBusinessLogic,
		oneKYCMigratedAppsRepository:      oneKYCMigratedAppsRepository,
		stpRejectionsRepository:           stpRejectionsRepository,
		scrutinyRejectionsRepository:      scrutinyRejectionsRepository,
		startRepository:                   startRepository,
		bankRepository:                    bankRepository,
	}
}
