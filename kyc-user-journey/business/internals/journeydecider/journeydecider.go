package journeydecider

import (
	"context"
	"slices"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/entities"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func (j *JourneyDeciderBusinessLogic) JourneyDecider(ctx context.Context, request *modelsApiV1.JourneyDeciderRequest) (*modelsApiV1.JourneyDeciderResponse, error) {
	appNumber := request.Meta.AppNumber
	mobile := request.Meta.Mobile

	res, err := j.getCurrentOneKYCJourney(ctx, request)
	if err != nil {
		return res, err
	}

	if isEligibleForMigration(ctx, appNumber, mobile) && res.ShouldMigrateToV2 {
		if errMigrate := j.migrate(ctx, request); errMigrate != nil {
			return res, errMigrate
		}
		// App Migrated, redirect to v2.
		res.JourneyVersion = "v2"
	}

	return res, err
}

func (j *JourneyDeciderBusinessLogic) getCurrentOneKYCJourney(ctx context.Context, request *modelsApiV1.JourneyDeciderRequest) (*modelsApiV1.JourneyDeciderResponse, error) {
	res, err := j.commonJourneyDeciderBusinessLogic.OneKYCJourneyDecider(ctx, request.Meta.AppNumber, request.Meta.Mobile)
	if err != nil {
		return res, err
	}

	if isAdminSource(request.Source) && res.JourneyVersion == "v1" {
		res.ShouldMigrateToV2 = true
	}

	return res, err
}

func (j *JourneyDeciderBusinessLogic) migrate(ctx context.Context, request *modelsApiV1.JourneyDeciderRequest) error {
	// Update Application status for rejected applications
	err := j.handleApplicationStatusForRejectedApps(ctx, request.Meta.AppNumber)
	if err != nil {
		return err
	}

	// Setting includeOneKycStatus to true as the status API business logic is alreayd handling the v1 onekyc to non-camunda based user journey conversion logic.
	includeOneKycStatus := true
	statusResponse, err := j.statusBusinessLogic.GetStatusResponse(ctx, request.Meta.AppNumber, request.Meta.Mobile, "", false, includeOneKycStatus, false)
	if err != nil {
		return err
	}
	if statusResponse != nil && isUserJourneyInProgress(statusResponse.Lead.Metadata.ApplicationStatus) {
		// Reset the flows that are incomplete and are allowed to be reset to avoid any flow status related issue.
		for _, val := range statusResponse.Lead.Flows {
			if val.Status == constants.OnboardingFlowStatusIncomplete && slices.Contains(config.Application().Validations.ResetFlow.AllowedFlowsForOnekycMigration, val.Name) {
				log.Info(ctx).Msgf("OneKYCV1Migration: Reset called for %s flow", val.Name)
				_ = j.resetBusinessLogic.Reset(ctx, getResetRequest(request, val.Name))
			}
			if val.Name == constants.BankFlowName && val.Status == constants.OnboardingFlowStatusComplete && val.Data != nil && !val.Data.(modelsApiV1.StatusAPIResponseLeadFlowDataBank).JointAccConfirmed {
				log.Info(ctx).Msg("OneKYCV1Migration: Updated Bank is_joint_account_confirmation_exempt to true")
				_ = j.bankRepository.UpdateJointAccountConfirmationExempt(ctx, request.Meta.AppNumber, true)
			}
		}
	}

	// App migrated, now add it to the migration table.
	return j.oneKYCMigratedAppsRepository.FreshUpsert(ctx, &business.OneKYCMigratedApps{AppNumber: request.Meta.AppNumber})
}

func (j *JourneyDeciderBusinessLogic) handleApplicationStatusForRejectedApps(ctx context.Context, appNumber string) error {
	rejections, err := j.stpRejectionsRepository.FetchActiveStpRejectionsForAppNumber(appNumber)
	if err != nil {
		return err
	}

	if len(rejections) > 0 {
		return j.startRepository.UpdateApplicationStatusForSpecifcAppStatus(ctx, appNumber, constants.ApplicationName, entities.StatusSTPRejectionJourneyInProgressForAdmins, entities.StatusFulfilmentInProgress)
	}

	rejections, err = j.scrutinyRejectionsRepository.FetchActiveScrutinyRejectionsForAppNumber(appNumber)
	if err != nil {
		return err
	}

	if len(rejections) > 0 {
		return j.startRepository.UpdateApplicationStatusForSpecifcAppStatus(ctx, appNumber, constants.ApplicationName, entities.StatusScrutinyRejectionJourneyInProgressForAdmins, entities.StatusFulfilmentInProgress)
	}

	return nil
}
