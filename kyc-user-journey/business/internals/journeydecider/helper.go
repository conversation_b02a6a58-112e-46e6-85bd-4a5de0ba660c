package journeydecider

import (
	"context"
	"slices"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsApiV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"
)

func getResetRequest(request *modelsApiV1.JourneyDeciderRequest, flow string) *modelsApiV1.ResetAPIRequest {
	return &modelsApiV1.ResetAPIRequest{
		Headers: request.Headers,
		Meta:    request.Meta,
		Data: modelsApiV1.ResetAPIRequestData{
			Flow:      flow,
			SkipComms: true,
		},
	}
}

func isAdminSource(source string) bool {
	return slices.Contains(config.Application().Validations.AllowedAdminSources, source)
}

func isEligibleForMigration(ctx context.Context, appNumber, mobile string) bool {
	isFeatureRolloutEnabled := percentagerollout.IsFeatureRolloutEnabled(ctx, appNumber, constants.ApplicationRolloutOnekycMigrationConfigKey, config.Feature().Enable.OnekycMigration, false)
	isWhitelisted := slices.Contains(config.Feature().Whitelist.OnekycMigration, mobile)
	return isWhitelisted || isFeatureRolloutEnabled
}

func isUserJourneyInProgress(status string) bool {
	if status == constants.ApplicationStatusSubmitted || status == constants.ApplicationStatusCoded {
		return false
	}
	return true
}
