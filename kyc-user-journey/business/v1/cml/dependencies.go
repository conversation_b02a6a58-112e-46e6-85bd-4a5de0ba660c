package cml

import (
	"context"
	"time"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type CmlRepository interface {
	FetchClientMaster(ctx context.Context, clientCode string) (*business.ClientMasterDetails, error)
	FetchClientMasterNomineeDetails(ctx context.Context, boID string) ([]business.NomineeDetails, error)
}

type CdslAppRepository interface {
	FetchAccountClosureTimestampForClientCode(ctx context.Context, clientCode string) (time.Time, error)
}

type CmlBusinessLogic struct {
	cmlRepository     CmlRepository
	cdslAppRepository CdslAppRepository
}

func NewCmlBusinessLogic(cmlRepository CmlRepository, cdslAppRepository CdslAppRepository) *CmlBusinessLogic {
	return &CmlBusinessLogic{
		cmlRepository:     cmlRepository,
		cdslAppRepository: cdslAppRepository,
	}
}
