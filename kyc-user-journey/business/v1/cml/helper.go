package cml

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"os"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils/errors"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"software.sslmate.com/src/go-pkcs12"
)

func loadPFX(pfxPath, password string, ctx context.Context) (*rsa.PrivateKey, *x509.Certificate, error) {
	pfxData, err := os.ReadFile(pfxPath)
	if err != nil {
		log.Error(ctx).Msgf("failed to read PFX file: %v", err)
		return nil, nil, err
	}

	// Decode the PFX data
	priv, cert, _, err := pkcs12.DecodeChain(pfxData, password)
	if err != nil {
		log.Error(ctx).Msgf("failed to decode PFX file: %v", err)
		return nil, nil, err
	}

	if priv == nil {
		return nil, nil, errors.New("private key not found in PFX file")
	}
	if cert == nil {
		return nil, nil, errors.New("cert key not found in PFX file")
	}

	return priv.(*rsa.PrivateKey), cert, nil
}

func ConvertDataToCMLReportStructure(clientMasterDetails *business.ClientMasterDetails, nomineeDetails []business.NomineeDetails, accountClosureDate string) business.CMLReportPdfStructure {
	var result business.CMLReportPdfStructure

	// Mapping fields from ClientMasterDetails to CMLReportPdfStructure
	result.DPID = clientMasterDetails.DPID
	result.ClientID = clientMasterDetails.ClientID
	result.Sex = config.CML().Gender[clientMasterDetails.Sex]
	if result.Sex == constants.Empty {
		result.Sex = clientMasterDetails.Sex
	}

	result.DPIntRefNO = clientMasterDetails.DPIntRefNO
	result.AccStatus = clientMasterDetails.AccStatus
	result.AccOpeningDt = clientMasterDetails.AccOpeningDt.Format(constants.StringDateFormatDdMmmYyyy)
	result.PurchaseWaiver = clientMasterDetails.PurchaseWaiver
	result.BOStatus = clientMasterDetails.BOStatus
	result.BOSubStatus = clientMasterDetails.BOSubStatus
	result.AccCategory = clientMasterDetails.AccCategory
	result.FreezeStatus = clientMasterDetails.FreezeStatus
	result.RegisteredForEasi = clientMasterDetails.RegisteredForEasi
	result.Nationality = config.CML().Nationality[clientMasterDetails.Nationality]
	if result.Nationality == constants.Empty {
		result.Nationality = clientMasterDetails.Nationality
	}
	result.ClosureInitBy = config.CML().ClosureInitBy[clientMasterDetails.ClosInitBy]
	result.StmtCycle = config.CML().StmtCycle[clientMasterDetails.StmtCycle]

	result.Occupation = config.CML().Occupation[clientMasterDetails.Occupation]
	if result.Occupation == constants.Empty {
		result.Occupation = clientMasterDetails.Occupation
	}
	result.AccClosureDt = accountClosureDate
	result.RegisteredForEasiest = clientMasterDetails.RegisteredForEasiest
	if clientMasterDetails.SMSRegister == "Y" {
		result.SMSRegister = constants.YesString
	} else {
		result.SMSRegister = constants.NoString
	}
	result.SMSMobileNo = clientMasterDetails.SMSMobileNo
	result.UID = clientMasterDetails.UID
	result.RBIRefNo = clientMasterDetails.RBIRefNo
	if clientMasterDetails.RBIApprovalDt.Equal(time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)) {
		result.RBIApprovalDt = constants.Empty
	} else {
		result.RBIApprovalDt = clientMasterDetails.RBIApprovalDt.Format(constants.StringDateFormatDdMmmYyyy)
	}
	if clientMasterDetails.BSDAFlag == "Y" {
		result.BSDAFlag = constants.YesString
	} else {
		result.BSDAFlag = constants.NoString
	}
	if clientMasterDetails.RGESSFlag == "Y" {
		result.RGESSFlag = constants.YesString
	} else {
		result.RGESSFlag = constants.NoString
	}
	result.PledgeSIFlag = clientMasterDetails.PledgeSIFlag
	if clientMasterDetails.EmailFlag == "Y" {
		result.EmailFlag = constants.YesString
	} else {
		result.EmailFlag = constants.NoString
	}
	result.AnnualReportFlag = clientMasterDetails.AnnualReportFlag
	result.FirstHolderName = clientMasterDetails.FirstHolderName
	result.FirstHolderPAN = clientMasterDetails.FirstHolderPAN
	result.FirstHolderDOB = clientMasterDetails.FirstHolderDOB

	// Parse string into time.Time object
	if clientMasterDetails.FirstHolderDOB != constants.Empty {
		dob, _ := time.Parse(constants.TimeFormatYyyyMmDdWithHyphen, clientMasterDetails.FirstHolderDOB)
		result.FirstHolderDOB = dob.Format(constants.StringDateFormatDdMmmYyyy)
	}
	result.SecondHolderName = clientMasterDetails.SecondHolderName
	result.SecondHolderPAN = clientMasterDetails.SecondHolderPAN
	result.SecondHolderDOB = ""
	result.ThirdHolderName = clientMasterDetails.ThirdHolderName
	result.ThirdHolderPAN = clientMasterDetails.ThirdHolderPAN
	result.ThirdHolderDOB = ""
	result.CorrespondenceAddress = clientMasterDetails.CorrespondenceAddress
	result.CorrespondenceAddressPhone = clientMasterDetails.CorrespondenceAddressPhone
	result.CorrespondenceAddressFAX = clientMasterDetails.CorrespondenceAddressFAX
	result.PermanentAddress = clientMasterDetails.PermanentAddress
	result.PermanentAddressPhone = clientMasterDetails.PermanentAddressPhone
	result.PermanentAddressFAX = clientMasterDetails.PermanentAddressFAX
	result.Email = clientMasterDetails.Email
	result.BankName = clientMasterDetails.BankName
	result.BankAccNo = clientMasterDetails.BankAccNo
	result.MICRCode = clientMasterDetails.MICRCode

	result.BankAccType = config.CML().BankAccType[clientMasterDetails.BankAccType]
	if result.BankAccType == constants.Empty {
		result.BankAccType = clientMasterDetails.BankAccType
	}
	result.IFSCCode = clientMasterDetails.IFSCCode
	result.ECSFlag = clientMasterDetails.ECSFlag
	result.POAMasterID = clientMasterDetails.POAMasterID
	result.POAReference = clientMasterDetails.POAReference
	result.Holder = getHolder(clientMasterDetails)
	ConvertNomineeDataToCMLReportStructure(nomineeDetails, &result)

	return result
}

func getHolder(clientMasterDetails *business.ClientMasterDetails) string {
	if strings.TrimSpace(clientMasterDetails.SecondHolderName) == constants.Empty && strings.TrimSpace(clientMasterDetails.ThirdHolderName) == constants.Empty {
		return "First Holder"
	} else if strings.TrimSpace(clientMasterDetails.SecondHolderName) != constants.Empty && strings.TrimSpace(clientMasterDetails.ThirdHolderName) == constants.Empty {
		return "Second Holder"
	} else {
		return "Third Holder"
	}
}

func ConvertNomineeDataToCMLReportStructure(nomineeDetails []business.NomineeDetails, result *business.CMLReportPdfStructure) {
	for key, val := range nomineeDetails {
		switch key {
		case 0:
			result.Nominee1Name = getFullname(&val)
			result.Nominee1PerShare = strings.TrimSpace(val.PercOFShares)
			if strings.TrimSpace(val.NOMSrNo) == "1" {
				result.Nominee1ResidualSecFlag = constants.YesString
			} else {
				result.Nominee1ResidualSecFlag = constants.NoString
			}
		case 1:
			result.Nominee2Name = getFullname(&val)
			result.Nominee2PerShare = strings.TrimSpace(val.PercOFShares)
			if strings.TrimSpace(val.NOMSrNo) == "1" {
				result.Nominee2ResidualSecFlag = constants.YesString
			} else {
				result.Nominee2ResidualSecFlag = constants.NoString
			}
		case 2:
			result.Nominee3Name = getFullname(&val)
			result.Nominee3PerShare = strings.TrimSpace(val.PercOFShares)
			if strings.TrimSpace(val.NOMSrNo) == "1" {
				result.Nominee3ResidualSecFlag = constants.YesString
			} else {
				result.Nominee3ResidualSecFlag = constants.NoString
			}
		}
	}
}

func getFullname(val *business.NomineeDetails) string {
	firstName := strings.TrimSpace(val.FirstName)
	middleName := strings.TrimSpace(val.MiddleName)
	lastName := strings.TrimSpace(val.LastName)

	// Construct the full name
	fullname := firstName
	if middleName != "" {
		fullname += " " + middleName
	}
	if lastName != "" {
		fullname += " " + lastName
	}

	return fullname
}
