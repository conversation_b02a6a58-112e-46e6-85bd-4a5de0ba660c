package cml

import (
	"bytes"
	"context"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils/pdf"
	"github.com/unidoc/unipdf/v3/annotator"
	"github.com/unidoc/unipdf/v3/core"
	unidocModel "github.com/unidoc/unipdf/v3/model"
	"github.com/unidoc/unipdf/v3/model/sighandler"
)

func (c *CmlBusinessLogic) GenerateAndSignCMLReportPdf(ctx context.Context, clientCode string) ([]byte, error) {
	// now make the database call
	clientMasterDetails, err := c.cmlRepository.FetchClientMaster(ctx, clientCode)
	if err != nil {
		return nil, err
	}

	accountClosureDate, err := c.checkAndUpdateAccountClosureDate(ctx, clientMasterDetails.AccClosureDt, clientCode)
	if err != nil {
		return nil, err
	}

	var nomineeDetails []business.NomineeDetails
	if strings.Contains(clientMasterDetails.BOSubStatus, "NEGATIVE") {
		log.Debug(ctx).Msgf("clientCode %s Opted out for nominee", clientCode)
	} else {
		nomineeDetails, err = c.cmlRepository.FetchClientMasterNomineeDetails(ctx, clientMasterDetails.BoID)
		if err != nil {
			return nil, err
		}
	}

	NomineePdfStructure := ConvertDataToCMLReportStructure(clientMasterDetails, nomineeDetails, accountClosureDate)
	Map, err := pdf.ConvertStructureToMap(NomineePdfStructure)
	if err != nil {
		log.Error(ctx).Err(constants.ErrParsingStructure.WithDetails(err)).Msgf("error parsing cml report data for clientId %s", clientCode)
		return nil, err
	}
	path := config.CML().Template.Cml
	pdfData, err := pdf.FillHTMLAndConvertIntoPdf(path, Map)
	if err != nil {
		log.Error(ctx).Err(constants.ErrHTMLParsing.WithDetails(err)).Msgf("error parsing cmlreport template for clientId %s", clientCode)
		return nil, err
	}

	pfxPath := config.CML().PfxDetails.PfxFilePath
	password := config.CML().PfxDetails.PfxFilePassword
	// Get private key and X509 certificate from the P12 file.
	priv, cert, err := loadPFX(pfxPath, password, ctx)
	if err != nil {
		log.Error(ctx).Msgf("failed to load PFX file: %v", err)
		return nil, err
	}

	// Create reader.
	reader := bytes.NewReader(pdfData)
	pdfReader, err := unidocModel.NewPdfReader(reader)
	if err != nil {
		log.Error(ctx).Msgf("failed to create PDF reader: %v", err)
		return nil, err
	}

	// Create appender.
	appender, err := unidocModel.NewPdfAppender(pdfReader)
	if err != nil {
		log.Error(ctx).Msgf("failed to create PDF appender: %v", err)
		return nil, err
	}

	// Create signature handler.
	handler, err := sighandler.NewAdobePKCS7Detached(priv, cert)
	if err != nil {
		log.Error(ctx).Msgf("failed to create signature handler: %v", err)
		return nil, err
	}

	// Create signature.
	signature := unidocModel.NewPdfSignature(handler)
	signature.SetName("Digitally signed by Angel Broking LTD")
	signature.SetReason("DigitallySignedPDF")
	signature.SetDate(time.Now(), "2006-01-02 15:04:05")

	if err = signature.Initialize(); err != nil {
		log.Error(ctx).Msgf("failed to initialize signature: %v", err)
		return nil, err
	}

	// Create signature field and appearance.
	opts := annotator.NewSignatureFieldOpts()
	opts.FontSize = 20
	opts.Rect = []float64{50, 100, 140, 130}

	field, err := annotator.NewSignatureField(
		signature,
		[]*annotator.SignatureLine{
			annotator.NewSignatureLine("Name", "Digitally signed by Angel Broking LTD"),
			annotator.NewSignatureLine("Date", time.Now().Format("2006-01-02 15:04:05")),
			annotator.NewSignatureLine("Location", "Mumbai CSO"),
		},
		opts,
	)
	if err != nil {
		log.Error(ctx).Msgf("failed to create signature field: %v", err)
		return nil, err
	}

	field.T = core.MakeString("Self signed PDF")

	if err = appender.Sign(1, field); err != nil {
		log.Error(ctx).Msgf("failed to sign PDF: %v", err)
		return nil, err
	}

	// Write output PDF file.
	var signedPdfBuffer bytes.Buffer
	err = appender.Write(&signedPdfBuffer)
	if err != nil {
		log.Error(ctx).Msgf("failed to write signed PDF to buffer: %v", err)
		return nil, err
	}

	return signedPdfBuffer.Bytes(), nil
}

func (c *CmlBusinessLogic) checkAndUpdateAccountClosureDate(ctx context.Context, clientMasterAccClosureDate time.Time, clientCode string) (string, error) {
	var accountClosureDate string
	if clientMasterAccClosureDate.Equal(time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)) {
		// fetch closure date from cdsl table
		accountClosure, err := c.cdslAppRepository.FetchAccountClosureTimestampForClientCode(ctx, clientCode)
		if err != nil {
			return accountClosureDate, err
		}
		if accountClosure.IsZero() {
			accountClosureDate = constants.Empty
		} else {
			accountClosureDate = accountClosure.Format(constants.StringDateFormatDdMmmYyyy)
		}
	} else {
		// date present in internal in house db
		accountClosureDate = clientMasterAccClosureDate.Format(constants.StringDateFormatDdMmmYyyy)
	}
	return accountClosureDate, nil
}
