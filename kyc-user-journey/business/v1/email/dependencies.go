package email

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type EmailRepository interface {
	FreshUpsert(ctx context.Context, emailDTO *business.Email) error
	FetchEmailForAppNumber(ctx context.Context, appNumber string) (*business.Email, error)
}

type EmailTokenRepository interface {
	FreshUpsert(ctx context.Context, emailTokenDTO *business.EmailToken) error
}

type CommonEmailBusinessLogic interface {
	CheckIfDuplicateEmail(ctx context.Context, email, normalizedEmail, appNumber string, isGoogleVerified bool, skiableClientCode string) (*business.CheckIfDuplicateEmailResponse, error)
}

type EmailOTPBusinessLogic interface {
	SendEmailOTP(ctx context.Context, email string) error
}

type KRABusinessLogic interface {
	SaveAddressIfKRACompliant(ctx context.Context, appNumber, source string) error
}

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type EmailBusinessLogicImpl struct {
	emailRepository          EmailRepository
	commonEmailBusinessLogic CommonEmailBusinessLogic
	emailOTPBusinessLogic    EmailOTPBusinessLogic
	emailTokenRepository     EmailTokenRepository
	kraBusinessLogic         KRABusinessLogic
	lsqBusinessLogic         LSQBusinessLogic
}

func NewEmailBusinessLogic(emailRepository EmailRepository, commonEmailBusinessLogic CommonEmailBusinessLogic,
	emailOTPBusinessLogic EmailOTPBusinessLogic, emailTokenRepository EmailTokenRepository, kraBusinessLogic KRABusinessLogic, lsqBusinessLogic LSQBusinessLogic) *EmailBusinessLogicImpl {
	return &EmailBusinessLogicImpl{
		emailRepository:          emailRepository,
		commonEmailBusinessLogic: commonEmailBusinessLogic,
		emailOTPBusinessLogic:    emailOTPBusinessLogic,
		emailTokenRepository:     emailTokenRepository,
		kraBusinessLogic:         kraBusinessLogic,
		lsqBusinessLogic:         lsqBusinessLogic,
	}
}
