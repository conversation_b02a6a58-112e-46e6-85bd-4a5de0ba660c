package email

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/entities"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"google.golang.org/api/idtoken"
)

func (e *EmailBusinessLogicImpl) EmailOTP(ctx context.Context, request *modelsV1.EmailAPIRequest) (*modelsV1.EmailAPIResponse, error) {
	normalizedEmail, err := GetNormalizedEmail(request.Data.Email)
	if err != nil {
		return nil, err
	}

	checkIfDuplicateEmailResponse, checkIfDuplicateEmailErr := e.commonEmailBusinessLogic.CheckIfDuplicateEmail(ctx, request.Data.Email, normalizedEmail, request.AppNumber, false, request.ClientCode)
	if checkIfDuplicateEmailErr != nil {
		return nil, checkIfDuplicateEmailErr
	}

	if checkIfDuplicateEmailResponse.DuplicateEmailStatus != entities.EmailNotDuplicateStatus {
		e.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.DuplicateEmailLSQEvent)
		return nil, constants.ErrDuplicateEmail.WithDetails("This email is already used")
	}

	emailDTO := getEmailDTOFromEmailRequest(request, normalizedEmail)
	err = e.emailRepository.FreshUpsert(ctx, &emailDTO)
	if err != nil {
		return nil, err
	}

	err = e.emailOTPBusinessLogic.SendEmailOTP(ctx, emailDTO.Email)
	if err != nil {
		log.Error(ctx).Err(err).Str("email", emailDTO.Email).Msg("Error sending otp to provided email")
		return nil, err
	}

	return &modelsV1.EmailAPIResponse{Email: emailDTO.Email}, nil
}

func (e *EmailBusinessLogicImpl) EmailGoogleSignIn(ctx context.Context, request *modelsV1.EmailGoogleSignInAPIRequest) (*modelsV1.EmailGoogleSignInAPIResponse, error) {
	payload, err := idtoken.ParsePayload(request.Data.CredentialToken)
	if err != nil {
		log.Error(ctx).Err(err).Str("token", request.Data.CredentialToken).Msg("Unable to validate token")
		return nil, constants.ErrInValidEmailToken.WithDetails(err.Error())
	}

	if time.Now().Unix() > payload.Expires {
		log.Error(ctx).Err(err).Str("token", request.Data.CredentialToken).Msg("Expired email token")
		return nil, constants.ErrEmailTokenExpired.Value()
	}

	claimsName, ok := payload.Claims["name"]
	var emailName string
	if ok {
		emailName = claimsName.(string)
	} else {
		log.Error(ctx).Str("token", request.Data.CredentialToken).Msg("Unable to read name from the provided token")
	}

	claimsEmail, ok := payload.Claims["email"]
	if !ok {
		log.Error(ctx).Str("token", request.Data.CredentialToken).Msg("Unable to read email from the provided token")
		return nil, constants.ErrInValidEmailToken.Value()
	}

	email := claimsEmail.(string)
	parts := strings.Split(email, constants.AtRate)
	domain := parts[len(parts)-1]
	domainParts := strings.Split(domain, constants.Dot)
	domainWithoutExtension := domainParts[0]
	allowDomainList := config.Application().Validations.Email.Allow.Domain
	if !slices.Contains(allowDomainList, strings.ToLower(domainWithoutExtension)) {
		return nil, constants.ErrDomainNotAllowed.WithMessage(fmt.Sprintf(constants.ErrDomainNotAllowed.Message, domain))
	}
	checkIfDuplicateEmailResponse, checkIfDuplicateEmailErr := e.commonEmailBusinessLogic.CheckIfDuplicateEmail(ctx, email, email, request.AppNumber, true, request.ClientCode)
	if checkIfDuplicateEmailErr != nil {
		return nil, checkIfDuplicateEmailErr
	}

	if checkIfDuplicateEmailResponse.DuplicateEmailStatus != entities.EmailNotDuplicateStatus {
		e.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.DuplicateEmailLSQEvent)
		return nil, constants.ErrDuplicateEmail.WithDetails("This email is already used")
	}

	emailDTO := getEmailDTOFromEmailGoogleSignInRequest(email, request)
	err = e.emailRepository.FreshUpsert(ctx, emailDTO)
	if err != nil {
		return nil, err
	}

	emailTokenDTO := getEmailTokenDTOFromEmailGoogleSignInRequest(request)
	_ = e.emailTokenRepository.FreshUpsert(ctx, emailTokenDTO)
	return &modelsV1.EmailGoogleSignInAPIResponse{Email: email, EmailName: emailName}, nil
}
