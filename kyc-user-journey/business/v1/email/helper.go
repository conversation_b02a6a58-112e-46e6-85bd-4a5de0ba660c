package email

import (
	"slices"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func getEmailDTOFromEmailRequest(request *modelsV1.EmailAPIRequest, normalizedEmail string) business.Email {
	return business.Email{
		AppNumber:        request.AppNumber,
		Email:            request.Data.Email,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		IsVerified:       false,
		NormalizedEmail:  normalizedEmail,
	}
}

func getEmailDTOFromEmailGoogleSignInRequest(email string, request *modelsV1.EmailGoogleSignInAPIRequest) *business.Email {
	return &business.Email{
		AppNumber:        request.AppNumber,
		Email:            email,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		IsVerified:       true,
		NormalizedEmail:  email,
		VerifiedBy:       constants.Google,
		VerifiedTS:       time.Now(),
	}
}

func getEmailTokenDTOFromEmailGoogleSignInRequest(request *modelsV1.EmailGoogleSignInAPIRequest) *business.EmailToken {
	return &business.EmailToken{
		AppNumber:        request.AppNumber,
		Medium:           constants.Google,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		Value:            request.Data.CredentialToken,
		Type:             constants.CredentialToken,
	}
}

func GetNormalizedEmail(email string) (string, error) {
	email = strings.ToLower(strings.TrimSpace(email))
	parts := strings.Split(email, constants.AtRate)
	domainWhereDotNotMatter := config.Application().Validations.Email.DotNotMatter.Domain
	domainWherePlusNotMatter := config.Application().Validations.Email.PlusNotMatter.Domain
	if len(parts) < 2 {
		return email, constants.ErrInvalidEmail.Value()
	}

	localPart := strings.Join(parts[0:len(parts)-1], constants.Empty)
	domain := parts[len(parts)-1]
	if slices.Contains(domainWherePlusNotMatter, strings.ToLower(domain)) {
		splitWithPlus := strings.Split(localPart, constants.PLUS)
		localPart = splitWithPlus[0]
	}

	if slices.Contains(domainWhereDotNotMatter, strings.ToLower(domain)) {
		localPart = strings.ReplaceAll(localPart, constants.DOT, constants.Empty)
	}

	return localPart + constants.AtRate + domain, nil
}
