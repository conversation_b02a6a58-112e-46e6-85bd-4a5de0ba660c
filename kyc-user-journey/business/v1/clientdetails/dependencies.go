package clientdetails

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type ClientDetailsRepository interface {
	UpsertClientDetailsEntities(ctx context.Context, incomeDTO *business.Income, occupationDTO *business.Occupation,
		personalDTO *business.Personal, userPreferencesDTO *business.UserPreferences, depositoryDTO *business.Depository, panDTO *business.Pan) error
}

type ClientDetailsBusinessLogic struct {
	clientDetailsRepository ClientDetailsRepository
	poaRepository           POARepository
}

type POARepository interface {
	FetchPOAForAppNumberWithContext(ctx context.Context, appNumber string) (business.POA, error)
}

func NewClientDetailsBusinessLogic(clientDetailsRepository ClientDetailsRepository, poaRepository POARepository) *ClientDetailsBusinessLogic {
	return &ClientDetailsBusinessLogic{
		clientDetailsRepository: clientDetailsRepository,
		poaRepository:           poaRepository,
	}
}

type NriClientDetailsRepository interface {
	UpsertNriClientDetailsEntities(ctx context.Context, incomeDTO *business.Income, occupationDTO *business.Occupation,
		personalDTO *business.Personal, nriPersonalDTO *business.NriPersonalDetails) error
}

type ClientDetailsNriBusinessLogic struct {
	nriClientDetailsRepository NriClientDetailsRepository
}

func NewNriClientDetailsBusinessLogic(nriClientDetailsRepository NriClientDetailsRepository) *ClientDetailsNriBusinessLogic {
	return &ClientDetailsNriBusinessLogic{
		nriClientDetailsRepository: nriClientDetailsRepository,
	}
}
