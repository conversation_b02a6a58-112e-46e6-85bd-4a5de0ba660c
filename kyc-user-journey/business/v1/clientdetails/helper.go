package clientdetails

import (
	"slices"

	"github.com/angel-one/kyc-user-journey/config"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func getOccupationDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.Occupation {
	return &business.Occupation{
		AppNumber:          request.AppNumber,
		Occupation:         request.Data.Occupation,
		OtherSpecification: request.Data.OthersSpecification,
		CreateSource:       request.Source,
		LastUpdateSource:   request.Source,
		WhatsAppOptIn:      request.Data.WhatsappOptIn,
	}
}

func getIncomeDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.Income {
	return &business.Income{
		AppNumber:        request.AppNumber,
		Income:           request.Data.Income,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
}

func getPersonalDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.Personal {
	return &business.Personal{
		AppNumber:           request.AppNumber,
		Gender:              request.Data.Gender,
		MaritalStatus:       request.Data.MaritalStatus,
		CreateSource:        request.Source,
		LastUpdateSource:    request.Source,
		GuardianName:        request.Data.GuardianName,
		SettlementFrequency: request.Data.SettlementFrequency,
		GuardianType:        "F",
	}
}

func getPreferencesDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.UserPreferences {
	return &business.UserPreferences{
		AppNumber:           request.AppNumber,
		MTF:                 request.Data.MTFOptIn,
		CreateSource:        request.Source,
		LastUpdateSource:    request.Source,
		WhatsAppOptIn:       request.Data.WhatsappOptIn,
		SettlementFrequency: request.Data.SettlementFrequency,
		DDPIOptIn:           false,
	}
}

func getDepositoryDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.Depository {
	return &business.Depository{
		AppNumber:        request.AppNumber,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		DDPIOptIn:        false,
	}
}

func getPanDTOFromRequest(request *modelsAPIv1.ClientDetailsAPIRequest) *business.Pan {
	return &business.Pan{
		AppNumber:        request.AppNumber,
		GuardianType:     "F",
		FatherName:       request.Data.GuardianName,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
}

func isAdminSource(source string) bool {
	return slices.Contains(config.Application().Validations.AllowedAdminSources, source)
}
