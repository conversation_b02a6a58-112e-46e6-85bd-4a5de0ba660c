package clientdetails

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func (c *ClientDetailsBusinessLogic) ClientDetails(ctx context.Context, request *modelsAPIv1.ClientDetailsAPIRequest) error {
	incomeDTO := getIncomeDTOFromRequest(request)
	occupationDTO := getOccupationDTOFromRequest(request)
	personalDTO := getPersonalDTOFromRequest(request)
	preferencesDTO := getPreferencesDTOFromRequest(request)
	depositoryDTO := getDepositoryDTOFromRequest(request)
	panDTO := getPanDTOFromRequest(request)
	poaDTO, err := c.poaRepository.FetchPOAForAppNumberWithContext(ctx, request.AppNumber)
	if err != nil && !isAdminSource(request.Source) {
		log.Error(ctx).Err(err).Msg("ClientDetails: Error getting poa data")
		return err
	}
	if poaDTO.Type == constants.Digilocker && poaDTO.Gender != constants.Empty {
		// Using gender from digio/digilocker.
		personalDTO.Gender = poaDTO.Gender
	}
	err = c.clientDetailsRepository.UpsertClientDetailsEntities(ctx, incomeDTO, occupationDTO, personalDTO, preferencesDTO, depositoryDTO, panDTO)
	if err != nil {
		return err
	}

	return nil
}

func (c *ClientDetailsNriBusinessLogic) ClientDetailsNri(ctx context.Context, request *modelsAPIv1.ClientDetailsNriRequest) error {
	incomeDTO := &business.Income{
		AppNumber:        request.AppNumber,
		Income:           request.Data.Income,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	occupationDTO := &business.Occupation{
		AppNumber:          request.AppNumber,
		Occupation:         request.Data.Occupation,
		OtherSpecification: request.Data.OthersSpecification,
		CreateSource:       request.Source,
		LastUpdateSource:   request.Source,
	}
	personalDTO := &business.Personal{
		AppNumber:        request.AppNumber,
		Gender:           request.Data.Gender,
		MaritalStatus:    request.Data.MaritalStatus,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		GuardianName:     request.Data.GuardianName,
		GuardianType:     request.Data.GuardianType,
	}
	nriPersonalDTO := &business.NriPersonalDetails{
		AppNumber:          request.AppNumber,
		TradingExperience:  request.Data.TradingExperience,
		Nationality:        request.Data.Nationality,
		BirthCountry:       request.Data.BirthCountry,
		PassportNumber:     request.Data.PassportNumber,
		PassportExpiry:     request.Data.PassportExpiry,
		PoliticallyExposed: request.Data.PoliticallyExposed,
		CreateSource:       request.Source,
		LastUpdateSource:   request.Source,
	}
	err := c.nriClientDetailsRepository.UpsertNriClientDetailsEntities(ctx, incomeDTO, occupationDTO, personalDTO, nriPersonalDTO)
	if err != nil {
		return err
	}

	return nil
}
