package country

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type CountrySearchRepository interface {
	GetCountryData(ctx context.Context, country string, from, size int) ([]business.CountrySearchData, error)
}

type CountrySearchBusinessLogic struct {
	countrySearchRepository CountrySearchRepository
}

func NewCountrySerachBusinessLogic(countrySearchRepository CountrySearchRepository) *CountrySearchBusinessLogic {
	return &CountrySearchBusinessLogic{
		countrySearchRepository: countrySearchRepository,
	}
}
