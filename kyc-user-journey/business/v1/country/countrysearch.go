package country

import (
	"context"
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
)

func (c *CountrySearchBusinessLogic) CountrySearch(ctx context.Context, countrySearchRequest modelsAPIv1.CountrySearchAPIRequest) (*modelsAPIv1.CountryAPIResponse, error) {
	countryData, err := c.countrySearchRepository.GetCountryData(ctx, countrySearchRequest.Search, countrySearchRequest.From, countrySearchRequest.Size)
	if err != nil {
		log.Error(ctx).Err(err).Msg(fmt.Sprintf("CountrySearch: got error while fetching data form DB for %s", countrySearchRequest.Search))
		return nil, err
	}

	var countryResponse []modelsAPIv1.CountryAPIResponseData
	err = objectmapper.GetMapperInstance().MapperSlice(&countryData, &countryResponse)
	if err != nil {
		log.Error(ctx).Err(err).Msg(fmt.Sprintf("CountrySearch: got error mapping the data for %s", countrySearchRequest.Search))
		return nil, constants.ErrMappingFailed.WithDetails(err)
	}

	return &modelsAPIv1.CountryAPIResponse{Data: countryResponse}, nil
}
