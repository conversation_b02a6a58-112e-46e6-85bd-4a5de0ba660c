package daeassistance

import (
	"context"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func (d *DaeAssistanceBusinessLogic) RequestDaeAssistance(ctx context.Context, request *modelsV1.DaeAssistanceRequest) (*modelsV1.DaeAssistanceResponse, error) {
	appNumber := request.AppNumber
	page := request.Data.Page
	messageType := getAssistanceMessageType()

	err := d.daeAssistanceRepository.UpsertDaeAssistanceRequest(ctx, appNumber, page, messageType)
	if err != nil {
		log.Error(ctx).Err(err).Msg("RequestDaeAssistance: Error getting status data")
		return nil, err
	}

	lsqMessage := &business.LSQMessage{
		AppNumber: appNumber,
		Data: map[string]any{
			"page":   page,
			"status": constants.HelpRequestedLSQEvent,
		},
	}

	err = d.lsqBusinessLogic.TriggerLSQ(ctx, lsqMessage)
	if err != nil {
		log.Error(ctx).Err(err).Msg("RequestDaeAssistance: Error triggering LSQ")
		return nil, err
	}

	message := getDaeMessageForType(messageType)
	response := &modelsV1.DaeAssistanceResponse{
		ShowMsg: true,
		Message: message,
	}
	log.Info(ctx).Msg("RequestDaeAssistance: Successfully completed")
	return response, nil
}

func getAssistanceMessageType() string {
	daeConfig := config.Dae()

	currentTime := time.Now().UTC()
	shiftStartTimeUTCMinutes := daeConfig.ShiftStartTime - constants.ISTOffsetMinutes
	shiftEndTimeUTCMinutes := daeConfig.ShiftEndTime - constants.ISTOffsetMinutes

	shiftStartHour := shiftStartTimeUTCMinutes / 60
	shiftStartMinute := shiftStartTimeUTCMinutes % 60

	shiftEndHour := shiftEndTimeUTCMinutes / 60
	shiftEndMinute := shiftEndTimeUTCMinutes % 60

	shiftStart := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), shiftStartHour, shiftStartMinute, 0, 0, time.UTC)
	shiftEnd := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), shiftEndHour, shiftEndMinute, 0, 0, time.UTC)

	currentWeekday := strings.ToLower(currentTime.Weekday().String())
	for _, day := range daeConfig.NonWorkingDays {
		if day == currentWeekday {
			return constants.DaeNonWorkingDaysMsg
		}
	}
	if currentTime.Before(shiftStart) || currentTime.After(shiftEnd) {
		return constants.DaeNonWorkingHrsMsg
	}
	return constants.DaeWorkingHrsMsg
}

func getDaeMessageForType(messageType string) string {
	daeConfig := config.Dae()
	switch messageType {
	case constants.DaeNonWorkingDaysMsg:
		return daeConfig.NonWorkingDaysMsg
	case constants.DaeNonWorkingHrsMsg:
		return daeConfig.NonWorkingHrsMsg
	case constants.DaeWorkingHrsMsg:
		return daeConfig.WorkingHrsMsg
	default:
		return daeConfig.WorkingHrsMsg
	}
}
