package daeassistance

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type DaeAssistanceRepository interface {
	UpsertDaeAssistanceRequest(ctx context.Context, appNumber string, page string, messageType string) error
}

type DaeAssistanceBusinessLogic struct {
	daeAssistanceRepository DaeAssistanceRepository
	lsqRepository           LsqRepository
	lsqBusinessLogic        LsqBusinessLogic
}

type LsqRepository interface {
	GetLSQDataByAppNumber(ctx context.Context, appNumber string) (business.LSQData, error)
}

type LsqBusinessLogic interface {
	TriggerLSQ(ctx context.Context, lsqMessage *business.LSQMessage) error
}

func NewDaeAssistanceBusinessLogic(daeAssistanceRepository DaeAssistanceRepository, lsqRepository LsqRepository, lsqBusinessLogic LsqBusinessLogic) *DaeAssistanceBusinessLogic {
	return &DaeAssistanceBusinessLogic{
		daeAssistanceRepository: daeAssistanceRepository,
		lsqBusinessLogic:        lsqBusinessLogic,
		lsqRepository:           lsqRepository,
	}
}
