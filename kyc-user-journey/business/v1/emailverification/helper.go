package emailverification

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"time"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExt "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func isOTPValid(otp *business.EmailVerificationDTO, validityInMins, maxAttempts int) bool {
	expiryTS := otp.OTPGeneratedTS.Add(time.Minute * time.Duration(validityInMins))
	return otp.OTP != constants.Empty &&
		expiryTS.After(utils.GetCurrentTime()) && otp.AttemptCount <= maxAttempts && !otp.OTPVerified
}

func generateOTP(e *business.EmailVerificationDTO, size int) error {
	otp, err := utils.GetRandomNumber(size)
	if err != nil {
		return err
	}
	e.OTP = otp
	e.OTPGeneratedTS = utils.GetCurrentTime()
	e.AttemptCount = 1
	return nil
}

func generateEmailOtpRequest(ctx context.Context, email *business.EmailVerificationDTO, validityInMins int) (*modelsExt.SendEmailRequest, error) {
	emailContent := business.EmailOTPMailContent{
		FullName:       constants.Angelite,
		OTP:            email.OTP,
		ValidityInMins: validityInMins,
	}

	// Create Email Body.
	body, err := generateEmailOTPContent(ctx, emailContent)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).
			Msg("GenerateEmailOtpRequest: Error while creating the email body")
		return nil, err
	}

	emailData := modelsExt.EmailData{
		FromEmail:        config.Email().Otp.Sender,
		DestinationEmail: email.Email,
		Body:             body,
		Title:            email.OTP + config.Email().Otp.Title,
	}

	req := modelsExt.SendEmailRequest{
		RequestID:        utils.GenerateUUID(),
		Priority:         config.Email().Otp.Priority,
		NotificationType: config.Email().Otp.NotificationType,
		Email:            emailData,
	}

	return &req, nil
}

func generateEmailOTPContent(ctx context.Context, emailContent business.EmailOTPMailContent) (string, error) {
	content := config.Email().Otp.Template

	buf := new(bytes.Buffer)
	t, err := template.New(emailContent.OTP).Parse(content)
	if err != nil {
		log.Error(ctx).Interface(constants.LogRequestKey, emailContent).Err(err).Msg("GenerateEmailOTPContent: error parsing email content")
		return constants.Empty, err
	}

	err = t.Execute(buf, emailContent)
	if err != nil {
		log.Error(ctx).Interface(constants.LogRequestKey, emailContent).Err(err).Msg("GenerateEmailOTPContent: error processing email content")
		return constants.Empty, err
	}

	return buf.String(), nil
}

func verifyOTP(email *business.EmailVerificationDTO, otp string) error {
	validityInMins := config.Email().Otp.ValidityInMinutes
	expiryTS := email.OTPGeneratedTS.Add(time.Minute * time.Duration(validityInMins))

	if otp != email.OTP {
		return constants.ErrInvalidOTPEmail.Value()
	}

	currentTime := utils.GetCurrentTime()
	if expiryTS.Before(currentTime) {
		return constants.ErrExpiredOTPEmail.Value()
	}

	if email.OTPVerified {
		return constants.ErrAlreadyUsedOTPEmail.Value()
	}

	// OTP verified.
	return nil
}

func (e *EmailOTPBusinessLogic) startIncomeFulfilmentWorkflow(ctx context.Context, request *modelsV1.ValidateEmailOTPPageRequest) error {
	req := modelsExt.ProcessStartRequest{
		BusinessKey: request.ClientCode,
		Variables:   getProcessVariables(request),
	}
	log.Info(ctx).Str(constants.LogClientCodeKey, request.ClientCode).Interface(constants.LogWorkflowProcessStartRequest, req).Msg("WelcomeEmailOTPValidationPage: starting fulfilment workflow ")
	res, err := e.workflowExternalProvider.StartProcessInstance(ctx, constants.WelcomeEmailResendWOrkflow, req)
	if err != nil {
		return err
	}
	log.Info(ctx).Str(constants.LogClientCodeKey, request.ClientCode).Msg(fmt.Sprintf("WelcomeEmailOTPValidationPage: workflow instance created for workflowId: %s with busniessKey: %s and processId: %s",
		res.DefinitionID, res.BusinessKey, res.ID))
	return nil
}

func getProcessVariables(request *modelsV1.ValidateEmailOTPPageRequest) map[string]modelsExt.VariableObject {
	variables := map[string]modelsExt.VariableObject{}
	variables[constants.MobileWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Mobile,
	}
	variables[constants.AppNumberWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.AppNumber,
	}
	variables[constants.ClientCodeWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.ClientCode,
	}
	variables[constants.CorrelationIDWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.ClientCode,
	}
	variables[constants.SourceWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Source,
	}
	variables[constants.IPAddressWorkflowKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.IPAddress,
	}
	variables[constants.IsFnoProcessVariableKey] = modelsExt.VariableObject{
		Type:  constants.WorkflowVariableBooleanType,
		Value: false,
	}
	return variables
}
