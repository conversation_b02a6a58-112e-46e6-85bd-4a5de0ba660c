package emailverification

import (
	"context"
	"fmt"
	"strings"

	"github.com/angel-one/kyc-user-journey/config/models"

	businessCommonEmod "github.com/angel-one/kyc-user-journey/business/common/emod"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	apiV1 "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
)

func (e *EmailOTPBusinessLogic) SendEmailOTP(ctx context.Context, email string) error {
	emailVerificationDto, err := e.emailVerRepository.GetEntity(ctx, email)

	if emailVerificationDto.Email == constants.Empty { // Row not found in the table
		log.Debug(ctx).Interface("Email", email).Msg("Provided Email not found in OTP Table")
		emailVerificationDto.Email = email
	} else if err != nil {
		log.Error(ctx).Err(constants.ErrDatabase.WithDetails(err)).Interface("Email", email).
			Msg("emailOTP: Error fetching emailOTP details from DB")
		return constants.ErrDatabase.WithDetails(err)
	}

	validityInMins := config.Email().Otp.ValidityInMinutes
	maxAttempts := config.Email().Otp.MaxAttempts
	length := config.Email().Otp.Length

	emailVerificationDto.AttemptCount += 1

	// if otp has expired/not generated/attempts>maxAttempts, then generate otp.
	if !isOTPValid(emailVerificationDto, validityInMins, maxAttempts) {
		err = generateOTP(emailVerificationDto, length)
		if err != nil {
			log.Error(ctx).Err(constants.ErrGeneratingRandomNumber.WithDetails(err)).Interface("Email", email).
				Msg("emailOTP: Error in generating the otp")
			return constants.ErrGeneratingRandomNumber.WithDetails(err.Error())
		}
	}

	// Send OTP mail.
	request, err := generateEmailOtpRequest(ctx, emailVerificationDto, validityInMins)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).
			Msg("emailOTP: Error in generating the mail request")
		return err
	}

	err = e.notificationExternalProvider.SendEmailSync(ctx, request)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).Msg("emailOTP: Error while sending Email to user")
		return constants.ErrSendingEMail.WithDetails(err.Error())
	}

	// update db.
	err = e.emailVerRepository.UpsertEntity(ctx, emailVerificationDto)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).Msg("emailOTP: Error while updating the DB")
		return constants.ErrDatabase.WithDetails(err.Error())
	}

	// success.
	log.Info(ctx).Msg("emailOTP: Email sent successfully to user")
	return nil
}

func (e *EmailOTPBusinessLogic) ValidateOTP(ctx context.Context, email, userOtp string) (*modelsV1.ValidateEmailOTPResponse, error) {
	// fetch data from db.
	emailVerificationDto, err := e.emailVerRepository.GetEntity(ctx, email)
	if err != nil {
		log.Error(ctx).Err(constants.ErrDatabase.WithDetails(err)).Interface("Email", email).
			Msg("emailOTP: Error fetching emailOTP details from DB")
		return nil, constants.ErrDatabase.WithDetails(err.Error())
	}

	// verify otp.
	err = verifyOTP(emailVerificationDto, userOtp)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).
			Msg("emailOTP: OTP verification failed")
		return nil, err
	}

	// otp verified, mark verified in db.
	err = e.emailVerRepository.MarkVerified(emailVerificationDto)
	if err != nil {
		log.Error(ctx).Err(err).Interface("Email", email).Msg("emailOTP: Error while updating the DB after verification")
		return nil, constants.ErrDatabase.WithDetails(err.Error())
	}

	authToken, err := businessCommonEmod.GetEmailMobileAuthToken(
		map[string]string{
			constants.EmodEmailAuthenticationClaimKey: email,
		})
	if err != nil {
		return nil, err
	}
	return &modelsV1.ValidateEmailOTPResponse{AuthToken: authToken}, nil
}

func (e *EmailOTPBusinessLogic) ValidateOTPAndMarkEmailAsVerified(ctx context.Context, email, userOtp, appNumber, source string) error {
	_, err := e.ValidateOTP(ctx, email, userOtp)
	if err != nil {
		return err
	}
	err = e.emailRepository.MarkEmailAsVerified(ctx, appNumber, constants.User)
	if err != nil {
		return err
	}

	err = e.kraBusinessLogic.SaveAddressIfKRACompliant(ctx, appNumber, source)
	if err != nil {
		return err
	}

	if strings.HasPrefix(appNumber, constants.NR) {
		// send email to sb and customer
		emailOtherDetails := &models.Otp{
			Sender:           config.Email().EmailVerificationSuccessForNri.Sender,
			Title:            config.Email().EmailVerificationSuccessForNri.Title,
			Priority:         config.Email().EmailVerificationSuccessForNri.Priority,
			NotificationType: config.Email().EmailVerificationSuccessForNri.NotificationType,
		}
		err = e.commonEmailBusinessLogic.SendEmailForNri(ctx, appNumber, emailOtherDetails)
		if err != nil {
			log.Error(ctx).Err(err).Msg("ValidateOTPAndMarkEmailAsVerified: Error while sending the email")
		}
	}

	return nil
}

func (e *EmailOTPBusinessLogic) GetEmailForUser(ctx context.Context, clientCode string) (*modelsV1.ValidateEmailOTPPageResponse, error) {
	var response modelsV1.ValidateEmailOTPPageResponse
	profileResponse, _, err := e.GetExternalDetails(ctx, clientCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg(fmt.Sprintf("Error getting KYC status from profile API for client code %s", clientCode))
		return nil, err
	}
	response.Data.Email = profileResponse.Profiles[0].ClientDetails.Email
	return &response, nil
}

func (e *EmailOTPBusinessLogic) GetExternalDetails(ctx context.Context, clientCode string) (
	*apiV1.GetProfileResponse, *apiV1.GetClientBasicDetailsReponse, error) {
	chProfile := make(chan apiV1.ChannelGetProfileResponse)

	go e.clcmExternalProvider.GetCLCMProfilesWithChannel(ctx, &apiV1.ProfileDetailsFromCLCMRequest{
		ClientID: clientCode,
		Fields:   constants.CLCMProfileAttributesForWelcomeEmail,
	}, chProfile)

	chProfileRes := <-chProfile
	if chProfileRes.Error != nil {
		return nil, nil, chProfileRes.Error
	}
	return chProfileRes.ProfileResponse, nil, nil
}

func (e *EmailOTPBusinessLogic) StartWorkflow(ctx context.Context, request *modelsV1.ValidateEmailOTPPageRequest) error {
	appNumber, err := e.emailRepository.GetAppNumberFromEmail(ctx, request.ClientCode)
	if err != nil {
		log.Error(ctx).Str(constants.LogClientCodeKey, request.ClientCode).Err(err).Msg("WelcomeEmailOTPValidationPage: error starting workflow")
		return err
	}
	request.AppNumber = appNumber
	err = e.startIncomeFulfilmentWorkflow(ctx, request)
	if err != nil {
		log.Error(ctx).Str(constants.LogClientCodeKey, request.ClientCode).Err(err).Msg("WelcomeEmailOTPValidationPage: error starting workflow")
		return err
	}
	return err
}
