package emailverification

import (
	"context"

	"github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type EmailVerificationRepository interface {
	GetEntity(ctx context.Context, email string) (*business.EmailVerificationDTO, error)
	UpsertEntity(ctx context.Context, dto *business.EmailVerificationDTO) error
	MarkVerified(dto *business.EmailVerificationDTO) error
}

type EmailRepository interface {
	MarkEmailAsVerified(ctx context.Context, appNumber, verifiedBy string) error
	GetAppNumberFromEmail(ctx context.Context, userid string) (string, error)
}

type KRABusinessLogic interface {
	SaveAddressIfKRACompliant(ctx context.Context, appNumber, source string) error
}

type NotificationExternalProvider interface {
	SendEmailSync(ctx context.Context, rq *modelsV1.SendEmailRequest) error
}

type ClcmExternalProvider interface {
	GetClientBasicDetails(ctx context.Context, clientCode string) (*modelsV1.GetClientBasicDetailsReponse, error)
	FetchClientDetailsWithMobileList(ctx context.Context, mobileList, attributes []string) (*modelsV1.GetClientDetailsByAttributesResponse, error)
	FetchClientDetailsWithEmailList(ctx context.Context, emailList, attributes []string) (*modelsV1.GetClientDetailsByAttributesResponse, error)
	FetchPanDetails(ctx context.Context, clientCode string) (*modelsV1.FetchPanFromCLCMResponse, error)
	GetClientBasicDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsV1.ChannelGetClientBasicDetailsResponse)
	GetCLCMProfilesWithChannel(ctx context.Context, request *modelsV1.ProfileDetailsFromCLCMRequest, ch chan modelsV1.ChannelGetProfileResponse)
}

type ProfileExternalProvider interface {
	GetProfilesWithChannel(ctx context.Context, request *modelsV1.GetProfileRequest, ch chan modelsV1.ChannelGetProfileResponse)
	// GetPartnerProfile(ctx context.Context, request modelsV1.GetPartnerProfileRequest) (*modelsV1.GetPartnerProfileResponse, error)
}

type WorkflowExternalProvider interface {
	StartProcessInstance(ctx context.Context, workflowName string, request modelsV1.ProcessStartRequest) (*modelsV1.ProcessStartResponse, error)
}

type CommonEmailBusinessLogic interface {
	SendEmailForNri(ctx context.Context, appNumber string, emailOtherDetails *models.Otp) error
}

type EmailOTPBusinessLogic struct {
	emailVerRepository           EmailVerificationRepository
	notificationExternalProvider NotificationExternalProvider
	emailRepository              EmailRepository
	kraBusinessLogic             KRABusinessLogic
	clcmExternalProvider         ClcmExternalProvider
	profileExternalProvider      ProfileExternalProvider
	workflowExternalProvider     WorkflowExternalProvider
	commonEmailBusinessLogic     CommonEmailBusinessLogic
}

func NewEmailOTPBusinessLogic(emailVerRepository EmailVerificationRepository, notificationExternalProvider NotificationExternalProvider, emailRepository EmailRepository, kraBusinessLogic KRABusinessLogic, clcmExternalProvider ClcmExternalProvider,
	profileExternalProvider ProfileExternalProvider, workflowExternalProvider WorkflowExternalProvider, commonEmailBusinessLogic CommonEmailBusinessLogic) *EmailOTPBusinessLogic {
	return &EmailOTPBusinessLogic{
		emailVerRepository:           emailVerRepository,
		notificationExternalProvider: notificationExternalProvider,
		emailRepository:              emailRepository,
		kraBusinessLogic:             kraBusinessLogic,
		clcmExternalProvider:         clcmExternalProvider,
		profileExternalProvider:      profileExternalProvider,
		workflowExternalProvider:     workflowExternalProvider,
		commonEmailBusinessLogic:     commonEmailBusinessLogic,
	}
}
