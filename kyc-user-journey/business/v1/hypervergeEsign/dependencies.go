package hypervergeEsign

import (
	"context"

	models2 "github.com/angel-one/kyc-user-journey/config/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/models/task"
)

type ESignBusinessLogic interface {
	CheckEsignEligibility(ctx context.Context, appNumber, mobile, source string) error
	GetRedirectRequestData(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, esignFormat string) (*business.HypervergeESignRedirectURLData, error)
	GetBasicHypervergeEsignDetails(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest) (*business.BasicHypervergeEsignDetails, error)
	GetSigningConfigID(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, basicHypervergeDetails *business.BasicHypervergeEsignDetails) (string, error)
	SaveSignedDocumentDetails(ctx context.Context, appNumber, esignType, signerAadhaarName, filePath, submitterCode string) error
}
type GeolocationRepository interface {
	FreshUpsert(ctx context.Context, geolocationDTO *business.GeoLocation) error
}

type ReferralRepository interface {
	GetReferralWithAppNumber(ctx context.Context, appNumber string) (*business.Referral, error)
}

type DeliveryBrokerageConsentRepository interface {
	GetByID(ctx context.Context, appNumber string) (*business.DeliveryBrokConsent, error)
	Update(ctx context.Context, deliveryBrokConsentDTO *business.DeliveryBrokConsent) error
}

type POARepository interface {
	FetchPOAForAppNumberWithContext(ctx context.Context, appNumber string) (business.POA, error)
}

type DIYJourneyRepository interface {
	FetchActiveDIYJourneyTypeForAppNumber(appNumber string) (string, error)
}

type HypervergeESignExternalProvider interface {
	CallHypervergeInputAPI(ctx context.Context, req *modelsExternal.HypervergeInputRequest) (*modelsExternal.HypervergeInputResponse, error)
	CallHypervergeOutputAPI(ctx context.Context, req *modelsExternal.HypervergeOutputRequest) (*modelsExternal.HypervergeOutputResponse, error)
	GetSignatureStatus(ctx context.Context, documentID string) (*modelsExternal.HypervergeSignatureStatusResponse, error)
	DownloadSignedDocument(ctx context.Context, documentID string) (*modelsExternal.HypervergeDocumentDownloadResponse, error)
}

type ESignHyperVergeBusinessLogic interface {
	InitiateESign(ctx context.Context, request *modelsAPIV1.ESignHypervergeAPIRequest, eSignTask task.HypervergeEsignTask, flowConfig *models2.HypervergeESignFlowConfig) (*modelsAPIV1.ESignHypervergeAPIResponse, error)
	FetchSignedDocument(ctx context.Context, transactionId string) (*modelsExternal.HypervergeOutputResponse, error)
}

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type PrismSQSBusinessLogic interface {
	SendMessageToQueue(ctx context.Context, prismEventMessage *business.PrismEventMessage)
}

type ESignHyperVergeBusinessLogicImpl struct {
	geolocationRepository              GeolocationRepository
	referralRepository                 ReferralRepository
	deliveryBrokerageConsentRepository DeliveryBrokerageConsentRepository
	hypervergeExternalProvider         HypervergeESignExternalProvider
	poaRepository                      POARepository
	diyJourneyRepository               DIYJourneyRepository
	lsqBusinessLogic                   LSQBusinessLogic
	prismSQSBusinessLogic              PrismSQSBusinessLogic
	esignBusinessLogic                 ESignBusinessLogic
}

func NewHypervergeESignBusinessLogic(geolocationRepository GeolocationRepository,
	referralRepository ReferralRepository,
	deliveryBrokerageConsentRepository DeliveryBrokerageConsentRepository,
	poaRepository POARepository,
	hypervergeExternalProvider HypervergeESignExternalProvider,
	diyJourneyRepository DIYJourneyRepository,
	lsqBusinessLogic LSQBusinessLogic,
	prismSQSBusinessLogic PrismSQSBusinessLogic,
	esignBusinessLogic ESignBusinessLogic) *ESignHyperVergeBusinessLogicImpl {
	return &ESignHyperVergeBusinessLogicImpl{
		geolocationRepository:              geolocationRepository,
		referralRepository:                 referralRepository,
		deliveryBrokerageConsentRepository: deliveryBrokerageConsentRepository,
		hypervergeExternalProvider:         hypervergeExternalProvider,
		poaRepository:                      poaRepository,
		diyJourneyRepository:               diyJourneyRepository,
		lsqBusinessLogic:                   lsqBusinessLogic,
		prismSQSBusinessLogic:              prismSQSBusinessLogic,
		esignBusinessLogic:                 esignBusinessLogic,
	}
}
