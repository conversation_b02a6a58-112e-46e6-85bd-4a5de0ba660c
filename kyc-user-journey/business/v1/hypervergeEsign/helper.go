package hypervergeEsign

import (
	"context"
	"fmt"
	models2 "github.com/angel-one/kyc-user-journey/config/models"
	"net/url"
	"slices"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	goUtils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils/security"
)

func encryptHypervergeRequestData(ctx context.Context, data *business.HypervergeESignRedirectURLData) (string, error) {
	key := config.Security().AESEncryptionKey
	dataJSON, err := goUtils.MarshalJSON(data)
	if err != nil {
		log.Error(ctx).Err(err).Msg("encryptHypervergeRequestData: error marshaling the request data")
		return constants.Empty, err
	}

	encryptedData, err := security.EncryptAES([]byte(key), string(dataJSON))
	if err != nil {
		log.Error(ctx).Err(err).Msg("encryptHypervergeRequestData: error encrypting the request data")
		return constants.Empty, err
	}
	return encryptedData, nil
}

func decryptHypervergeRequestData(ctx context.Context, request *modelsAPIV1.ESignHypervergeRedirectAPIRequestData) error {
	key := config.Security().AESEncryptionKey
	hypervergeRedirectURLDataString, err := security.DecryptAES([]byte(key), request.RequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptHypervergeRequestData: error decrypting request data string")
		return constants.ErrAESDecryption.WithDetails(err.Error())
	}
	err = goUtils.UnmarshalJSON([]byte(hypervergeRedirectURLDataString), &request.DecodedRequestData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("decryptHypervergeRequestData: error unmarshalling setu redirect URL data string")
		return err
	}
	return nil
}

func BuildHypervergeInputs(basicHypervergeDetails *business.BasicHypervergeEsignDetails, flowConfig *models2.HypervergeESignFlowConfig) *modelsExternal.HypervergeInputRequest {
	coordinates := []modelsExternal.Coordinates{
		{
			X1:   constants.Empty,
			Y1:   constants.Empty,
			X2:   constants.Empty,
			Y2:   constants.Empty,
			Page: constants.Empty,
		},
	}
	base64PDF := "" // TODO: Fill with actual base64 PDF logic
	userName := basicHypervergeDetails.FullName
	userEmail := "" // TODO: Fill with actual user email
	userPhone := basicHypervergeDetails.Mobile
	transactionId := basicHypervergeDetails.AppNumber
	callbackUrl := flowConfig.CallBackURL

	inputReq := &modelsExternal.HypervergeInputRequest{
		WorkflowId:      "esign",
		TransactionId:   transactionId,
		RedirectUrl:     callbackUrl,
		ForceCreateLink: "yes",
		Inputs: modelsExternal.Inputs{
			InviteeName:  userName,
			InviteeEmail: userEmail,
			InviteePhone: userPhone,
			Coordinates:  coordinates,
			File:         base64PDF,
		},
	}

	return inputReq
}

func getBaseRedirectURL(source string) string {
	if slices.Contains(config.Application().Validations.AllowedAdminSources, source) {
		return config.HyperVergeESign().Flow.Onboarding.RedirectAdmin.URL
	}

	return config.HyperVergeESign().Flow.Onboarding.Redirect.URL
}

func getHypervergeFailureRedirectResponse(baseRedirectURL string, queryParams url.Values, message string) *modelsAPIV1.HypervergeRedirectAPIResponse {
	queryParams.Add(constants.FailureCallBackParam, message)

	redirectURL := fmt.Sprintf("%s?%s", baseRedirectURL, queryParams.Encode())

	return &modelsAPIV1.HypervergeRedirectAPIResponse{
		RedirectURL: redirectURL,
	}
}

func getPrismEventData(appNumber string) *business.PrismEventMessage {
	eventData := business.PrismEventMessage{
		Source:         constants.ApplicationName,
		EventName:      constants.PrismEsignCompletedEvent,
		EventSubtype:   constants.PrismEsignCompletedEvent,
		ActivityID:     constants.ESignFlowName,
		EventStartTime: time.Now().UTC().UnixMilli(),
		EventEmitTime:  time.Now().UTC().UnixMilli(),
		Workflow:       constants.ApplicationName,
		AppNumber:      appNumber,
		RetryCount:     1,
		EventEndTime:   time.Now().UTC().UnixMilli(),
	}

	return &eventData
}
