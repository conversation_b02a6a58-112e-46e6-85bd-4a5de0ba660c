package hypervergeEsign

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"net/url"
	"slices"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	models2 "github.com/angel-one/kyc-user-journey/config/models"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/models/task"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/fetch"

	"time"
)

func (h *ESignHyperVergeBusinessLogicImpl) InitiateESign(ctx context.Context, request *modelsAPIV1.ESignHypervergeAPIRequest, eSignTask task.HypervergeEsignTask, flowConfig *models2.HypervergeESignFlowConfig) (*modelsAPIV1.ESignHypervergeAPIResponse, error) {
	// Check eligibility
	err := h.esignBusinessLogic.CheckEsignEligibility(ctx, request.AppNumber, request.Mobile, request.Source)
	if err != nil {
		log.Error(ctx).Err(err).Str("appNumber", request.AppNumber).Msg("InitiateESign: is not eligible for e-sign")
		return nil, err
	}

	// Gather KYC/contextual data, coordinates...
	basicDetails, err := h.esignBusinessLogic.GetBasicHypervergeEsignDetails(ctx, request)
	if err != nil {
		log.Error(ctx).Err(err).Str("appNumber", request.AppNumber).Msg("InitiateESign: error getting basic hyperverge esign details")
		return nil, err
	}

	// Get redirect request data (contextual data needed for esign)
	redirectData, err := h.esignBusinessLogic.GetRedirectRequestData(ctx, request, basicDetails.ESignFormat)
	if err != nil {
		log.Error(ctx).Err(err).Str("appNumber", request.AppNumber).Msg("InitiateESign: error getting redirect request data")
		return nil, err
	}

	// Encrypt the contextual data using the helper function
	encryptedData, err := encryptHypervergeRequestData(ctx, redirectData)
	if err != nil {
		log.Error(ctx).Err(err).Str("appNumber", request.AppNumber).Msg("InitiateESign: error encrypting request data")
		return nil, err
	}

	// 3. Encode PDF as base64
	// Get Signed URL for the PDF
	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	signedURL, err := s3.GetS3Client().GetSignedURL(ctx, basicDetails.PDFPath, time.Duration(signedURLExpiry)*time.Second)
	if err != nil {
		log.Error(ctx).
			Err(err).
			Str("appNumber", request.AppNumber).
			Msg("InitiateESign: Error getting signed URL")
		return nil, constants.ErrGettingSignedURL.WithDetails(err.Error())
	}

	// Get PDF file content using the signed URL
	pdfBytes, err := fetch.GetFileBytes(ctx, signedURL)
	if err != nil {
		log.Error(ctx).
			Err(err).
			Str("appNumber", request.AppNumber).
			Msg("InitiateESign: Error fetching PDF bytes")
		return nil, constants.ErrGettingFileBytes.WithDetails(err.Error())
	}

	// 4. Input API request model - use the BuildHypervergeInputs helper function
	inputReq := BuildHypervergeInputs(basicDetails, flowConfig)

	// Set the transaction ID, redirect URL, and file content
	inputReq.TransactionId = request.AppNumber
	inputReq.RedirectUrl = flowConfig.CallBackURL + "?requestData=" + url.QueryEscape(encryptedData)
	inputReq.Inputs.File = base64.StdEncoding.EncodeToString(pdfBytes)

	// 5. Externals : Call Input API
	inputResponse, err := h.hypervergeExternalProvider.CallHypervergeInputAPI(ctx, inputReq)
	if err != nil {
		log.Error(ctx).
			Err(err).
			Str("appNumber", request.AppNumber).
			Msg("InitiateESign: Error calling Hyperverge Input API")
		return nil, err
	}

	// 6. Save details in DB
	err = eSignTask.SaveHypervergeInitiateDetails(ctx, request, basicDetails, inputResponse.MetaData.RequestId)
	if err != nil {
		log.Error(ctx).
			Err(err).
			Str("appNumber", request.AppNumber).
			Msg("InitiateESign: Error saving Hyperverge initiate details")
		return nil, err
	}

	// 7. Return the redirect URL
	return &modelsAPIV1.ESignHypervergeAPIResponse{
		RedirectURL: inputResponse.MetaData.RequestId,
	}, nil
}

// HandleRedirect : Handles the redirect after the hyperverge
func (h *ESignHyperVergeBusinessLogicImpl) HandleRedirect(ctx context.Context, req *modelsAPIV1.ESignHypervergeRedirectAPIRequest) (*modelsAPIV1.HypervergeRedirectAPIResponse, error) {
	// 1. Decrypt & Validate the request data
	err := decryptHypervergeRequestData(ctx, &req.Data)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleRedirect: error decrypting request data")
		return nil, err
	}

	appNumber := req.Data.DecodedRequestData.AppNumber
	source := req.Data.DecodedRequestData.Source
	mobile := req.Data.DecodedRequestData.Mobile
	ctx = context.WithValue(ctx, constants.CorrelationIDContextKey, appNumber)

	// Get base redirect URL for success/failure responses
	baseRedirectURL := getBaseRedirectURL(source)
	queryParams := url.Values{}
	queryParams.Add(constants.TypeKey, constants.HypervergeESignType)
	queryParams.Add(constants.ESignProvider, req.Data.ESignProvider)

	// Add necessary params if call was made from admin
	if slices.Contains(config.Application().Validations.AllowedAdminSources, source) {
		queryParams.Add(constants.Mobile, utils.HashUpperCaseWithSHA256(mobile))
		queryParams.Add(constants.AgentID, req.Data.DecodedRequestData.AgentID)
	}

	// Get default failure message from config
	flowConfig := config.HyperVergeESign().Flow.Onboarding
	defaultFailureMessage := flowConfig.Redirect.Message.Failure

	// Check Eligibility
	err = h.esignBusinessLogic.CheckEsignEligibility(ctx, appNumber, mobile, source)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleRedirect: ineligible for esign")
		return getHypervergeFailureRedirectResponse(baseRedirectURL, queryParams, defaultFailureMessage), nil
	}

	// Check if signature was successful
	if !strings.EqualFold("true", req.Data.Success) {
		log.Error(ctx).Msg("HandleRedirect: signature was not successful")
		message := config.HyperVergeESign().ErrorCodeToFailureMessageMap[req.Data.ErrorCode]
		if message == constants.Empty {
			message = defaultFailureMessage
		}
		return getHypervergeFailureRedirectResponse(baseRedirectURL, queryParams, message), nil
	}

	// Get signed document and upload to S3
	s3Path := fmt.Sprintf("%s/eSign/esign%s%d.pdf", appNumber, "-", time.Now().Unix())
	signerAadhaarName, err := h.getEsignDetailsFromHypervergeAndUploadToS3(ctx, req, s3Path)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleRedirect: error getting esign details and uploading to S3")
		return getHypervergeFailureRedirectResponse(baseRedirectURL, queryParams, defaultFailureMessage), nil
	}

	// Save Path in DB
	esignType := fmt.Sprintf("%s-%s", constants.HypervergeESignType, req.Data.ESignProvider)
	submitterCode := constants.Empty

	// If admin source, use agent ID as submitter code
	if slices.Contains(config.Application().Validations.AllowedAdminSources, source) {
		submitterCode = req.Data.DecodedRequestData.AgentID
	}

	err = h.esignBusinessLogic.SaveSignedDocumentDetails(ctx, appNumber, esignType, signerAadhaarName, s3Path, submitterCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleRedirect: error saving signed document details")
		return getHypervergeFailureRedirectResponse(baseRedirectURL, queryParams, defaultFailureMessage), nil
	}

	// Trigger SQS for fulfillment
	if strings.HasPrefix(appNumber, "EQ") {
		h.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, appNumber, constants.EsignSuccessLSQEvent)
		// Send end event
		go h.prismSQSBusinessLogic.SendMessageToQueue(context.WithoutCancel(ctx), getPrismEventData(appNumber))
	}

	// Return success response
	queryParams.Add(constants.SuccessCallBackParam, flowConfig.Redirect.Message.Success)
	successRedirectURL := fmt.Sprintf("%s?%s", baseRedirectURL, queryParams.Encode())

	return &modelsAPIV1.HypervergeRedirectAPIResponse{
		RedirectURL: successRedirectURL,
	}, nil
}

// Helper: getEsignDetailsFromHypervergeAndUploadToS3
func (h *ESignHyperVergeBusinessLogicImpl) getEsignDetailsFromHypervergeAndUploadToS3(ctx context.Context, request *modelsAPIV1.ESignHypervergeRedirectAPIRequest, filePath string) (string, error) {
	chDownloadDocument := make(chan modelsExternal.ChannelHypervergeDocumentDownload)
	chSignatureStatus := make(chan modelsExternal.ChannelHypervergeSignatureStatus)

	go h.downloadDocument(ctx, request.Data.ID, chDownloadDocument)
	go h.getSignatureStatus(ctx, request.Data.ID, chSignatureStatus)

	downloadDocumentResponse := <-chDownloadDocument
	signatureStatusResponse := <-chSignatureStatus

	if downloadDocumentResponse.Error != nil {
		log.Error(ctx).Err(downloadDocumentResponse.Error).Msg("getEsignDetailsFromHypervergeAndUploadToS3: error downloading signed document from Hyperverge")
		return constants.Empty, downloadDocumentResponse.Error
	}
	if signatureStatusResponse.Error != nil {
		log.Error(ctx).Err(signatureStatusResponse.Error).Msg("getEsignDetailsFromHypervergeAndUploadToS3: error getting signature status from Hyperverge")
		return constants.Empty, signatureStatusResponse.Error
	}

	if len(signatureStatusResponse.Response.Signers) == 0 || signatureStatusResponse.Response.Signers[0].SignatureDetails.AadhaarName == constants.Empty {
		return constants.Empty, constants.ErrSetuSignatureStatus.WithDetails("signer details not available")
	}

	aadhaarSuffix := signatureStatusResponse.Response.Signers[0].SignatureDetails.AadhaarSuffix

	poaDTO, err := h.poaRepository.FetchPOAForAppNumberWithContext(ctx, request.Data.DecodedRequestData.AppNumber)

	if err != nil {
		return constants.Empty, constants.ErrFetchingPOAData.WithDetails(err.Error())
	}

	if poaDTO.ID == constants.Empty || len(poaDTO.ID) < 4 {
		return constants.Empty, constants.ErrPOANotFound.WithDetails("Aadhaar number not found")
	}

	idSuffix := poaDTO.ID[len(poaDTO.ID)-4:]

	if idSuffix != aadhaarSuffix {
		return constants.Empty, constants.ErrAadhaarNotMatch.WithDetails("Aadhaar suffix not matching")
	}

	// check for url emptiness
	if downloadDocumentResponse.Response.DownloadURL == constants.Empty {
		return constants.Empty, constants.ErrDownloadingSignedDocumentHypervergeESign.WithDetails("Empty file URL")
	}

	// download file from hyperverge url
	fileBytes, err := fetch.GetFileBytes(ctx, downloadDocumentResponse.Response.DownloadURL)
	if err != nil {
		log.Error(ctx).Err(downloadDocumentResponse.Error).Msg("getESignDetailsFromHyperverge: error downloading file using setu URL")
		return constants.Empty, err
	}

	// upload to s3
	err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileBytes))
	if err != nil {
		log.Error(ctx).Err(downloadDocumentResponse.Error).Msg("getESignDetailsFromHyperverge: error uploading file to s3")
		return constants.Empty, err
	}

	return strings.TrimSpace(signatureStatusResponse.Response.Signers[0].SignatureDetails.AadhaarName), nil
}

func (h *ESignHyperVergeBusinessLogicImpl) downloadDocument(ctx context.Context, id string, ch chan<- modelsExternal.ChannelHypervergeDocumentDownload) {
	res, err := h.hypervergeExternalProvider.DownloadSignedDocument(ctx, id)
	ch <- modelsExternal.ChannelHypervergeDocumentDownload{
		Response: res,
		Error:    err,
	}
}

func (h *ESignHyperVergeBusinessLogicImpl) getSignatureStatus(ctx context.Context, id string, ch chan<- modelsExternal.ChannelHypervergeSignatureStatus) {
	res, err := h.hypervergeExternalProvider.GetSignatureStatus(ctx, id)
	ch <- modelsExternal.ChannelHypervergeSignatureStatus{
		Response: res,
		Error:    err,
	}
}
