package onboarding

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils"

	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	kycUtils "github.com/angel-one/kyc-user-journey/utils"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
)

func (e *EsignOnboardingBusinessLogic) GetKYCDataForEsign(ctx context.Context, appNumber, ipAddress, source string) (*modelsExternal.KYCCreatePDFRequest, error) {
	// create channels.
	chKYC := make(chan ChannelKYCResponse)
	chAddress := make(chan ChannelAddressResponse)
	chBrokerage := make(chan ChannelBrokerageResponse)
	chOtherDocuments := make(chan ChannelOtherDocumentResponse)
	chGeolocation := make(chan ChannelGeolocationResponse)

	// call go routines.
	go e.getKYCDetailsWithChannel(ctx, appNumber, chKYC)
	go e.getKYCAddressWithChannel(ctx, appNumber, chAddress)
	go e.getBrokerageDetailsWithChannel(ctx, appNumber, chBrokerage)
	go e.getOtherDocumentsWithChannel(ctx, appNumber, chOtherDocuments)
	go e.getGeolocationResponseWithChannel(ctx, ipAddress, appNumber, chGeolocation)

	// get data from channel.
	chKYCResponse := <-chKYC
	if chKYCResponse.Error != nil {
		return nil, chKYCResponse.Error
	}
	chAddressResponse := <-chAddress
	if chAddressResponse.Error != nil {
		return nil, chAddressResponse.Error
	}
	chBrokerageResponse := <-chBrokerage
	if chBrokerageResponse.Error != nil {
		return nil, chBrokerageResponse.Error
	}
	chOtherDocumentsResponse := <-chOtherDocuments
	if chOtherDocumentsResponse.Error != nil {
		return nil, chOtherDocumentsResponse.Error
	}
	chGeolocationResponse := <-chGeolocation
	if chGeolocationResponse.Error != nil {
		return nil, chGeolocationResponse.Error
	}

	// get different responses.
	kycData := chKYCResponse.KycResponse
	kycAddress := chAddressResponse.Address

	// compute journey type.
	journeyType := getJourneyTypeAndKRAChecks(kycData, kycAddress)

	aadhaarRes, err := getKYCAadhaarResponse(ctx, kycData, journeyType.isPOA, &kycAddress[0])
	if err != nil {
		return nil, err
	}
	nomineeRes, err := getNomineeData(ctx, kycData)
	if err != nil {
		return nil, err
	}
	consentRes, err := getConsentData(ctx, kycData)
	if err != nil {
		return nil, err
	}

	if consentRes.NominatedAccountOperator.Name != constants.Empty {
		consentRes.NominatedAccountOperator.NomineeNumber = e.getNomineeNumberFromNomineeList(nomineeRes, consentRes.NominatedAccountOperator.Name)
	}

	brokerageRes, err := getBrokerageResponse(kycData, chBrokerageResponse.Brokerage)
	if err != nil {
		return nil, err
	}
	documentRes, err := getDocumentResponse(ctx, journeyType.isPOA, kycData, chOtherDocumentsResponse.Documents)
	if err != nil {
		return nil, err
	}
	sbRes, err := e.getSubBrokerDetailsResponse(ctx, kycData)
	if err != nil {
		return nil, err
	}

	err = e.enrichNomineesWithLocation(ctx, nomineeRes)
	if err != nil {
		return nil, err
	}

	return &modelsExternal.KYCCreatePDFRequest{
		Basic:             getBasicResponse(kycData, journeyType.dob),
		Addresses:         getAddressExternalResponse(kycAddress),
		Bank:              getBankResponse(kycData),
		Personal:          getKYCPersonalResponse(ctx, kycData, journeyType.isKRACompliant, journeyType.isAadhaar, journeyType.isAadhaarOCR, journeyType.isPanOCR),
		Nominee:           nomineeRes,
		Consent:           consentRes,
		KRA:               getKYCKRAResponse(ctx, kycData, journeyType.isKRACompliant),
		Aadhaar:           aadhaarRes,
		PoaDetails:        getPOADetails(kycData, journeyType.isKRACompliant),
		TradingPreference: getTradingPreferences(kycData),
		Brokerage:         brokerageRes,
		Documents:         documentRes,
		GeoLocation:       getGeolocationResponse(ipAddress, chGeolocationResponse.Geolocation),
		SubBrokerDetails:  sbRes,
		ESignFormat:       "B2C_KYC",
		Source:            source,
		JourneyType:       kycData.KycType,
	}, nil
}

func (e *EsignOnboardingBusinessLogic) getNomineeNumberFromNomineeList(nomineeList []modelsExternal.NomineeData, nominatedAccountOperatorName string) int64 {
	for idx := range nomineeList {
		if nomineeList[idx].FullName == nominatedAccountOperatorName {
			return int64(idx + 1)
		}
	}

	return 0
}

func (e *EsignOnboardingBusinessLogic) enrichNomineesWithLocation(ctx context.Context, nomineeRes []modelsExternal.NomineeData) error {
	pincodeSet := make(map[string]struct{}, len(nomineeRes))
	pincodes := make([]string, 0, len(nomineeRes))

	for i := range nomineeRes {
		pin := nomineeRes[i].Address.PinCode
		if pin == "" {
			continue
		}
		if _, exists := pincodeSet[pin]; !exists {
			pincodeSet[pin] = struct{}{}
			pincodes = append(pincodes, pin)
		}
	}

	if len(pincodes) == 0 {
		return nil
	}

	pincodeRecords, err := e.pincodeRepository.FetchRecordsByPincodes(ctx, pincodes)
	if err != nil {
		return fmt.Errorf("failed to fetch pincode data: %w", err)
	}

	pincodeMap := make(map[string]business.Pincode, len(pincodeRecords))
	for i := range pincodeRecords {
		pincodeMap[pincodeRecords[i].Pincode] = pincodeRecords[i]
	}

	for i := range nomineeRes {
		pin := nomineeRes[i].Address.PinCode
		if location, found := pincodeMap[pin]; found {
			nomineeRes[i].City = location.City
			nomineeRes[i].State = location.State
		}
	}

	return nil
}

func (e *EsignOnboardingBusinessLogic) getKYCDetailsWithChannel(ctx context.Context, appNumber string, ch chan ChannelKYCResponse) {
	kycData, err := e.kycRepository.FetchAllKYCDetails(ctx, appNumber)
	res := ChannelKYCResponse{KycResponse: kycData, Error: err}
	ch <- res
}

func (e *EsignOnboardingBusinessLogic) getKYCAddressWithChannel(ctx context.Context, appNumber string, ch chan ChannelAddressResponse) {
	address, err := e.kycRepository.FetchKYCAddress(ctx, appNumber)
	res := ChannelAddressResponse{Address: address, Error: err}
	ch <- res
}

func (e *EsignOnboardingBusinessLogic) getBrokerageDetailsWithChannel(ctx context.Context, appNumber string, ch chan ChannelBrokerageResponse) {
	brokerage, err := e.kycRepository.FetchBrokerageDetailsForEsign(ctx, appNumber)
	res := ChannelBrokerageResponse{Brokerage: brokerage, Error: err}
	ch <- res
}
func (e *EsignOnboardingBusinessLogic) getOtherDocumentsWithChannel(ctx context.Context, appNumber string, ch chan ChannelOtherDocumentResponse) {
	documents, err := e.documentRepository.GetAllDocumentsForAppNumber(ctx, appNumber)
	res := ChannelOtherDocumentResponse{Documents: documents, Error: err}
	ch <- res
}

func (e *EsignOnboardingBusinessLogic) getGeolocationResponseWithChannel(ctx context.Context, ipAddress, appNumber string, ch chan ChannelGeolocationResponse) {
	geolocation, err := e.geolocationRepository.FetchGeolocationForAppNumberAndFlow(ctx, appNumber, constants.SelfieFlowName)
	var geolocationExtRes *modelsExternal.GeoLocationExternalResponse

	if err == nil && kycUtils.IsAllGeolocationDataPresent(geolocation.GeoCity, geolocation.GeoState, geolocation.Latitude, geolocation.Longitude) {
		geolocationExtRes = &modelsExternal.GeoLocationExternalResponse{
			City:      geolocation.GeoCity,
			State:     geolocation.GeoState,
			Latitude:  geolocation.Latitude,
			Longitude: geolocation.Longitude,
		}
	} else {
		geolocationExtRes, err = e.geolocationExternalProvider.GetGeolocation(ctx, ipAddress)
	}

	res := ChannelGeolocationResponse{Geolocation: geolocationExtRes, Error: err}
	ch <- res
}

func (e *EsignOnboardingBusinessLogic) getSubBrokerDetailsResponse(ctx context.Context, kycData *business.KYCEsignDetails) (*modelsExternal.KYCSubBrokerDetailsResponse, error) {
	if kycData.ClientType == "B2B" {
		sbDetails, err := e.subBrokerRepository.GetSubBrokerCommunicationDetails(ctx, kycData.ReferralSbTag, kycData.ReferralBranch)
		if err != nil {
			return nil, err
		}
		return &modelsExternal.KYCSubBrokerDetailsResponse{
			Tag:      kycData.ReferralSbTag,
			Mobile:   sbDetails.Mobile,
			Name:     sbDetails.Name,
			IsMfOnly: kycData.IsMfOnly,
		}, nil
	}
	if kycData.ClientType == "B2C" && kycData.RegisterReferralCode != constants.Empty {
		return &modelsExternal.KYCSubBrokerDetailsResponse{
			Tag:      kycData.ReferralClientID,
			Mobile:   "",
			Name:     kycData.ReferrerName,
			IsMfOnly: false,
		}, nil
	}

	return nil, nil
}

func getBrokerageResponse(kycData *business.KYCEsignDetails, kycBrokerage *business.BrokerageEsignDetails) (*modelsExternal.KYCBrokerageDetailsResponse, error) {
	if kycData.ClientType != "B2B" {
		return nil, nil
	}
	var extRes modelsExternal.KYCBrokerageDetailsResponse
	err := objectmapper.GetMapperInstance().AutoMapper(kycBrokerage, &extRes)
	if err != nil {
		return nil, err
	}
	return &extRes, nil
}

func getGeolocationResponse(ipAddress string, geolocation *modelsExternal.GeoLocationExternalResponse) *modelsExternal.KYCGeoLocationResponse {
	return &modelsExternal.KYCGeoLocationResponse{
		City:      geolocation.City,
		State:     geolocation.State,
		Latitude:  geolocation.Latitude,
		Longitude: geolocation.Longitude,
		IPAddress: ipAddress,
	}
}

type journeyCheck struct {
	isKRACompliant bool
	isPOA          bool
	isAadhaar      bool
	isAadhaarOCR   bool
	isPanOCR       bool
	dob            string
}

func getJourneyTypeAndKRAChecks(kycData *business.KYCEsignDetails, kycAddress []business.Address) *journeyCheck {
	isKRACompliant := kycData.PanNumber != constants.Empty && isKRACompliantApplication(&IsKraCompliantCheckData{
		KraStatus:           kycData.CvlkraStatus,
		KraPermAddressLine1: kycData.CvlkraPermanentAddressLine1,
		KraMobile:           kycData.CvlkraMobile,
		KraEmail:            kycData.CvlkraEmail,
		PoaID:               kycData.PoaID,
		PoaType:             kycData.PoaType,
		KycMobile:           kycData.Mobile,
		KycEmail:            kycData.Email,
	})
	isPOA := !isKRACompliant
	isAadhaar := isPOA && kycData.PoaID != constants.Empty && kycData.PoaType == constants.DigilockerPOAType
	isAadhaarOCR := isPOA && kycData.PoaID != constants.Empty && kycData.PoaType == constants.AadhaarPOAType &&
		kycData.PanOcrData != constants.Empty && len(kycAddress) > 0 &&
		kycData.PanOcrData == strings.ToLower(fmt.Sprintf("%s|%s", MaskAadhaar(kycData.PoaID), kycAddress[0].Pincode))
	dob := fetchDOBBasedOnPriority(FetchDobBasedOnPriority{
		IsAadhaarOcr:   isAadhaarOCR,
		PoaDob:         kycData.PoaDob,
		PanDob:         kycData.PanDob,
		IsDigilocker:   isAadhaar,
		IsKraCompliant: isKRACompliant,
		DobMasterDob:   kycData.DobMasterDob,
	})
	isPanOCR := kycData.PanOcrData != constants.Empty && kycData.PanNumber != constants.Empty && dob != constants.Empty &&
		kycData.PanOcrData == strings.ToLower(fmt.Sprintf("%s|%s", kycData.PanNumber, dob))
	return &journeyCheck{
		isKRACompliant: isKRACompliant,
		isPOA:          isPOA,
		isAadhaar:      isAadhaar,
		isAadhaarOCR:   isAadhaarOCR,
		isPanOCR:       isPanOCR,
		dob:            dob,
	}
}

func getKYCPersonalResponse(ctx context.Context, kycData *business.KYCEsignDetails, isKRACompliant, isAadhaar, isAadhaarOCR, isPanOCR bool) modelsExternal.KYCPersonalResponse {
	firstName, middleName, lastName := getNamePartsFromFullName(kycData.PanFullName)
	fatherFirstName, fatherMiddleName, fatherLastName := getNamePartsFromFullName(kycData.PanFatherName)

	isCvlKRA := kycData.KraInfo == constants.CvlKRA
	isKRA := isKRAApplication(kycData.CvlkraStatus)
	isCKYC := false

	if percentagerollout.IsFeatureRolloutEnabled(ctx, kycData.AppNumber, constants.ApplicationRolloutPanImageConfigKey, config.Feature().Enable.PanImage, false) && kycData.PanImagePath == constants.Empty {
		isCKYC = true
	}

	return modelsExternal.KYCPersonalResponse{
		FirstName:             firstName,
		MiddleName:            middleName,
		LastName:              lastName,
		FatherFirstName:       fatherFirstName,
		FatherMiddleName:      fatherMiddleName,
		FatherLastName:        fatherLastName,
		IsDDPI:                kycData.DDPIOptIn,
		IsWhatsapp:            kycData.WhatsappOptIn,
		MaritalStatus:         &kycData.MaritalStatus,
		IncomeID:              &kycData.Income,
		OccupationID:          &kycData.Occupation,
		MotherFirstName:       constants.Empty,
		MotherMiddleName:      constants.Empty,
		MotherLastName:        constants.Empty,
		MaidenFirstName:       constants.Empty,
		MaidenMiddleName:      constants.Empty,
		MaidenLastName:        constants.Empty,
		BasicIdentityDocument: "pan",
		ResidentialStatus:     "R",
		IsCvlKRA:              isCvlKRA,
		IsKRA:                 isKRA,
		IsKRACompliant:        isKRACompliant,
		IsAadhaar:             isAadhaar,
		IsAadhaarOCR:          isAadhaarOCR,
		IsPanOcr:              isPanOCR,
		IsCKYC:                isCKYC, // no longer needed
		TradingExperienceID:   constants.Empty,
	}
}

func getKYCKRAResponse(ctx context.Context, kycData *business.KYCEsignDetails, isKRACompliant bool) *modelsExternal.KYCKRAResponse {
	if !isKRACompliant {
		log.Debug(ctx).Msg("returning nil from getKYCKRAResponse as kyc.IsKRACompliant nil or false")
		return nil
	}
	if kycData.KraInfo == constants.Empty || kycData.PanNumber == constants.Empty ||
		kycData.CvlkraStatusDate == constants.Empty || kycData.CvlkraIpvFlag == constants.Empty {
		log.Debug(ctx).Msg("returning nil from getKYCKRAResponse as one of mandatory flags nil")
		return nil
	}
	return &modelsExternal.KYCKRAResponse{
		KRAInfo:      kycData.KraInfo,
		Pan:          kycData.PanNumber,
		StatusDate:   kycData.CvlkraStatusDate,
		AppEntryDate: kycData.CvlkraStatusDate,
		KYCMode:      kycData.CvlkraKycMode,
		IPVFlag:      kycData.CvlkraIpvFlag,
	}
}

func getKYCAadhaarResponse(ctx context.Context, kycData *business.KYCEsignDetails, isPOA bool, address *business.Address) (*modelsExternal.KYCAadhaarResponse, error) {
	if !isPOA {
		return nil, nil
	}
	if kycData.PoaType != constants.DigilockerPOA && kycData.PoaType != constants.AadhaarPOA {
		return nil, nil
	}

	firstName, middleName, lastName := getNamePartsFromFullName(kycData.PoaFullName)
	var photoURL, photoPath string

	if kycData.PoaPhotoPath != constants.Empty {
		photoURL = kycData.PoaPhotoPath
	} else if kycData.SelfieImagePath != constants.Empty {
		photoURL = kycData.SelfieImagePath
	}
	if photoPath != constants.Empty {
		url, err := s3.GetS3Client().GetSignedURL(ctx, photoPath, time.Duration(config.S3().SignURLExpiryInSeconds)*time.Second)
		if err != nil {
			return nil, err
		}
		photoURL = url
	}

	var poaDateTime string
	if !kycData.PoaLastUpdateTS.IsZero() {
		defaultTimeZoneTime := ConvertTimeToDefaultTimezone(&kycData.PoaLastUpdateTS)
		poaDateTime = defaultTimeZoneTime.Format("02-01-2006 15:04:05")
	}

	return &modelsExternal.KYCAadhaarResponse{
		FirstName:      firstName,
		MiddleName:     middleName,
		LastName:       lastName,
		DOB:            &kycData.PoaDob,
		AddressLine1:   &address.AddressLine1,
		AddressLine2:   &address.AddressLine2,
		AddressLine3:   &address.AddressLine3,
		City:           &address.City,
		State:          &address.State,
		Pincode:        &address.Pincode,
		Gender:         getGender(kycData.PersonalGender, kycData.PoaGender, kycData.CvlkraGender, kycData.CvlkraStatus),
		Aadhaar:        &kycData.PoaID,
		AadhaarNumber:  &kycData.PoaID,
		PhotoURL:       &photoURL,
		GenerationDate: poaDateTime,
		DownloadDate:   poaDateTime,
		LandMark:       constants.Empty,
		Locality:       constants.Empty,
	}, nil
}

func getPOADetails(kycData *business.KYCEsignDetails, isKRACompliant bool) *modelsExternal.PoaDetails {
	if isKRACompliant || kycData.PoaType == constants.Empty {
		return nil
	}
	poaDetails := modelsExternal.PoaDetails{}
	poaType := strings.ToLower(kycData.PoaType)

	switch poaType {
	case constants.AadhaarPOAType:
		poaDetails.Type = constants.AadhaarPOATypeValue
	case constants.DigilockerPOAType:
		poaDetails.Type = constants.DigilockerPOATypeValue
	case constants.PassportPOAType:
		poaDetails.Type = constants.PassportPOATypeValue
	case constants.DrivingLicensePOAType:
		poaDetails.Type = constants.DrivingLicensePOATypeValue
	case constants.VoterCardPOAType:
		poaDetails.Type = constants.VoterCardPOATypeValue
	default:
		poaDetails.Type = constants.OtherPoaTypeValue
		poaDetails.DocName = kycData.PoaType
	}

	if poaType == constants.AadhaarPOAType || poaType == constants.DigilockerPOAType {
		if kycData.PoaID != constants.Empty {
			poaDetails.Number = GetAadhaarLastFourNumber(kycData.PoaID)
		}
	} else {
		if kycData.PoaID != constants.Empty {
			poaDetails.Number = kycData.PoaID
		}
	}

	if kycData.PoaExpiresOn != constants.Empty {
		expireDate, _ := time.Parse("02/01/2006", kycData.PoaExpiresOn)
		poaDetails.ExpiryDate = expireDate.Format("02-01-2006")
	}

	return &poaDetails
}

func getNomineeData(ctx context.Context, kycData *business.KYCEsignDetails) ([]modelsExternal.NomineeData, error) {
	var nomineeList []business.NomineeData
	var extNomineeList []modelsExternal.NomineeData
	if kycData.Nominees == constants.Empty || !kycData.IsNomineeValid {
		log.Info(ctx).Msg("Nominee details not present") // UPCOMING: changes according to opt_out logic
		return extNomineeList, nil
	}
	err := utils.UnmarshalJSON([]byte(kycData.Nominees), &nomineeList)
	if err != nil {
		log.Error(ctx).Err(err).Msg("Error unmarshalling nominee data")
		return nil, err
	}

	err = objectmapper.GetMapperInstance().MapperSlice(&nomineeList, &extNomineeList)
	if err != nil {
		return nil, err
	}

	return extNomineeList, nil
}

func getConsentData(ctx context.Context, kycData *business.KYCEsignDetails) (modelsExternal.ConsentData, error) {
	var consent business.ConsentData
	var extConsent modelsExternal.ConsentData
	if kycData.Nominees == constants.Empty || !kycData.IsNomineeValid || kycData.Consent == constants.Empty {
		log.Info(ctx).Msg("Nominee details not present")
		return extConsent, nil
	}
	err := utils.UnmarshalJSON([]byte(kycData.Consent), &consent)
	if err != nil {
		log.Error(ctx).Err(err).Msg("Error unmarshalling nominee data")
		return extConsent, err
	}

	err = objectmapper.GetMapperInstance().Mapper(&consent, &extConsent)
	if err != nil {
		return extConsent, err
	}

	return extConsent, nil
}

func getAddressExternalResponse(addresses []business.Address) []modelsExternal.KYCAddress {
	var extAddress = make([]modelsExternal.KYCAddress, len(addresses))
	for i := range addresses {
		address := &addresses[i]
		if address.Country == constants.Empty {
			address.Country = constants.INDIA
		}
		extAddress[i] = modelsExternal.KYCAddress{
			Type:         address.Type,
			AddressLine1: address.AddressLine1,
			AddressLine2: address.AddressLine2,
			AddressLine3: address.AddressLine3,
			City:         address.City,
			State:        address.State,
			Country:      address.Country,
			Pincode:      address.Pincode,
			AddressType:  constants.ZERO,
		}
	}
	return extAddress
}

func getTradingPreferences(kycData *business.KYCEsignDetails) modelsExternal.KYCTradingPreferenceResponse {
	isFnoProofAvailable := kycData.DerivativesPath != constants.Empty || kycData.CamsConsentStatus == constants.CamsConsentStatusApproved
	return modelsExternal.KYCTradingPreferenceResponse{
		IsEquity:    true, // true by default
		IsFNO:       isFnoProofAvailable && kycData.UserPreferencesSegments.IsFnO,
		IsCurrency:  isFnoProofAvailable && kycData.UserPreferencesSegments.IsCurrency,
		IsCommodity: isFnoProofAvailable && kycData.UserPreferencesSegments.IsCommodity,
		DPId:        kycData.DepositoryID,
		MTF:         kycData.UserPreferencesMtf,
	}
}

func getBankResponse(kycData *business.KYCEsignDetails) *modelsExternal.KYCBankResponse {
	return &modelsExternal.KYCBankResponse{
		BankName:        kycData.BankName,
		AccountNumber:   &kycData.BankAccountNumber,
		IFSC:            &kycData.BankIfscCode,
		IsIMPS:          kycData.IsImps,
		FullName:        &kycData.BankFullName,
		TransactionDate: &kycData.BankImpsTS,
	}
}

func getBasicResponse(kycData *business.KYCEsignDetails, dob string) modelsExternal.KYCBasicResponse {
	return modelsExternal.KYCBasicResponse{
		AppNumber:           kycData.AppNumber,
		FullName:            &kycData.PanFullName,
		Mobile:              kycData.Mobile,
		Pan:                 &kycData.PanNumber,
		DOB:                 &dob,
		Email:               &kycData.Email,
		Gender:              getGender(kycData.PersonalGender, kycData.PoaGender, kycData.CvlkraGender, kycData.CvlkraStatus),
		SettlementFrequency: kycData.SettlementFrequency,
		KycMode:             getKycMode(kycData.PoaType),
		ApplicationType:     getApplicationType(kycData.CvlkraStatus),
		ReferralType:        kycData.ClientType,
		ReferredByType:      &kycData.ReferralType,
		IsMITCEnabled:       true,
	}
}

func getDocumentResponse(ctx context.Context, isPOA bool, kycData *business.KYCEsignDetails, otherDocuments []business.Document) (*[]modelsExternal.KYCDocumentResponse, error) {
	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	expiryDuration := time.Duration(signedURLExpiry) * time.Second

	var documentsResponse []modelsExternal.KYCDocumentResponse

	documents := []modelsExternal.KYCDocumentResponse{
		{Path: kycData.PanImagePath, Type: constants.BasicDocument, Flag: constants.Empty},                  // basic document which is the pan card.
		{Path: kycData.BankImagePath, Type: constants.FinancialDocument, Flag: constants.Empty},             // financial document.
		{Path: kycData.SelfieImagePath, Type: constants.PhotoDocument, Flag: constants.UserSelfiePhotoFlag}, // photo document.
		{Path: kycData.SignatureImagePath, Type: constants.SignatureDocument, Flag: constants.Empty},        // signature document.
		{Path: kycData.DerivativesPath, Type: constants.FNODocument, Flag: constants.Empty},                 // fno document.
	}
	if isPOA {
		// address document.
		if kycData.PoaType != constants.DigilockerPOAType {
			documents = append(documents,
				modelsExternal.KYCDocumentResponse{
					Path: kycData.PoaFirstImagePath, Type: constants.AddressDocument, Flag: constants.Empty},
				modelsExternal.KYCDocumentResponse{
					Path: kycData.PoaSecondImagePath, Type: constants.AddressDocument, Flag: constants.Empty},
			)
		}

		// photo document.
		if kycData.PoaPhotoPath != constants.Empty {
			documents = append(documents, modelsExternal.KYCDocumentResponse{
				Path: kycData.PoaPhotoPath, Type: constants.PhotoDocument, Flag: constants.DigilockerPhotoFlag})
		} else {
			documents = append(documents, modelsExternal.KYCDocumentResponse{
				Path: kycData.SelfieImagePath, Type: constants.PhotoDocument, Flag: constants.DigilockerPhotoFlag})
		}
	}

	// get url for documents.
	for _, document := range documents {
		if document.Path != constants.Empty {
			url, err := s3.GetS3Client().GetSignedURL(ctx, document.Path, expiryDuration)
			if err != nil {
				return nil, err
			}
			document.URL = url
			documentsResponse = append(documentsResponse, document)
		}
	}

	for i := range otherDocuments {
		document := &otherDocuments[i]
		// if document needs to be ignored
		if slices.Contains(config.Application().Validations.Esign.AdditionalDocTypeToIgnore, document.Type) {
			continue
		}

		t, ok := config.Application().Validations.Esign.AdditionalDocTypeMapping[document.Type]
		if !ok {
			return nil, constants.ErrEsignDocumentTypeMapping.Value()
		}
		f := constants.Empty
		if t == constants.PhotoDocument {
			f = constants.UserUploadedPhotoFlag
		}
		if document.Path != constants.Empty {
			url, err := s3.GetS3Client().GetSignedURL(ctx, document.Path, expiryDuration)
			if err != nil {
				return nil, err
			}
			documentsResponse = append(documentsResponse, modelsExternal.KYCDocumentResponse{
				Flag: f,
				URL:  url,
				Type: t,
			})
		}
	}

	return &documentsResponse, nil
}
