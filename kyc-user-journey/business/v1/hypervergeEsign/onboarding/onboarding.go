package onboarding

import (
	"context"
	"fmt"
	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	"slices"
	"strings"
)

func (e *EsignOnboardingBusinessLogic) CreateESignPDF(ctx context.Context, header *models.Headers, meta *models.Meta) (*business.BasicHypervergeEsignDetails, error) {
	kycData, err := e.GetKYCDataForEsign(ctx, meta.AppNumber, header.IPAddress, header.Source)
	if err != nil {
		return nil, err
	}
	// generate dpID if not present.
	if kycData.TradingPreference.DPId == constants.Empty && kycData.JourneyType != constants.PhysicalMFOnlyJourney {
		dpID, errDP := e.depositoryCommonBusinessLogic.HandleDepository(ctx, meta.AppNumber, meta.Mobile, header.Source)
		if errDP != nil {
			return nil, errDP
		}
		kycData.TradingPreference.DPId = dpID
	}

	// external pdf generation call.
	createPDFResponse, err := e.kycFormExternalProvider.CreateEsignPDF(ctx, kycData)
	if err != nil {
		return nil, err
	}
	return &business.BasicHypervergeEsignDetails{
		Mobile:      kycData.Basic.Mobile,
		FullName:    *kycData.Basic.FullName,
		DOB:         *kycData.Basic.DOB,
		ESignFormat: kycData.ESignFormat,
		City:        kycData.GeoLocation.City,
		PDFPath:     createPDFResponse.Data.Path,
		Source:      header.Source,
		AppNumber:   meta.AppNumber,
		MITCPageNo:  createPDFResponse.Data.MITCPageNo,
	}, nil
}

func (e *EsignOnboardingBusinessLogic) SaveHypervergeInitiateDetails(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, basicHypervergeDetails *business.BasicHypervergeEsignDetails, HypervergeEsignRequestID string) error {
	esignDTO := business.Esign{
		AppNumber:        req.AppNumber,
		Type:             constants.Empty,
		FullName:         constants.Empty,
		FilePath:         constants.Empty,
		SubmitterCode:    constants.Empty,
		SetuRequestID:    HypervergeEsignRequestID,
		MITCPageNo:       basicHypervergeDetails.MITCPageNo,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}

	err := e.esignRepository.FreshUpsert(ctx, &esignDTO)
	if err != nil {
		return err
	}
	return nil
}

func (e *EsignOnboardingBusinessLogic) CheckEsignEligibility(ctx context.Context, appNumber, mobile, source string) error {
	includeOneKYCStatus := true
	if slices.Contains(config.Application().Validations.AllowedAdminSources, source) {
		includeOneKYCStatus = false
	}
	statusResponse, err := e.statusBusinessLogic.GetStatusResponse(ctx, appNumber, mobile, "", false, includeOneKYCStatus, false)
	if err != nil {
		return err
	}
	err = e.isNomineePanAllowed(ctx, appNumber)
	if err != nil {
		log.Error(ctx).Msg("Same Nominee Pan or Guardian Pan")
		return err
	}

	for _, val := range statusResponse.Lead.Flows {
		if val.Name == constants.OnboardingFlowEsign && val.Status == constants.OnboardingFlowStatusComplete {
			log.Info(ctx).Msg("Esign flow is already completed")
			return constants.ErrEsignAlreadyCompleted.Value()
		}
		if val.Name != constants.OnboardingFlowEsign && val.Status != constants.OnboardingFlowStatusComplete {
			log.Info(ctx).Msgf("%s flow is not completed", val.Name)

			return constants.ErrIncompleteFlow.WithMessage(fmt.Sprintf("%s flow is not completed", getStepName(val.Name)))
		}
	}

	return nil
}

func (e *EsignOnboardingBusinessLogic) GetBasicHypervergeEsignDetails(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest) (*business.BasicHypervergeEsignDetails, error) {
	geolocationFlow := config.HyperVergeESign().Flow.Onboarding.GeolocationFlowName

	basicEsignDetails, err := e.kycRepository.FetchBasicEsignDetails(ctx, req.Meta.AppNumber, geolocationFlow)
	if err != nil {
		return nil, err
	}

	return &business.BasicHypervergeEsignDetails{
		Mobile:      req.Meta.Mobile,
		Source:      req.Headers.Source,
		AppNumber:   req.Meta.AppNumber,
		ESignFormat: "B2C_KYC",
		FullName:    basicEsignDetails.PanFullName,
		DOB:         basicEsignDetails.PanDOB,
		City:        basicEsignDetails.GeolocationCity,
		PDFPath:     basicEsignDetails.EsignFilePath,
		MITCPageNo:  basicEsignDetails.MITCPageNo,
	}, nil
}

func (e *EsignOnboardingBusinessLogic) GetRedirectRequestData(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, esignFormat string) (*business.HypervergeESignRedirectURLData, error) {
	data := business.HypervergeESignRedirectURLData{
		Source:        req.Source,
		AppNumber:     req.AppNumber,
		Mobile:        req.Mobile,
		Platform:      req.Platform,
		AppVersion:    req.AppVersion,
		CorrelationID: req.AppNumber,
		Device:        req.Device,
		DeviceOS:      req.DeviceOS,
		AgentID:       req.AgentID,
	}
	return &data, nil
}

func (e *EsignOnboardingBusinessLogic) GetSigningConfigID(ctx context.Context, req *modelsAPIV1.ESignHypervergeAPIRequest, basicHypervergeDetails *business.BasicHypervergeEsignDetails) (string, error) {
	signingConfig := config.HyperVergeESign().Flow.Onboarding.SigningConfigID.Default

	mitcConfigKey := fmt.Sprintf("mitcPageCustomPlan%d", basicHypervergeDetails.MITCPageNo)

	if strings.EqualFold(basicHypervergeDetails.PlanName, constants.B2bAssistedPlanName) ||
		strings.EqualFold(basicHypervergeDetails.PlanName, constants.ITradePremier) ||
		basicHypervergeDetails.PlanName == constants.Empty {
		// value add b2b
		// itrade premier b2b
		// itrade premier b2c
		// if no entry in referral entry then default as itrade premier
		mitcConfigKey = fmt.Sprintf("mitcPage%d", basicHypervergeDetails.MITCPageNo)
	}

	mitcConfig := config.HyperVergeESign().Flow.Onboarding.SigningConfigID.Others[mitcConfigKey]

	if mitcConfigKey == constants.Empty {
		log.Info(ctx).Msgf("mitc config not found for %s", mitcConfigKey)
	} else {
		signingConfig = mitcConfig
	}
	return signingConfig, nil
}

// Redirect

func (e *EsignOnboardingBusinessLogic) SaveSignedDocumentDetails(ctx context.Context, appNumber, esignType, signerAadhaarName, filePath, submitterCode string) error {
	err := e.esignRepository.UpdateEsignSuccessDetails(ctx, appNumber, esignType, signerAadhaarName, filePath, "hyperverge", submitterCode)
	if err != nil {
		return err
	}
	return nil
}

// Helper: Checks if nominee pan or nominee's guardian pan is same as account holder's pan.
func (e *EsignOnboardingBusinessLogic) isNomineePanAllowed(ctx context.Context, appNumber string) error {
	panAndNomineeData, err := e.panRepository.GetPanAndNomineeData(ctx, appNumber)
	if err != nil {
		return err
	}
	if panAndNomineeData.Nominees != constants.Empty {
		// Nominee Opt-in
		var nomineeList []business.NomineeData
		err = utils.UnmarshalJSON([]byte(panAndNomineeData.Nominees), &nomineeList)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error unmarshalling nominee data")
			return err
		}
		for i := 0; i < len(nomineeList); i++ {
			nominees := &nomineeList[i]
			if nominees.Pan == panAndNomineeData.Pan {
				log.Error(ctx).Err(err).Msg("error same nominee and user pan")
				return constants.ErrSameNomineePan.Value()
			}
			if nominees.GuardianPan == panAndNomineeData.Pan {
				log.Error(ctx).Err(err).Msg("error same nominee and guardian pan")
				return constants.ErrNomineeGuardianPanSame.Value()
			}
		}
	}
	return nil
}
