package onboarding

import (
	"context"

	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
)

type KYCFormExternalProvider interface {
	CreateEsignPDF(ctx context.Context, request *modelsExternal.KYCCreatePDFRequest) (*modelsExternal.KYCCreatePDFResponse, error)
}

type WorkflowExternalProvider interface {
	StartProcessInstance(ctx context.Context, workflowName string, request modelsExternal.ProcessStartRequest) (*modelsExternal.ProcessStartResponse, error)
}

type GeolocationExternalProvider interface {
	GetGeolocation(ctx context.Context, ipAddress string) (*modelsExternal.GeoLocationExternalResponse, error)
}

type KYCRepository interface {
	FetchBasicEsignDetails(ctx context.Context, appNumber, flowName string) (*business.BasicEsignDetails, error)
	FetchAllKYCDetails(ctx context.Context, appNumber string) (*business.KYCEsignDetails, error)
	FetchKYCAddress(ctx context.Context, appNumber string) ([]business.Address, error)
	FetchBrokerageDetailsForEsign(ctx context.Context, appNumber string) (*business.BrokerageEsignDetails, error)
}

type DocumentRepository interface {
	GetAllDocumentsForAppNumber(ctx context.Context, appNumber string) ([]business.Document, error)
}

type EsignRepository interface {
	FreshUpsert(ctx context.Context, esignDTO *business.Esign) error
	FetchRecordByAppNumber(ctx context.Context, appNumber string) (*business.Esign, error)
	UpdateEsignSuccessDetails(ctx context.Context, appNumber, esignType, fullName, filePath, source, submitterCode string) error
}

type SubBrokerRepository interface {
	GetSubBrokerCommunicationDetails(ctx context.Context, subBrokerTag string, subBrokerBranch string) (*business.SubBroker, error)
}

type StartRepository interface {
	UpdateApplicationStatus(ctx context.Context, appNumber, source string, applicationStatus int) error
	FetchStartEntryForAppNumberWithContext(ctx context.Context, appNumber string) (*business.Start, error)
}

type DepositoryCommonBusinessLogic interface {
	HandleDepository(ctx context.Context, appNumber, mobile, source string) (string, error)
}

type StatusBusinessLogic interface {
	GetStatusResponse(ctx context.Context, appNumber, mobile, kycJourneyType string, skipClientData, includeOneKYCStatus, ignoreRejections bool) (*modelsAPIV1.StatusAPIResponse, error)
}

type OnboardingCompleteBusinessLogic interface {
	TriggerOnboardingCompleteMessagePublish(ctx context.Context, data *modelsAPIV1.ESignSetuRedirectURLData) error
}

type GeoLocationRepository interface {
	FetchGeolocationForAppNumberAndFlow(ctx context.Context, appNumber, flow string) (*business.GeoLocation, error)
}

type PanRepository interface {
	GetPanAndNomineeData(ctx context.Context, appNumber string) (*business.PanAndNomineeData, error)
}

type PincodeRepository interface {
	FetchRecordsByPincodes(ctx context.Context, pincodes []string) ([]business.Pincode, error)
}

type EsignOnboardingBusinessLogic struct {
	kycFormExternalProvider         KYCFormExternalProvider
	workflowExternalProvider        WorkflowExternalProvider
	geolocationExternalProvider     GeolocationExternalProvider
	kycRepository                   KYCRepository
	esignRepository                 EsignRepository
	documentRepository              DocumentRepository
	subBrokerRepository             SubBrokerRepository
	startRepository                 StartRepository
	geolocationRepository           GeoLocationRepository
	panRepository                   PanRepository
	depositoryCommonBusinessLogic   DepositoryCommonBusinessLogic
	statusBusinessLogic             StatusBusinessLogic
	onboardingCompleteBusinessLogic OnboardingCompleteBusinessLogic
	pincodeRepository               PincodeRepository
}

func NewEsignOnboardingBusinessLogic(kycRepository KYCRepository, documentRepository DocumentRepository, esignRepository EsignRepository,
	subBrokerRepository SubBrokerRepository, startRepository StartRepository, geolocationRepository GeoLocationRepository, panRepository PanRepository, kycFormExternalProvider KYCFormExternalProvider,
	workflowExternalProvider WorkflowExternalProvider, geolocationExternalProvider GeolocationExternalProvider,
	depositoryCommonBusinessLogic DepositoryCommonBusinessLogic, statusBusinessLogic StatusBusinessLogic, onboardingCompleteBusinessLogic OnboardingCompleteBusinessLogic,
	pincodeRepository PincodeRepository) *EsignOnboardingBusinessLogic {
	return &EsignOnboardingBusinessLogic{
		kycFormExternalProvider:         kycFormExternalProvider,
		workflowExternalProvider:        workflowExternalProvider,
		geolocationExternalProvider:     geolocationExternalProvider,
		kycRepository:                   kycRepository,
		esignRepository:                 esignRepository,
		documentRepository:              documentRepository,
		subBrokerRepository:             subBrokerRepository,
		startRepository:                 startRepository,
		geolocationRepository:           geolocationRepository,
		panRepository:                   panRepository,
		depositoryCommonBusinessLogic:   depositoryCommonBusinessLogic,
		statusBusinessLogic:             statusBusinessLogic,
		onboardingCompleteBusinessLogic: onboardingCompleteBusinessLogic,
		pincodeRepository:               pincodeRepository,
	}
}
