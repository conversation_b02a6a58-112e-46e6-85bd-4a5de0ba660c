package onboarding

import (
	"fmt"
	"slices"
	"strings"
	"time"

	modelExtv1 "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils"
)

func getKycMode(poaType string) string {
	if poaType == constants.Empty {
		return constants.ONLINEKYC
	}
	switch strings.ToLower(poaType) {
	case constants.Digilocker:
		return constants.DIGILOCKERMODE
	default:
		return constants.ONLINEKYC
	}
}

func getGender(userInputGender, poaGender, kraGender, cvlkraStatus string) string {
	if poaGender != constants.Empty {
		return poaGender
	}

	kraStatus := utils.TwoLengthKraStatus(cvlkraStatus)
	acceptedKraStatus := config.Application().Validations.AllowedKRAStatusToConsiderGender

	if slices.Contains(acceptedKraStatus, kraStatus) && kraGender != constants.Empty {
		if kraGender == constants.KRAMaleGender {
			return constants.KYC2MaleGender
		} else if kraGender == constants.KRAFeMaleGender {
			return constants.KYC2FemaleGender
		} else if kraGender == constants.KRAOthersGender {
			return constants.KYC2OthersGender
		}
	}
	if userInputGender == constants.KYC2OthersGender || userInputGender == constants.DigioSingleCharacterOthersGender {
		return constants.GenderTypeTransgender
	} else {
		return userInputGender
	}
}

// returns first,middle,last name from full name.
func getNamePartsFromFullName(fullName string) (string, string, string) {
	names := strings.Fields(fullName)
	size := len(names)
	switch size {
	case 0:
		return constants.Empty, constants.Empty, constants.Empty
	case 1:
		return names[0], constants.Empty, constants.Empty
	case 2:
		return names[0], constants.Empty, names[1]
	case 3:
		return names[0], names[1], names[2]
	default:
		return strings.Join(names[:size-2], constants.Space), names[size-2], names[size-1]
	}
}

type IsKraCompliantCheckData struct {
	KraStatus           string
	KraPermAddressLine1 string
	KraMobile           string
	KraEmail            string
	PoaID               string
	PoaType             string
	KycMobile           string
	KycEmail            string
}

const MobileSignificantLength = 10
const FOUR = 4

func getApplicationType(kraStatus string) string {
	if kraStatus == constants.Empty || slices.Contains(config.KRA().IsKRANotAvailableStatuses, kraStatus) {
		return constants.NewApplicationType
	}
	return constants.ModificationApplicationType
}

func isKRAApplication(kraStatus string) bool {
	return slices.Contains(config.KRA().IsKRAStatuses, kraStatus)
}

func isKRACompliantApplication(data *IsKraCompliantCheckData) bool {
	return slices.Contains(config.KRA().KRACompliantStatuses, data.KraStatus) && // if status like %07
		data.KraPermAddressLine1 != constants.Empty && // if kra received from KRA data fetch
		(data.PoaID == constants.Empty && data.PoaType == constants.Empty) && // if poa not collected
		len(data.KraMobile) >= MobileSignificantLength && // if valid length mobile
		strings.EqualFold(data.KraMobile[len(data.KraMobile)-MobileSignificantLength:], data.KycMobile) && // if kyc mobile matches kra mobile
		strings.EqualFold(data.KraEmail, data.KycEmail) // if kyc email matches email mobile
}

type FetchDobBasedOnPriority struct {
	IsAadhaarOcr   bool
	PoaDob         string
	PanDob         string
	IsDigilocker   bool
	IsKraCompliant bool
	DobMasterDob   string
}

func fetchDOBBasedOnPriority(data FetchDobBasedOnPriority) string {
	if data.DobMasterDob != constants.Empty && data.DobMasterDob != constants.MigratedDoB {
		return data.DobMasterDob
	}

	if data.IsKraCompliant {
		return data.PanDob
	}

	if data.PoaDob != constants.Empty && (data.IsDigilocker || data.IsAadhaarOcr) &&
		utils.IsDOBValid(data.PoaDob, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge) == nil {
		return data.PoaDob
	}

	return data.PanDob
}

func MaskAadhaar(aadhaarNumber string) string {
	if !utils.IsValidAgainstRegex(config.Application().Validations.AadhaarRegex, aadhaarNumber) {
		return aadhaarNumber
	}
	return fmt.Sprintf("XXXXXXXX%s", aadhaarNumber[8:])
}
func FetchDefaultLocation() *time.Location {
	IstTimezone := config.Application().Defaults.Timezone
	india, _ := time.LoadLocation(IstTimezone)
	return india
}
func ConvertTimeToDefaultTimezone(originalTime *time.Time) time.Time {
	loc := FetchDefaultLocation()
	return originalTime.In(loc)
}

func GetAadhaarLastFourNumber(s string) string {
	if len(s) > FOUR {
		return s[len(s)-4:]
	}
	return s
}

func getEsignDTOForPDFPathUpdate(appNumber, source, filePath string, mitcPageNo int) *business.Esign {
	return &business.Esign{
		AppNumber:        appNumber,
		Type:             constants.Empty,
		FullName:         constants.Empty,
		FilePath:         filePath,
		SubmitterCode:    constants.Empty,
		SetuRequestID:    constants.Empty,
		MITCPageNo:       mitcPageNo,
		CreateSource:     source,
		LastUpdateSource: source,
	}
}

type ChannelKYCResponse struct {
	KycResponse *business.KYCEsignDetails
	Error       error
}

type ChannelAddressResponse struct {
	Address []business.Address
	Error   error
}

type ChannelBrokerageResponse struct {
	Brokerage *business.BrokerageEsignDetails
	Error     error
}

type ChannelOtherDocumentResponse struct {
	Documents []business.Document
	Error     error
}

type ChannelGeolocationResponse struct {
	Geolocation *modelExtv1.GeoLocationExternalResponse
	Error       error
}

func isSourceAllowedForOnboardingCompleteMessagePublish(source string) bool {
	return !slices.Contains(config.Application().Validations.AllowedAdminSources, source)
}

func getStepName(flow string) string {
	switch flow {
	case constants.PoaFlowName:
		return constants.AddressStep
	case constants.RPDFlowName, constants.BankFlowName:
		return constants.BankStep
	case constants.CamsFlowName, constants.DerivativesFlowName:
		return constants.FNOStep
	default:
		return flow
	}
}
