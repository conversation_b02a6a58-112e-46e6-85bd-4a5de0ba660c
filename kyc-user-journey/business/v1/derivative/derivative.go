package derivative

import (
	"bytes"
	"context"
	"fmt"
	"mime/multipart"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (d *DerivativeLogic) UploadDerivativeProof(ctx context.Context, req *modelsAPIv1.DerivativeUploadRequest) error {
	isOnboarding := d.commonOnboardingBusinessLogic.IsOnboardingAppNumber(req.AppNumber)
	if isOnboarding {
		err := d.derivativesCamsAndClientOptOutRepository.DeleteAllDerivativesData(ctx, req.AppNumber, req.Source, isOnboarding)
		if err != nil {
			log.Error(ctx).Err(err).Msg("UploadDerivativeProof: error deleting all derivatives data")
			return err
		}
	}

	// get s3 file path.
	fileExtension := utils.GetMultipartFileExtension(req.Data.File)
	filePath := d.GetFilePath(req.AppNumber, fileExtension)

	fileData, err := getDerivativeFileDataForS3(ctx, req, fileExtension)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: error getting file data for s3")
		return err
	}

	// derivative file ocr integration
	if req.Data.DoOcrValidation {
		ocrData, ocrErr := d.ValidateDerivativeFileOCR(ctx, req.Data.Type, req.Data.File)

		// save ocr response in db for future
		ocrDTO, dtoError := getOcrDTOFromDerivativeOCRResponse(req.AppNumber, ocrData, ocrErr)
		if dtoError != nil {
			log.Error(ctx).Msg("DerivativeOCR: Failed to make ocr details")
			return dtoError
		}
		dtoError = d.ocrRepository.Save(ctx, ocrDTO)
		if dtoError != nil {
			log.Error(ctx).Msg("DerivativeOCR: Failed to insert derivative ocr details")
			return dtoError
		}

		// return with ocr error
		if ocrErr != nil {
			log.Error(ctx).Msg("DerivativeOCR: Ocr Validation Failed with error")
			return ocrErr
		}
	}

	// Uploading file to S3.
	err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: error uploading file to s3")
		return constants.ErrUploadingFile.WithDetails([]string{constants.Derivatives, err.Error()})
	}
	log.Info(ctx).Str("filePath", filePath).Msg("UploadDerivativeProof - Uploaded derivative proof to s3")
	// save in DB.
	derivativesDTO := getDerivativesDTO(req, filePath)
	if !isOnboarding {
		err = d.derivativesRepository.Save(ctx, derivativesDTO)
	} else {
		userPreferencesDTO := getUserPreferencesDTOWithSegments(req)
		err = d.derivativesAndUserPreferencesRepository.SaveDerivativesAndUpdateSegments(ctx, derivativesDTO, userPreferencesDTO)
	}
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("generateAndSaveHoldingsPdf: error saving derivatives in db")
		return err
	}

	// all good return success.
	log.Debug(ctx).Str(constants.LogAppNumberKey, req.AppNumber).Msg("UploadDerivativeProof: success")
	return nil
}

func (d *DerivativeLogic) SkipDerivative(ctx context.Context, req *modelsAPIv1.DerivativeSkipRequest) error {
	// delete all derivatives data.
	err := d.derivativesCamsAndClientOptOutRepository.DeleteAllDerivativesData(ctx, req.AppNumber, req.Source, true)
	if err != nil {
		log.Error(ctx).Err(err).Msg("SkipDerivative: error deleting all derivatives data")
		return err
	}

	clientOptOutDTO := getClientOptOutDTO(req, constants.DerivativesFlowName)
	err = d.clientOptOutRepository.FreshUpsert(ctx, clientOptOutDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("SkipDerivative: error inserting client opt out data")
		return err
	}
	return nil
}

func (d *DerivativeLogic) DerivativeSegments(ctx context.Context, request *modelsAPIv1.DerivativeSegmentsAPIRequest) error {
	userPreferencesDTO := getUserPreferencesDTO(request)
	err := d.userPreferencesRepository.FreshUpsert(ctx, userPreferencesDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("DerivativeSegments: error adding user segments")
		return err
	}

	return nil
}

func (d *DerivativeLogic) NRIDDPIDerivativeSegments(ctx context.Context, request *modelsAPIv1.NRIDDPIDerivativeSegmentsAPIRequest) error {
	userPreferencesDTO := getNRIUserPreferencesDTO(request)
	depositoryDTO := getNRIDDPIDTO(request)
	err := d.nriDepositoryRepository.FreshUpsert(ctx, depositoryDTO, userPreferencesDTO)
	if err != nil {
		return err
	}
	return nil
}

func (d *DerivativeLogic) GetFilePath(appNumber, fileExtension string) string {
	if d.commonOnboardingBusinessLogic.IsOnboardingAppNumber(appNumber) {
		return fmt.Sprintf("%s/%s/%s%s%d%s", appNumber, constants.Derivatives, constants.DerivativesProof, "-", time.Now().Unix(), fileExtension)
	}
	return fmt.Sprintf("%s/%s/%s%s", appNumber, constants.Derivatives, constants.DerivativesProof, fileExtension)
}

func (d *DerivativeLogic) GetDerivativeFileOCRData(ctx context.Context, fileType string, file *multipart.FileHeader) (map[string]any, error) {
	switch fileType {
	case constants.ITR, constants.SalarySlip, constants.FORM16, constants.BankStatementDoc, constants.DematHoldingStatement:
		ocr, err := d.aiNxtExternalProvider.FetchDerivativeOCR(ctx, file)
		if err != nil {
			return map[string]any{}, err
		}
		return ocr, nil
	default:
		return map[string]any{}, nil
	}
}

func (d *DerivativeLogic) ValidateDerivativeFileOCR(ctx context.Context, fileType string, file *multipart.FileHeader) (map[string]any, error) {
	ocrData, err := d.GetDerivativeFileOCRData(ctx, fileType, file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: error doing ocr of derivatives file")
		return ocrData, constants.ErrUploadingFile.WithDetails([]string{constants.Derivatives, err.Error()})
	}

	// ocr validations
	documentType, _ := ocrData["documentType"].(float64)
	documentStatus, _ := ocrData["status"].(float64)
	statusErrorMessage, _ := ocrData["statuserrorMsg"].(string)
	isLatestErrorMessage, _ := ocrData["islatesterrorMsg"].(string)
	isEditedErrorMsg, _ := ocrData["editederrorMsg"].(string)
	isDocumentForged, _ := ocrData["isEdited"].(bool)
	isDocumentLatest, _ := ocrData["isLatest"].(bool)

	// first check for forged
	if documentType > 0 && isDocumentForged {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: edited information of ocr derivative proof")
		return ocrData, constants.ErrEditedDerivativeProof.WithMessage(isEditedErrorMsg)
	}

	// then check for all fields and same document type
	if documentType == 0 || (documentType > 0 && documentStatus > 1) || documentType != constants.AINXTDocumentTypeMap[fileType] {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: invalid ocr derivative proof")

		isValidDocumentErrorMsg := "Please retry to upload with a valid document."
		if statusErrorMessage != constants.Empty && documentStatus > 1 {
			isValidDocumentErrorMsg = statusErrorMessage
		}

		return ocrData, constants.ErrInvalidDerivativeProof.WithMessage(isValidDocumentErrorMsg)
	}

	// check for latest
	if documentType > 0 && documentStatus == 1 && !isDocumentLatest {
		log.Error(ctx).Stack().Err(err).Msg("UploadDerivativeProof: invalid information of ocr derivative proof")
		return ocrData, constants.ErrInvalidDerivativeProof.WithMessage(isLatestErrorMessage)
	}

	return ocrData, nil
}
