package derivative

import (
	"bytes"
	"context"
	"fmt"
	"io"

	goutils "github.com/angel-one/go-utils"

	"github.com/angel-one/kyc-user-journey/aws/s3"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils/pdf"
)

func getDerivativeFileDataForS3(ctx context.Context, req *modelsAPIv1.DerivativeUploadRequest, fileExtension string) ([]byte, error) {
	file, err := req.Data.File.Open()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getDerivativeFileDataForS3: error opening file")
		return nil, constants.ErrOpeningFile.WithDetails(err.Error())
	}
	fileData, err := io.ReadAll(file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getDerivativeFileDataForS3: error reading file")
		return nil, constants.ErrReadingFile.WithDetails(err.Error())
	}

	// check and unlock pdf using password.
	if fileExtension == constants.PDFExtension {
		isPasswordProtected, passErr := pdf.IsPasswordProtectedPDF(fileData)
		if passErr != nil {
			log.Error(ctx).Stack().Err(passErr).Msg("getDerivativeFileDataForS3: error checking if password protected")
			// Uploading file to S3.
			filePath := fmt.Sprintf("TempDerivatives/%s/%s%s", req.AppNumber, constants.DerivativesProof, fileExtension)
			s3err := s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
			if s3err != nil {
				log.Error(ctx).Stack().Err(err).Msg("getDerivativeFileDataForS3: error uploading file to s3")
			}
			return nil, passErr
		}
		if isPasswordProtected {
			if req.Data.Password == constants.Empty {
				log.Warn(ctx).Stack().Msg("getDerivativeFileDataForS3: empty password")
				return nil, constants.ErrMissingFilePassword.Value()
			}
			// decrypt file
			unlockedFile, decryptErr := pdf.DecryptPDFData(fileData, req.Data.Password)
			if decryptErr != nil {
				log.Error(ctx).Stack().Err(decryptErr).Msg("getDerivativeFileDataForS3: error decrypting pdf file")
				return nil, decryptErr
			}
			// use decrypted file
			fileData = unlockedFile
		}
	}
	return fileData, nil
}

func getDerivativesDTO(req *modelsAPIv1.DerivativeUploadRequest, filePath string) *business.Derivatives {
	return &business.Derivatives{
		AppNumber:           req.AppNumber,
		Type:                req.Data.Type,
		Path:                filePath,
		ActivationStatus:    constants.DerivativesStatusPending,
		CreateSource:        req.Source,
		LastUpdateSource:    req.Source,
		IsPasswordProtected: false,
	}
}

func getClientOptOutDTO(req *modelsAPIv1.DerivativeSkipRequest, flow string) *business.ClientOptOut {
	return &business.ClientOptOut{
		AppNumber:        req.AppNumber,
		Flow:             flow,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
}

func getUserPreferencesDTO(request *modelsAPIv1.DerivativeSegmentsAPIRequest) *business.UserPreferences {
	segments := business.UserOptedSegments{
		IsFnO:       request.Data.IsFnO,
		IsCurrency:  request.Data.IsCurrency,
		IsCommodity: request.Data.IsCommodity,
	}

	return &business.UserPreferences{
		AppNumber:              request.AppNumber,
		CreateSource:           request.Source,
		LastUpdateSource:       request.Source,
		Segments:               segments,
		IsSegmentsAcknowledged: true,
	}
}

func getNRIUserPreferencesDTO(request *modelsAPIv1.NRIDDPIDerivativeSegmentsAPIRequest) *business.NRIUserPreferences {
	segments := business.NRIUserOptedSegments{
		IsCash: request.Data.IsCash,
	}

	return &business.NRIUserPreferences{
		AppNumber:              request.AppNumber,
		CreateSource:           request.Source,
		LastUpdateSource:       request.Source,
		Segments:               segments,
		IsSegmentsAcknowledged: request.Data.IsCash,
		DDPIOptIn:              request.Data.DDPIOptIn,
		SettlementFrequency:    request.Data.SettlementFrequency,
	}
}

func getNRIDDPIDTO(request *modelsAPIv1.NRIDDPIDerivativeSegmentsAPIRequest) *business.Depository {
	return &business.Depository{
		AppNumber:        request.AppNumber,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		DDPIOptIn:        request.Data.DDPIOptIn,
	}
}

func getSegmentObjectWithAllSegmentsSet() business.UserOptedSegments {
	return business.UserOptedSegments{
		IsFnO:       true,
		IsCurrency:  true,
		IsCommodity: true,
	}
}

func getUserPreferencesDTOWithSegments(request *modelsAPIv1.DerivativeUploadRequest) *business.UserPreferences {
	return &business.UserPreferences{
		AppNumber:        request.AppNumber,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		Segments:         getSegmentObjectWithAllSegmentsSet(),
	}
}

func getOcrDTOFromDerivativeOCRResponse(appNumber string, ocrData map[string]any, errorCode error) (*business.OCR, error) {
	ocrDTO := map[string]any{
		"status":         constants.Success,
		"errorCode":      constants.Empty,
		"documentName":   ocrData["documentName"],
		"documentType":   ocrData["documentType"],
		"isEdited":       ocrData["isEdited"],
		"isLatest":       ocrData["isLatest"],
		"editederrorMsg": ocrData["editederrorMsg"],
		"editedReason":   ocrData["editedReason"],
		"statuserrorMsg": ocrData["statuserrorMsg"],
		"documentStatus": ocrData["status"],
	}
	if errorCode != nil {
		ocrDTO["status"] = constants.FailureStatus
		ocrDTO["errorCode"] = errorCode.Error()
	}
	ocrByteData, err := goutils.MarshalJSON(ocrDTO)
	if err != nil {
		return nil, err
	}
	return &business.OCR{
		AppNumber: appNumber,
		Type:      constants.DerivativeSourceOcr,
		Data:      string(ocrByteData),
	}, nil
}
