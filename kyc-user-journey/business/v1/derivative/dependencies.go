package derivative

import (
	"context"
	"mime/multipart"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type DerivativesRepository interface {
	Save(ctx context.Context, derivativesDTO *business.Derivatives) error
}

type ClientOptOutRepository interface {
	FreshUpsert(ctx context.Context, clientOptOutDTO *business.ClientOptOut) error
}

type DerivativesCamsAndClientOptOutRepository interface {
	DeleteAllDerivativesData(ctx context.Context, appNumber, source string, shoulDClearSegments bool) error
}

type UserPreferencesRepository interface {
	FreshUpsert(ctx context.Context, userPreferencesDTO *business.UserPreferences) error
}

type DerivativesAndUserPreferencesRepository interface {
	SaveDerivativesAndUpdateSegments(ctx context.Context, derivativesDTO *business.Derivatives, userPreferencesDTO *business.UserPreferences) error
}

type NRIDepositoryRepository interface {
	FreshUpsert(ctx context.Context, depositoryDTO *business.Depository, userPreferencesDTO *business.NRIUserPreferences) error
}

type CommonOnboardingBusinessLogic interface {
	IsOnboardingAppNumber(appNumber string) bool
}

type AiNxtExternalProvider interface {
	FetchDerivativeOCR(ctx context.Context, file *multipart.FileHeader) (map[string]any, error)
}

type OCRRepository interface {
	Save(ctx context.Context, octDTO *business.OCR) error
}

type DerivativeLogic struct {
	derivativesRepository                    DerivativesRepository
	clientOptOutRepository                   ClientOptOutRepository
	derivativesCamsAndClientOptOutRepository DerivativesCamsAndClientOptOutRepository
	userPreferencesRepository                UserPreferencesRepository
	derivativesAndUserPreferencesRepository  DerivativesAndUserPreferencesRepository
	nriDepositoryRepository                  NRIDepositoryRepository
	commonOnboardingBusinessLogic            CommonOnboardingBusinessLogic
	aiNxtExternalProvider                    AiNxtExternalProvider
	ocrRepository                            OCRRepository
}

func NewDerivativeBusinessLogic(derivativesRepository DerivativesRepository, clientOptOutRepository ClientOptOutRepository,
	derivativesCamsAndClientOptOutRepository DerivativesCamsAndClientOptOutRepository, userPreferencesDepository UserPreferencesRepository, derivativesAndUserPreferencesRepository DerivativesAndUserPreferencesRepository,
	nriDepositoryRepository NRIDepositoryRepository, commonOnboardingBusinessLogic CommonOnboardingBusinessLogic,
	aiNxtExternalProvider AiNxtExternalProvider, ocrRepository OCRRepository) *DerivativeLogic {
	return &DerivativeLogic{
		derivativesRepository:                    derivativesRepository,
		clientOptOutRepository:                   clientOptOutRepository,
		derivativesCamsAndClientOptOutRepository: derivativesCamsAndClientOptOutRepository,
		userPreferencesRepository:                userPreferencesDepository,
		derivativesAndUserPreferencesRepository:  derivativesAndUserPreferencesRepository,
		commonOnboardingBusinessLogic:            commonOnboardingBusinessLogic,
		nriDepositoryRepository:                  nriDepositoryRepository,
		aiNxtExternalProvider:                    aiNxtExternalProvider,
		ocrRepository:                            ocrRepository,
	}
}
