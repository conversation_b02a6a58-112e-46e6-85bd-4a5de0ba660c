package additionaldocuments

import (
	"bytes"
	"context"
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils/pdf"
)

func checkIfFileIsPasswordProtected(ctx context.Context, req *api.UploadAdditionalDocumentRequest, fileExtension string, fileData []byte) error {
	// checking if file is password protected.
	if fileExtension == constants.PDFExtension {
		isPasswordProtected, passErr := pdf.IsPasswordProtectedPDF(fileData)
		if passErr != nil {
			log.Error(ctx).Stack().Err(passErr).Msg("UploadAdditionalDocument: error checking if password protected")
			// Uploading file to S3 for debugging purpose.
			filePath := fmt.Sprintf("TempDocument/%s/%s%s", req.AppNumber, req.Data.Type, fileExtension)
			s3err := s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
			if s3err != nil {
				log.Error(ctx).Stack().Err(s3err).Msg("UploadAdditionalDocument: error uploading file to s3")
			}
			return passErr
		}
		if isPasswordProtected {
			return constants.ErrFilePasswordProtected.Value()
		}
	}
	return nil
}

func getDocumentDTO(req *api.UploadAdditionalDocumentRequest, path string) *business.Document {
	return &business.Document{
		AppNumber:        req.AppNumber,
		Type:             req.Data.Type,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
		Path:             path,
	}
}
