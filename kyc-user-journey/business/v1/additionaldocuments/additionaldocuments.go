package additionaldocuments

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (a *AdditionalDocumentsBusinessLogic) UploadAdditionalDocument(ctx context.Context, req *api.UploadAdditionalDocumentRequest) (*api.UploadAdditionalDocumentResponseData, error) {
	fileExtension := utils.GetMultipartFileExtension(req.Data.File)
	file, err := req.Data.File.Open()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadAdditionalDocument: error opening file")
		return nil, constants.ErrOpeningFile.Value()
	}
	fileData, err := io.ReadAll(file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadAdditionalDocument: error reading file")
		return nil, constants.ErrReadingFile.Value()
	}
	err = checkIfFileIsPasswordProtected(ctx, req, fileExtension, fileData)
	if err != nil {
		log.Error(ctx).Err(err).Msg("UploadAdditionalDocument: error checking if the file is password protected")
		return nil, err
	}

	filePath := fmt.Sprintf("%s/%s/%s%s%d%s", req.AppNumber, constants.Document, req.Data.Type, "-", time.Now().Unix(), fileExtension)
	s3err := s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
	if s3err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadAdditionalDocument: error uploading file to s3")
		return nil, s3err
	}

	documentDTO := getDocumentDTO(req, filePath)
	err = a.documentRepository.Save(ctx, documentDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("UploadAdditionalDocument: error inserting data into document table")
		return nil, err
	}

	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	previewURL, err := s3.GetS3Client().GetSignedURL(ctx, documentDTO.Path, time.Duration(signedURLExpiry)*time.Second)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("UploadAdditionalDocument: error getting signed URL of additional document " + documentDTO.Type)
		return nil, constants.ErrGettingSignedURL.WithDetails(err).WithMessage("UploadAdditionalDocument: error getting signed url")
	}

	return &api.UploadAdditionalDocumentResponseData{
		PreviewURL: previewURL,
	}, nil
}

func (a *AdditionalDocumentsBusinessLogic) PreviewAdditionalDocument(ctx context.Context, req *api.AdditionalDocumentPreviewRequest) (*api.AdditionalDocumentPreviewResponseData, error) {
	doc, err := a.documentRepository.GetDocument(ctx, req.AppNumber, req.DocumentType)
	if err != nil {
		log.Error(ctx).Err(err).Msg("PreviewAdditionalDocument: error inserting data into document table")
		return nil, err
	}

	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	previewURL, err := s3.GetS3Client().GetSignedURL(ctx, doc.Path, time.Duration(signedURLExpiry)*time.Second)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("PreviewAdditionalDocument: error getting signed URL of additional document " + doc.Type)
		return nil, constants.ErrGettingSignedURL.WithDetails(err).WithMessage("PreviewAdditionalDocument: error getting signed url")
	}

	return &api.AdditionalDocumentPreviewResponseData{
		PreviewURL: previewURL,
	}, nil
}
