package additionaldocuments

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type DocumentRepository interface {
	Save(ctx context.Context, documentDTO *business.Document) error
	GetDocument(ctx context.Context, appNumber, docType string) (business.Document, error)
}

type AdditionalDocumentsBusinessLogic struct {
	documentRepository DocumentRepository
}

func NewAdditionalDocumentsBusinessLogic(documentRepository DocumentRepository) *AdditionalDocumentsBusinessLogic {
	return &AdditionalDocumentsBusinessLogic{
		documentRepository: documentRepository,
	}
}
