package bank

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
)

type BankSearchRepository interface {
	GetBankDataByIfsc(ctx context.Context, ifsc string, from, size int) ([]business.BankBranchMasterSearchData, error)
	IsRegisteredIFSC(ctx context.Context, ifsc string) (bool, error)
}

type Pg2ExternalProvider interface {
	CheckNetBankingSupport(ctx context.Context, request *modelsExternal.NetBankingSupportRequest) (*modelsExternal.NetBankingSupportExternalResponse, error)
}

type CommonBankBusinessLogic interface {
	CheckIfBankAccountExists(ctx context.Context, accountNumber, appNumber, clientCode string) (bool, bool, error)
	CheckIfBankAccountExistsWithoutJointAccount(ctx context.Context, accountNumber, appNumber, clientCode string) (bool, error)
	GetIMPSBeneficiaryName(ctx context.Context, accountNumber, ifsc string) (string, error)
	IsCamsEligible(bankCode string) bool
}

type BankRepository interface {
	GetCountOfBankAccountLinkedApps(ctx context.Context, accountNumberList []string, appNumber string) (int64, error)
	FreshUpsert(ctx context.Context, bankDTO *business.Bank) error
	UpdateModifiedOCRInfo(ctx context.Context, bankDTO *business.Bank) error
	UpdateJointAccountConfirmation(ctx context.Context, appNumber string, isJointBankAccountConfirmed bool) error
	GetBankDataWithAppNumber(ctx context.Context, appNumber string) (*business.Bank, error)
}

type RPDRepository interface {
	GetRPDDataByAppNumber(ctx context.Context, appNumber string) (business.RPD, error)
}

type AWSLambdaExternalProvider interface {
	OCRBankCheque(ctx context.Context, ocrBankChequeRequest modelsExternal.OCRBankChequeRequest) (*modelsExternal.OCRBankChequeResponse, error)
}

type OCRRepository interface {
	Save(ctx context.Context, octDTO *business.OCR) error
	GetOCRData(ctx context.Context, appNumber, ocrType string) (*business.OCR, error)
}

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type NRIBankRepository interface {
	FreshUpsert(ctx context.Context, bankDTO *business.Bank, nriInvestmentBankDTO *business.NRIInvestmentBank, nriMetadataDTO *business.NRIMetaData) error
}

type NetBankingSupportBusinessLogic struct {
	bankSearchRepository BankSearchRepository
	pg2ExternalProvider  Pg2ExternalProvider
}
type PrismSQSBusinessLogic interface {
	SendMessageToQueue(ctx context.Context, prismEventMessage *business.PrismEventMessage)
}
type CommonPrismBusinessLogic interface {
	GetPrismErrorEventData(appNumber, eventName, subEventName, activityID, remark string) *business.PrismEventMessage
	GetPrismSuccessEventData(appNumber, eventName, subEventName, activityID string) *business.PrismEventMessage
}

func NewNetBankingSupportBusinessLogic(bankSearchRepository BankSearchRepository, pg2ExternalProvider Pg2ExternalProvider) *NetBankingSupportBusinessLogic {
	return &NetBankingSupportBusinessLogic{
		bankSearchRepository: bankSearchRepository,
		pg2ExternalProvider:  pg2ExternalProvider,
	}
}

type BankBusinessLogic struct {
	bankRepository            BankRepository
	commonBankBusinessLogic   CommonBankBusinessLogic
	lsqBusinessLogic          LSQBusinessLogic
	bankSearchRepository      BankSearchRepository
	awsLambdaExternalProvider AWSLambdaExternalProvider
	rpdRepository             RPDRepository
	ocrRepository             OCRRepository
	prismBusineessLogic       PrismSQSBusinessLogic
	commonPrismBusinessLogic  CommonPrismBusinessLogic
}

func NewBankBusinessLogic(bankRepository BankRepository, commonBankBusinessLogic CommonBankBusinessLogic, lsqBusinessLogic LSQBusinessLogic, bankSearchRepository BankSearchRepository,
	awsLambdaExternalProvider AWSLambdaExternalProvider, rpdRepository RPDRepository, ocrRepository OCRRepository, prismBusineessLogic PrismSQSBusinessLogic, commonPrismBusinessLogic CommonPrismBusinessLogic) *BankBusinessLogic {
	return &BankBusinessLogic{
		bankRepository:            bankRepository,
		commonBankBusinessLogic:   commonBankBusinessLogic,
		lsqBusinessLogic:          lsqBusinessLogic,
		bankSearchRepository:      bankSearchRepository,
		awsLambdaExternalProvider: awsLambdaExternalProvider,
		rpdRepository:             rpdRepository,
		ocrRepository:             ocrRepository,
		prismBusineessLogic:       prismBusineessLogic,
		commonPrismBusinessLogic:  commonPrismBusinessLogic,
	}
}

type NRIBankBusinessLogic struct {
	nriBankRepository       NRIBankRepository
	commonBankBusinessLogic CommonBankBusinessLogic
}

func NewNRIBankBusinessLogic(nriBankRepository NRIBankRepository, commonBankBusinessLogic CommonBankBusinessLogic) *NRIBankBusinessLogic {
	return &NRIBankBusinessLogic{
		nriBankRepository:       nriBankRepository,
		commonBankBusinessLogic: commonBankBusinessLogic,
	}
}
