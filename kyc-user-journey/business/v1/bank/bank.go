package bank

import (
	"context"
	"encoding/json"
	"slices"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
)

func (b *BankBusinessLogic) BankIMPS(ctx context.Context, request *modelsV1.BankIMPSAPIRequest) (*modelsV1.BankIMPSAPIResponse, error) {
	isIMPSSuccess := true
	seekConsent := false
	shouldBlock := false
	var bankDuplicateCheckErr error
	err := b.validateBankInfo(ctx, request.Data.IFSCCode)
	if err != nil {
		return nil, err
	}

	if config.Feature().Enable.JointBankAccount || slices.Contains(config.Feature().Whitelist.JointBankAccount, request.Mobile) {
		shouldBlock, seekConsent, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExists(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	} else {
		shouldBlock, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExistsWithoutJointAccount(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	}

	if bankDuplicateCheckErr != nil {
		log.Error(ctx).Err(bankDuplicateCheckErr).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Msg("Bank: error checking if bank account is duplicate")
		return nil, bankDuplicateCheckErr
	}

	if shouldBlock {
		log.Warn(ctx).Err(err).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Msg("Bank: bank account is already used")
		return nil, constants.ErrDuplicateBankAccount.WithDetails("Bank Account is already used")
	}

	beneficiaryName, err := b.commonBankBusinessLogic.GetIMPSBeneficiaryName(ctx, request.Data.AccountNumber, request.Data.IFSCCode)
	if err != nil || beneficiaryName == constants.Empty {
		isIMPSSuccess = false
		log.Error(ctx).Err(err).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Str(constants.LogIFSCKey, request.Data.IFSCCode).
			Msg("imps failed")
		errStr := constants.BeneficiaryNameIsEmpty
		if err != nil {
			errStr = err.Error()
		}
		go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.IMPS, errStr))
	}
	if isIMPSSuccess {
		log.Info(ctx).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Str(constants.LogIFSCKey, request.Data.IFSCCode).Msg("imps succeeded")
		bankDTO := getBankDTOForIMPS(request, beneficiaryName, seekConsent)
		err = b.bankRepository.FreshUpsert(ctx, bankDTO)
		if err != nil {
			return nil, err
		}
		b.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.BankDetailsCompletedLSQEvent)
		go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.IMPS))
	}

	return &modelsV1.BankIMPSAPIResponse{
		BeneficiaryName: beneficiaryName,
		IsConsentNeeded: seekConsent,
		AccountNumber:   request.Data.AccountNumber,
		IFSC:            request.Data.IFSCCode,
		IsImpsSuccess:   isIMPSSuccess,
		IsCamsEligible:  b.commonBankBusinessLogic.IsCamsEligible(request.Data.IFSCCode[0:4]),
	}, nil
}

func (b *BankBusinessLogic) BankOCRConfirm(ctx context.Context, request *modelsV1.BankOCRConfirmAPIRequest) (*modelsV1.BankOCRConfirmAPIResponse, error) {
	ocrDTO, err := b.ocrRepository.GetOCRData(ctx, request.AppNumber, constants.BankChequeOCRType)
	if err != nil {
		return nil, err
	}

	if ocrDTO.AppNumber == constants.Empty {
		log.Error(ctx).Err(err).Msg("OCR is not performed to confirm.")
		return nil, constants.ErrBankOCRNotPerformed.Value()
	}

	var bankOCRData business.BankOCRData
	err = json.Unmarshal([]byte(ocrDTO.Data), &bankOCRData)
	if err != nil {
		return nil, err
	}

	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	bankChequeURL, err := s3.GetS3Client().GetSignedURL(ctx, bankOCRData.Path, time.Duration(signedURLExpiry)*time.Second)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("OCRBank: error getting signed URL of bank cheque")
		return nil, constants.ErrGettingSignedURL.WithDetails(err).WithMessage("OCRBank: error getting signed url")
	}

	err = b.validateBankInfo(ctx, request.Data.IFSCCode)
	if err != nil {
		return nil, err
	}

	seekConsent := false
	shouldBlock := false
	var bankDuplicateCheckErr error
	if config.Feature().Enable.JointBankAccount || slices.Contains(config.Feature().Whitelist.JointBankAccount, request.Mobile) {
		shouldBlock, seekConsent, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExists(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	} else {
		shouldBlock, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExistsWithoutJointAccount(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	}

	if bankDuplicateCheckErr != nil {
		log.Warn(ctx).Err(err).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Msg("Bank: error checking if bank account is duplicate")
		return nil, err
	}

	if shouldBlock {
		log.Warn(ctx).Err(err).Msg("Bank: bank account is already used")
		return nil, constants.ErrDuplicateBankAccount.WithDetails("Bank Account is already used")
	}

	bankDTO := getBankDTOForOCR(request, bankOCRData.Path, seekConsent, bankOCRData)
	err = b.bankRepository.FreshUpsert(ctx, bankDTO)
	if err != nil {
		go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.BankSourceOcr, err.Error()))
		return nil, err
	}
	go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.BankSourceOcr))
	return &modelsV1.BankOCRConfirmAPIResponse{
		AccountNumber:   request.Data.AccountNumber,
		IFSC:            request.Data.IFSCCode,
		BankChequeURL:   bankChequeURL,
		IsConsentNeeded: seekConsent,
		IsCamsEligible:  b.commonBankBusinessLogic.IsCamsEligible(request.Data.IFSCCode[0:4]),
	}, nil
}

func (b *BankBusinessLogic) BankRPD(ctx context.Context, request *modelsV1.BankRPDAPIRequest) (*modelsV1.BankRPDAPIResponse, error) {
	rpdDTO, err := b.rpdRepository.GetRPDDataByAppNumber(ctx, request.AppNumber)
	if err != nil {
		return nil, err
	}

	var rpdResponseData *modelsExternal.RPDStatusResponseData
	err = json.Unmarshal([]byte(rpdDTO.Status), &rpdResponseData)
	if err != nil {
		return nil, err
	}

	err = validateWithRPDData(ctx, request, rpdResponseData)
	if err != nil {
		return nil, err
	}

	err = b.validateBankInfo(ctx, request.Data.IFSCCode)
	if err != nil {
		return nil, err
	}

	seekConsent := false
	shouldBlock := false
	var bankDuplicateCheckErr error
	if config.Feature().Enable.JointBankAccount || slices.Contains(config.Feature().Whitelist.JointBankAccount, request.Mobile) {
		shouldBlock, seekConsent, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExists(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	} else {
		shouldBlock, bankDuplicateCheckErr = b.commonBankBusinessLogic.CheckIfBankAccountExistsWithoutJointAccount(ctx, request.Data.AccountNumber, request.AppNumber, request.AppNumber)
	}

	if bankDuplicateCheckErr != nil {
		log.Warn(ctx).Err(err).Str(constants.LogBankAccountNumberKey, request.Data.AccountNumber).Msg("Bank: error checking if bank account is duplicate")
		return nil, err
	}

	if shouldBlock {
		log.Warn(ctx).Err(err).Msg("Bank: bank account is already used")
		return nil, constants.ErrDuplicateBankAccount.WithDetails("Bank Account is already used")
	}

	bankDTO := getBankDTOForRPD(rpdResponseData, seekConsent)
	err = b.bankRepository.FreshUpsert(ctx, bankDTO)
	if err != nil {
		return nil, err
	}
	go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.RPD))
	return &modelsV1.BankRPDAPIResponse{
		IsConsentNeeded: seekConsent,
		AccountNumber:   bankDTO.AccountNumber,
		IFSC:            bankDTO.IfscCode,
		IsCamsEligible:  b.commonBankBusinessLogic.IsCamsEligible(request.Data.IFSCCode[0:4]),
	}, nil
}

func (b *BankBusinessLogic) JointBankAccountConfirm(ctx context.Context, request *modelsV1.JointBankAccountConfirmationAPIRequest) error {
	err := b.bankRepository.UpdateJointAccountConfirmation(ctx, request.AppNumber, request.Data.IsJointAccount)
	if err != nil {
		return err
	}

	return nil
}

func (b *BankBusinessLogic) validateBankInfo(ctx context.Context, ifscCode string) error {
	isRegisteredIFSC, err := b.bankSearchRepository.IsRegisteredIFSC(ctx, ifscCode)
	if err != nil {
		return err
	}

	if !isRegisteredIFSC {
		log.Error(ctx).Err(err).Str("ifsc", ifscCode).Msg("This ifsc code is not registered.")
		return constants.ErrUnRegisteredIFSC.Value()
	}

	return nil
}
