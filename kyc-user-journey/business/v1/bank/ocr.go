package bank

import (
	"context"
	"fmt"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (b *BankBusinessLogic) BankOCR(ctx context.Context, request *modelsV1.BankOCRAPIRequest) (*modelsV1.BankOCRAPIResponse, error) {
	file := request.Data.BankChequeFile
	fileExtension := utils.GetMultipartFileExtension(file)

	// Path of file to be uploaded.
	path := fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.BankCheque, constants.BankCheque, "-", time.Now().Unix(), fileExtension)
	f, err := file.Open()
	if err != nil {
		return nil, constants.ErrOpeningFile.WithDetails(err.Error())
	}
	// Uploading file to S3.
	err = s3.GetS3Client().Upload(ctx, path, f)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("OCRBank: error uploading bank cheque to s3")
		return nil, constants.ErrUploadingFile.WithDetails([]string{constants.BankCheque, err.Error()})
	}
	log.Error(ctx).Str("filePath", path).Msg("Succesfully uploaded bank cheque to s3")

	signedURLExpiry := config.S3().SignURLExpiryInSeconds
	bankChequeURL, err := s3.GetS3Client().GetSignedURL(ctx, path, time.Duration(signedURLExpiry)*time.Second)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("OCRBank: error getting signed URL of bank cheque")
		return nil, constants.ErrGettingSignedURL.WithDetails(err).WithMessage("OCRBank: error getting signed url")
	}

	ocrBankChequeResponse, err := b.awsLambdaExternalProvider.OCRBankCheque(ctx, modelsExternal.OCRBankChequeRequest{URL: bankChequeURL})
	if err != nil {
		log.Warn(ctx).Err(err).Msg("OCRBank: error performing OCR of the bank cheque")
		go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.RPD, err.Error()))
		return nil, constants.ErrReadingBankCheque.Value()
	}
	var accountNumber, ifsc string
	if ocrBankChequeResponse != nil {
		accountNumber = ocrBankChequeResponse.AccountNo
		ifsc = ocrBankChequeResponse.IfscCode
	}

	ocrDTO, err := getOCRDTOForBank(request, accountNumber, ifsc, path)
	if err != nil {
		go b.prismBusineessLogic.SendMessageToQueue(ctx, b.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismBankCompletionEvent, constants.PrismBankCompletionEvent, constants.RPD, err.Error()))
		return nil, err
	}

	err = b.ocrRepository.Save(ctx, ocrDTO)
	if err != nil {
		return nil, err
	}

	return &modelsV1.BankOCRAPIResponse{
		AccountNumber: accountNumber,
		IFSC:          ifsc}, nil
}
