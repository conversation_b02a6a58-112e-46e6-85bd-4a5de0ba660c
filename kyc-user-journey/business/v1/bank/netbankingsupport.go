package bank

import (
	"context"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/kyc-user-journey/constants"
	modelsv1 "github.com/angel-one/kyc-user-journey/models/externals"
)

func (n *NetBankingSupportBusinessLogic) NetBankingSupport(ctx context.Context, request *modelsAPIv1.NetBankingSupportAPIRequest) (*modelsAPIv1.NetBankingSupportAPIResponse, error) {
	bankIfscData, err := n.bankSearchRepository.GetBankDataByIfsc(ctx, request.Data.BankIFSC, 0, 1)
	if err != nil {
		return nil, err
	}
	if len(bankIfscData) == 0 {
		return nil, constants.ErrGettingBankFromIFSC.WithDetails("unable to fetch bank details for the provided ifsc")
	}
	nbSupportResponse, err := n.pg2ExternalProvider.CheckNetBankingSupport(ctx, &modelsv1.NetBankingSupportRequest{
		BankIFSC:   request.Data.BankIFSC,
		BankName:   bankIfscData[0].BankName,
		ClientCode: request.ClientCode})
	if err != nil {
		return nil, err
	}
	return &modelsAPIv1.NetBankingSupportAPIResponse{IsNBSupported: nbSupportResponse.Data.NBSupported}, nil
}
