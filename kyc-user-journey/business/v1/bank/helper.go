package bank

import (
	"context"
	"time"

	"github.com/angel-one/kyc-user-journey/config/models"

	"github.com/angel-one/kyc-user-journey/config"

	"github.com/angel-one/go-pii-utils/log"
	goutils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func getBankDTOForIMPS(request *modelsV1.BankIMPSAPIRequest, beneficiaryName string, isJointAccountConfirmationNeeded bool) *business.Bank {
	return &business.Bank{
		AppNumber:                        request.AppNumber,
		AccountNumber:                    request.Data.AccountNumber,
		IfscCode:                         request.Data.IFSCCode,
		IMPS:                             true,
		FullName:                         beneficiaryName,
		CreateSource:                     request.Source,
		LastUpdateSource:                 request.Source,
		ImpsTS:                           utils.GetCurrentTime(),
		Source:                           constants.IMPS,
		DoesBankAccountNotExist:          true,
		JointBankAccountConfirmed:        false,
		IsJointAccountConfirmationExempt: !isJointAccountConfirmationNeeded,
	}
}

func getBankDTOForOCR(request *modelsV1.BankOCRConfirmAPIRequest, path string, isJointAccountConfirmationNeeded bool, bankOCRData business.BankOCRData) *business.Bank {
	var ocrData string
	if bankOCRData.AccountNumber != constants.Empty || bankOCRData.IFSCCode != constants.Empty {
		ocrData = bankOCRData.AccountNumber + constants.Pipe + bankOCRData.IFSCCode
	}

	return &business.Bank{
		AppNumber:                        request.AppNumber,
		AccountNumber:                    request.Data.AccountNumber,
		IfscCode:                         request.Data.IFSCCode,
		ImagePath:                        path,
		CreateSource:                     request.Source,
		LastUpdateSource:                 request.Source,
		Source:                           constants.OCR,
		OCRData:                          ocrData,
		DoesBankAccountNotExist:          true,
		JointBankAccountConfirmed:        false,
		IsJointAccountConfirmationExempt: !isJointAccountConfirmationNeeded,
	}
}

func getBankDTOForRPD(rpdResponseData *modelsExternal.RPDStatusResponseData, isJointAccountConfirmationNeeded bool) *business.Bank {
	return &business.Bank{
		AppNumber:                        rpdResponseData.AppNumber,
		AccountNumber:                    rpdResponseData.BankAccountNumber,
		IfscCode:                         rpdResponseData.BankIFSCCode,
		IMPS:                             true,
		FullName:                         rpdResponseData.BankAccountName,
		CreateSource:                     rpdResponseData.RequestSource,
		LastUpdateSource:                 rpdResponseData.RequestSource,
		ImpsTS:                           getTimeFromEpochMilliseconds(rpdResponseData.CompletedOn),
		Source:                           constants.BankSourceRPD,
		MICR:                             rpdResponseData.MICR,
		VPA:                              rpdResponseData.VPA,
		DoesBankAccountNotExist:          true,
		JointBankAccountConfirmed:        false,
		IsJointAccountConfirmationExempt: !isJointAccountConfirmationNeeded,
	}
}

func getOCRDTOForBank(request *modelsV1.BankOCRAPIRequest, accountNumber, ifsc, path string) (*business.OCR, error) {
	bankOCRData := business.BankOCRData{
		AccountNumber: accountNumber,
		IFSCCode:      ifsc,
		Path:          path,
	}

	ocrData, err := goutils.MarshalJSON(bankOCRData)
	if err != nil {
		return nil, err
	}

	return &business.OCR{
		AppNumber: request.AppNumber,
		Type:      constants.BankChequeOCRType,
		Data:      string(ocrData),
	}, nil
}

func validateWithRPDData(ctx context.Context, request *modelsV1.BankRPDAPIRequest, rpdResponseData *modelsExternal.RPDStatusResponseData) error {
	if request.Data.AccountNumber != rpdResponseData.BankAccountNumber {
		log.Error(ctx).Str(constants.LogBankAccountNumberKey, rpdResponseData.BankAccountNumber).Str("requestAccountNumber", request.Data.AccountNumber).Msg("Account number in request didn't match with account number received from RPD")
		return constants.ErrAccountNumberMisMatch.Value()
	}

	if request.Data.IFSCCode != rpdResponseData.BankIFSCCode {
		log.Error(ctx).Str(constants.LogIFSCKey, rpdResponseData.BankIFSCCode).Str("requestIfsc", request.Data.IFSCCode).Msg("ifsc in request didn't match with ifsc received from RPD")
		return constants.ErrIFSCMisMatch.Value()
	}

	if request.Data.AccountHolderName != rpdResponseData.BankAccountName {
		log.Error(ctx).Str("accountHolderName", rpdResponseData.BankAccountName).Str("requestAccountHolderName", request.Data.AccountHolderName).Msg("account holder name in request didn't match with that of received from RPD")
		return constants.ErrAccountHolderNameMismatch.Value()
	}
	return nil
}

func getTimeFromEpochMilliseconds(epochMilliseconds int64) time.Time {
	seconds := epochMilliseconds / 1000
	nanoseconds := (epochMilliseconds % 1000) * int64(time.Millisecond)
	return time.Unix(seconds, nanoseconds)
}

// returns partner bank list base on nri bank type.
func getNRIBankListForBankTypeConfig(bankType string) []models.DefaultBankConfig {
	var bankSearchData []models.DefaultBankConfig
	if bankType == constants.NRIBankTypeNRE {
		bankSearchData = config.Application().NRIBank.NRE
	} else if bankType == constants.NRIBankTypeNRO {
		bankSearchData = config.Application().NRIBank.NRO
	}
	return bankSearchData
}

func getNRIBankDTOs(request *modelsV1.NRIBankAPIRequest) (bankDTO *business.Bank, nriInvestmentBankDTO *business.NRIInvestmentBank, nriMetadataDTO *business.NRIMetaData) {
	savingsBankDTO := business.Bank{
		AppNumber:               request.AppNumber,
		AccountNumber:           request.Data.SavingsAccount.AccountNumber,
		IfscCode:                request.Data.SavingsAccount.IFSCCode,
		DoesBankAccountNotExist: true,
		CreateSource:            request.Source,
		LastUpdateSource:        request.Source,
	}

	investmentBankDTO := business.NRIInvestmentBank{
		AppNumber:               request.AppNumber,
		AccountNumber:           request.Data.InvestmentAccount.AccountNumber,
		IfscCode:                request.Data.InvestmentAccount.IFSCCode,
		DoesBankAccountNotExist: true,
		CreateSource:            request.Source,
		LastUpdateSource:        request.Source,
	}
	// add rbi and account type in nri metadata
	metadataDTO := business.NRIMetaData{
		AppNumber:         request.AppNumber,
		AccountType:       request.Data.NRIBankType,
		RBIApprovalNumber: request.Data.RBIApprovalNumber,
		CreateSource:      request.Source,
		LastUpdateSource:  request.Source,
	}
	return &savingsBankDTO, &investmentBankDTO, &metadataDTO
}
