package bank

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"

	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"

	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
)

func (n *NRIBankBusinessLogic) GetNRIPartnerBank(_ context.Context, nriPartnerBankSearchRequest *modelsAPIv1.NRIPartnerBankSearchAPIRequest) (*modelsAPIv1.NRIPartnerBankSearchAPIResponse, error) {
	var bankSearchResponse []modelsAPIv1.NRIPartnerBankSearchAPIResponseData
	bankSearchData := getNRIBankListForBankTypeConfig(nriPartnerBankSearchRequest.BankType)
	err := objectmapper.GetMapperInstance().MapperSlice(&bankSearchData, &bankSearchResponse)
	if err != nil {
		return nil, constants.ErrMappingFailed.WithDetails(err)
	}
	return &modelsAPIv1.NRIPartnerBankSearchAPIResponse{Data: bankSearchResponse}, nil
}

func (n *NRIBankBusinessLogic) NRIBank(ctx context.Context, request *modelsAPIv1.NRIBankAPIRequest) (*modelsAPIv1.NRIBankAPIResponse, error) {
	// savings duplicate check
	shouldBlock, bankDuplicateCheckErr := n.commonBankBusinessLogic.CheckIfBankAccountExistsWithoutJointAccount(ctx, request.Data.SavingsAccount.AccountNumber, request.AppNumber, request.AppNumber)
	if bankDuplicateCheckErr != nil {
		log.Error(ctx).Err(bankDuplicateCheckErr).Str(constants.LogBankAccountNumberKey, request.Data.SavingsAccount.AccountNumber).Msg("NRIBank: error checking if bank account is duplicate")
		return nil, bankDuplicateCheckErr
	}

	if shouldBlock {
		log.Warn(ctx).Str(constants.LogBankAccountNumberKey, request.Data.SavingsAccount.AccountNumber).Msg("NRIBank: bank account is already used")
		return nil, constants.ErrDuplicateBankAccount.WithDetails("Bank Account is already used")
	}

	// investment duplicate check
	shouldBlock, bankDuplicateCheckErr = n.commonBankBusinessLogic.CheckIfBankAccountExistsWithoutJointAccount(ctx, request.Data.InvestmentAccount.AccountNumber, request.AppNumber, request.AppNumber)
	if bankDuplicateCheckErr != nil {
		log.Error(ctx).Err(bankDuplicateCheckErr).Str(constants.LogBankAccountNumberKey, request.Data.InvestmentAccount.AccountNumber).Msg("NRIBank: error checking if investment bank account is duplicate")
		return nil, bankDuplicateCheckErr
	}

	if shouldBlock {
		log.Warn(ctx).Str(constants.LogBankAccountNumberKey, request.Data.InvestmentAccount.AccountNumber).Msg("NRIBank: investment bank account is already used")
		return nil, constants.ErrDuplicateInvestmentBankAccount.WithDetails("Investment Bank Account is already used")
	}

	savingsBankDTO, investmentBankDTO, metadataDTO := getNRIBankDTOs(request)
	err := n.nriBankRepository.FreshUpsert(ctx, savingsBankDTO, investmentBankDTO, metadataDTO)
	if err != nil {
		return nil, err
	}

	return &modelsAPIv1.NRIBankAPIResponse{
		AppNumber:         savingsBankDTO.AppNumber,
		RBIApprovalNumber: metadataDTO.RBIApprovalNumber,
		NRIBankType:       metadataDTO.AccountType,
		SavingsAccount: modelsAPIv1.BankAccountDetails{
			AccountNumber: savingsBankDTO.AccountNumber,
			IFSCCode:      savingsBankDTO.IfscCode,
		},
		InvestmentAccount: modelsAPIv1.BankAccountDetails{
			AccountNumber: investmentBankDTO.AccountNumber,
			IFSCCode:      investmentBankDTO.IfscCode,
		},
	}, nil
}
