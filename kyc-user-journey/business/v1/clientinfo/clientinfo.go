package clientinfo

import (
	"context"
	"strconv"
	"strings"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
)

func (c *ClientInfoBusinessLogic) GetClientInfo(ctx context.Context, request *modelsAPIV1.ClientInfoRequest) (any, error) {
	if config.DataAPIClients().APIKeyMapper[request.Headers.APIKey] == constants.UserProfilingRetention {
		var userProfilingData modelsBusiness.UserProfiling
		clientInfoData, err := c.clientInfoRepository.FetchUserProfilingDataForAppNumber(ctx, request.AppNumber)
		if err != nil {
			return nil, err
		}

		err = objectmapper.GetMapperInstance().AutoMapper(clientInfoData, &userProfilingData)
		if err != nil {
			return nil, constants.ErrMappingFailed.WithDetails(err)
		}

		dob := clientInfoData.PanDOB
		if dob == constants.Empty {
			dob = clientInfoData.PoaDOB
		}

		age, err := utils.GetAgeFromDOB(config.Application().Validations.DOBFormat, dob)
		if err != nil {
			log.Error(ctx).Err(err).Str("dob", dob).Msg("Couldn't parse DOB")
		}

		if age != 0 {
			userProfilingData.Age = strconv.Itoa(age)
		}
		if userProfilingData.City == constants.Empty {
			userProfilingData.City = clientInfoData.PinCodeCity
		}
		userProfilingData.IsKRA = isKRA(clientInfoData.KRAStatus)
		if !userProfilingData.IsKRA {
			userProfilingData.KRAStatusDate = constants.Empty
		}
		if userProfilingData.KRAStatusDate != constants.Empty {
			dateSplit := strings.Split(userProfilingData.KRAStatusDate, constants.Space)
			userProfilingData.KRAStatusDate = dateSplit[0]
		}
		userProfilingData.IsFNO = isFno(clientInfoData.Segments)
		var clientInfoUserProfilingResponse modelsAPIV1.ClientInfoUserProfilingResponse
		err = objectmapper.GetMapperInstance().AutoMapper(&userProfilingData, &clientInfoUserProfilingResponse)
		if err != nil {
			return nil, constants.ErrMappingFailed.WithDetails(err)
		}

		return &clientInfoUserProfilingResponse, nil
	}
	return nil, nil
}
