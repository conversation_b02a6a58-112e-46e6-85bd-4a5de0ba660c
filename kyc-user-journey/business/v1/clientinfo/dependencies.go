package clientinfo

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type ClientInfoRepository interface {
	FetchUserProfilingDataForAppNumber(ctx context.Context, appNumber string) (*business.ClientInfo, error)
}

type ClientInfoBusinessLogic struct {
	clientInfoRepository ClientInfoRepository
}

func NewClientInfoBusinessLogic(clientInfoRepository ClientInfoRepository) *ClientInfoBusinessLogic {
	return &ClientInfoBusinessLogic{
		clientInfoRepository: clientInfoRepository,
	}
}
