package clientinfo

import (
	"slices"

	goUtils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
)

func isKRA(kraStatus string) bool {
	if slices.Contains(config.KRA().KRACompliantStatuses, kraStatus) || slices.Contains(config.KRA().IsKRAStatuses, kraStatus) {
		return true
	}

	return false
}

func isFno(segments string) bool {
	if segments == constants.Empty {
		return false
	}

	var segmentsDTO modelsBusiness.UserOptedSegments
	err := goUtils.UnmarshalJSON([]byte(segments), &segmentsDTO)
	if err != nil {
		return false
	}

	return segmentsDTO.IsFnO
}
