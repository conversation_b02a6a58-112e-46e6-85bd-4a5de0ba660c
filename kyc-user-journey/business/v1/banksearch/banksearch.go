package banksearch

import (
	"context"
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
)

func (d *BankSearchBusinessLogic) BankIfscSearch(ctx context.Context, bankIfscSearchRequest modelsAPIv1.BankIfscSearchAPIRequest) (*modelsAPIv1.BankIfscSearchAPIResponse, error) {
	bankIfscData, err := d.bankSearchRepository.GetBankDataByIfsc(ctx, bankIfscSearchRequest.Search, bankIfscSearchRequest.From, bankIfscSearchRequest.Size)
	if err != nil {
		log.Info(ctx).Msg(fmt.Sprintf("BankIfscSearch: No ifsc found with prefix %s", bankIfscSearchRequest.Search))
		return nil, err
	}

	var bankIfscResponse []modelsAPIv1.BankIfscSearchAPIResponseData
	err = objectmapper.GetMapperInstance().MapperSlice(&bankIfscData, &bankIfscResponse)
	if err != nil {
		return nil, constants.ErrMappingFailed.WithDetails(err)
	}

	return &modelsAPIv1.BankIfscSearchAPIResponse{Data: bankIfscResponse}, nil
}

func (d *BankSearchBusinessLogic) BankBranchSearch(ctx context.Context, bankBranchSearchRequest modelsAPIv1.BankBranchSearchAPIRequest) (*modelsAPIv1.BankBranchSearchAPIResponse, error) {
	var bankBranchResponse []modelsAPIv1.BankBranchSearchAPIResponseData
	if bankBranchSearchRequest.BankName == constants.Empty {
		return &modelsAPIv1.BankBranchSearchAPIResponse{Data: bankBranchResponse}, nil
	}
	bankBranchData, err := d.bankSearchRepository.GetBankBranchSearchData(ctx, bankBranchSearchRequest.Size, bankBranchSearchRequest.BankName, bankBranchSearchRequest.Search)
	if err != nil {
		return nil, err
	}

	err = objectmapper.GetMapperInstance().MapperSlice(&bankBranchData, &bankBranchResponse)
	if err != nil {
		return nil, constants.ErrMappingFailed.WithDetails(err)
	}

	return &modelsAPIv1.BankBranchSearchAPIResponse{Data: bankBranchResponse}, nil
}

func (d *BankSearchBusinessLogic) BankSearch(ctx context.Context, bankSearchRequest modelsAPIv1.BankSearchAPIRequest) (*modelsAPIv1.BankSearchAPIResponse, error) {
	var bankSearchResponse []modelsAPIv1.BankSearchAPIResponseData
	if bankSearchRequest.Search == constants.Empty {
		bankSearchData := config.Application().Defaults.Bank.TopN
		err := objectmapper.GetMapperInstance().MapperSlice(&bankSearchData, &bankSearchResponse)
		if err != nil {
			return nil, constants.ErrMappingFailed.WithDetails(err)
		}
	} else {
		bankSearchData, err := d.bankSearchRepository.GetBankSearchData(ctx, bankSearchRequest.From, bankSearchRequest.Size, bankSearchRequest.Search)
		if err != nil {
			return nil, err
		}

		err = objectmapper.GetMapperInstance().MapperSlice(&bankSearchData, &bankSearchResponse)
		if err != nil {
			return nil, constants.ErrMappingFailed.WithDetails(err)
		}
	}

	return &modelsAPIv1.BankSearchAPIResponse{Data: bankSearchResponse}, nil
}
