package banksearch

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type BankSearchRepository interface {
	GetBankDataByIfsc(ctx context.Context, ifsc string, from, size int) ([]business.BankBranchMasterSearchData, error)
	GetBankBranchSearchData(ctx context.Context, size int, bankName, searchToken string) ([]business.BankBranchMasterSearchData, error)
	GetBankSearchData(ctx context.Context, from, size int, searchToken string) ([]business.BankSearchData, error)
	IsRegisteredIFSC(ctx context.Context, ifsc string) (bool, error)
}

type BankSearchBusinessLogic struct {
	bankSearchRepository BankSearchRepository
}

func NewBankSearchBusinessLogic(bankSearchRepository BankSearchRepository) *BankSearchBusinessLogic {
	return &BankSearchBusinessLogic{
		bankSearchRepository: bankSearchRepository,
	}
}
