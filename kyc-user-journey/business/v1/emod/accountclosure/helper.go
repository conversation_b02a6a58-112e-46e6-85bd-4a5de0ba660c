package accountclosure

import (
	"context"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	emodmodel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func getEmodStartDTO(
	request *emodmodel.AccountClosureStartRequest, appNumber, mobile string, intent string) *business.EmodStart {
	emodStart := business.EmodStart{
		AppNumber:         appNumber,
		Mobile:            mobile,
		ClientCode:        request.ClientCode,
		JourneyType:       constants.EmodJourneyType,
		Intent:            intent,
		Completed:         false,
		CreateSource:      request.Source,
		LastUpdateSource:  request.Source,
		Status:            constants.EmodStatusCreated,
		OptedMobileChange: false,
	}

	return &emodStart
}

func getAccountClosureStartDTO(
	request *emodmodel.AccountClosureStartRequest,
	appNumber string) *business.AccountClosure {
	if request.Data.ClosureReason != constants.Empty &&
		request.Data.OthersComment != constants.Empty {
		request.Data.ClosureReason += ":" + request.Data.OthersComment
	}

	ac := business.AccountClosure{
		AppNumber:        appNumber,
		ClientCode:       request.ClientCode,
		CreateSource:     request.Source,
		CreateTS:         utils.GetCurrentTime(),
		LastUpdateSource: request.Source,
		LastUpdateTS:     utils.GetCurrentTime(),
		ClosureReason:    request.Data.ClosureReason,
	}

	return &ac
}

func getPanDTO(request *emodmodel.AccountClosureStartRequest, clcmBasicDetails *modelsV1.ClientBasicDetails, appNumber, fullName string) *business.Pan {
	return &business.Pan{
		AppNumber:        appNumber,
		Pan:              clcmBasicDetails.PanDetails.Pan,
		DOB:              clcmBasicDetails.PanDetails.Dob,
		FullName:         fullName,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
		Source:           constants.ClcmSource,
	}
}

func (a *AccountClosureStartBusinessLogic) checkEligibility(ctx context.Context, clientCode, tradeToken, nonTradeToken string) (
	*business.AccountClosureEligibility, bool, error) {
	return PerformEligibilityChecks(ctx, clientCode, tradeToken, nonTradeToken, false,
		a.portfolioExternalProvider,
		a.amxExternalProvider,
		a.ipoExternalProvider,
		a.mfExternalProvider,
		a.subscriptionExternalProvider)
}

func holdingsCheck(data *modelsV1.PortfolioHoldingResponseData) bool {
	if data == nil {
		return true
	}

	if data.TradableHoldings.Holdings != nil {
		for i := range data.TradableHoldings.Holdings {
			if !isZeroNetDetails(&data.TradableHoldings.Holdings[i].NetDetails) {
				return false
			}
		}
	}

	if data.MutualFund.Holdings != nil {
		for i := range data.MutualFund.Holdings {
			if !isZeroNetDetails(&data.MutualFund.Holdings[i].NetDetails) {
				return false
			}
		}
	}

	return true
}

func isZeroNetDetails(d *modelsV1.HoldingDetailsV2) bool {
	if d == nil {
		return true
	}

	values := []float64{
		d.FreeQty,
		d.PoolQty,
		d.MTFQty,
		d.CuspaQty,
		d.MarginQty,
		d.UncreditedBonusQty,
		d.UncreditedSplitQty,
		d.CuspaReleaseQty,
		d.MarginReleaseQty,
		d.MTFReleaseQty,
		d.BlockedQty,
		d.DayBuyQty,
		d.DaySellQty,
		d.TradableQty,
		d.AvailableQty,
	}

	for _, v := range values {
		if v != 0 {
			return false
		}
	}

	return true
}

func ipoCheck(ctx context.Context, rs []modelsV1.IPOOrder) bool {
	// Get the current date
	currentDate := time.Now()
	utils.ConvertTimeToIndianTimezone(&currentDate)

	for _, order := range rs {
		listingDate, err := time.Parse(constants.DOBDateTimeLayout, order.IpoListingDate)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error while parsing date for IPO in account closure/transfer")
			continue // Skip this order if there's a parsing error
		}

		// Check if the current date is less than the listing date and TransactionType is 7021 and status is either submitted,success
		if currentDate.Before(listingDate) && order.TransactionType == constants.ActiveIpoTransactionType &&
			(order.TransactionStatus == constants.OrderStatusSubmitted || order.TransactionStatus == constants.OrderStatusSuccess) {
			return false
		}
	}

	// Return true if no order matches the condition
	return true
}

func fundsCheck(rs modelsV1.RMSLimitData) bool {
	return (rs.NetAvailableFunds >= 0 && rs.NetAvailableFunds <= 1) &&
		(rs.FundsForTrading >= 0 && rs.FundsForTrading <= 1) &&
		(rs.FundsAvailable >= 0 && rs.FundsAvailable <= 1) &&
		(rs.FundsForAllocation >= 0 && rs.FundsForAllocation <= 1)
}

func positionsCheck(rs []modelsV1.PositionsData) bool {
	return len(rs) == 0
}

func ordersCheck(rs []modelsV1.OrderbookData, useS2S bool) bool {
	if useS2S {
		// Since using v2 api for s2s, iterate through orders and check all open orders
		for _, order := range rs {
			if order.OrderStatus == constants.OpenOrders {
				return false
			}
		}
		return true
	}
	return len(rs) == 0
}

func mfCheck(o modelsV1.GetMFOrdersData,
	sip modelsV1.GetMFSIPsData, swp modelsV1.GetMFSWPsData) bool {
	return o.Summary.TotalProcessingOrders == 0 &&
		sip.Summary.TotalSIP == 0 &&
		swp.Total == 0
}

func subscriptionCheck(response modelsV1.PlanDetails) (bool, string) {
	if response.IsPlanActive {
		subscriptionStatus := response.Status
		return false, subscriptionStatus
	}
	return true, ""
}

func getFailedChecks(checks *business.AccountClosureEligibility) []string {
	var failure []string
	if !checks.Funds {
		failure = append(failure, "funds")
	}
	if !checks.Positions {
		failure = append(failure, "positions")
	}
	if !checks.Holdings {
		failure = append(failure, "holdings")
	}
	if !checks.IPO {
		failure = append(failure, "ipo")
	}
	if !checks.MF {
		failure = append(failure, "mf")
	}
	if !checks.Orders {
		failure = append(failure, "orders")
	}
	if !checks.Subscription {
		failure = append(failure, "subscription_"+strings.ToLower(checks.SubscriptionStatus))
	}
	return failure
}

func validateAccountTransferRequest(checks *business.AccountClosureEligibility) bool {
	return !checks.Holdings && checks.MF && checks.Orders && checks.IPO && checks.Positions && checks.Funds
}

func (a *AccountClosureStartBusinessLogic) updateClosureReasonForExistingJourney(ctx context.Context, request *emodmodel.AccountClosureStartRequest,
	existingAppNumber string, rs *emodmodel.AccountClosureStartResponse) error {
	if request.Data.ClosureReason != constants.Empty &&
		request.Data.OthersComment != constants.Empty {
		request.Data.ClosureReason += ":" + request.Data.OthersComment
	}

	err := a.accountClosureRepository.UpdateClosureReason(ctx, existingAppNumber, request.Data.ClosureReason)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountTransferStart: Error updating closure reason")
		return err
	}

	rs.AppNumber = existingAppNumber
	return nil
}

func (a *AccountClosureStartBusinessLogic) createNewRequest(ctx context.Context, request *emodmodel.AccountClosureStartRequest,
	profileResponse *modelsV1.GetProfileResponse, rs *emodmodel.AccountClosureStartResponse, clcmBasicResponse *modelsV1.GetClientBasicDetailsReponse,
	intent, fullName string, isNameMatched bool) (*emodmodel.AccountClosureStartResponse, error) {
	nextAppNumber, err := a.emodStartRepository.GetNextAppNumberForJourneyType(constants.EmodJourneyType)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountClosureStart: Error generating new app number")
		return nil, err
	}

	acDTO := getAccountClosureStartDTO(request, nextAppNumber)
	emodDTO := getEmodStartDTO(request, nextAppNumber, profileResponse.Profiles[0].Mobile, intent)
	panDTO := getPanDTO(request, &clcmBasicResponse.ClientBasicDetails, nextAppNumber, fullName)
	err = a.emodRepository.CreateAccountClosureRequest(ctx, emodDTO, acDTO, panDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountClosureStart: Error creating request in DB")
		return nil, err
	}

	rs.AppNumber = nextAppNumber
	rs.IsValidPanDetails = isNameMatched
	return rs, nil
}

func (a *AccountClosureStartBusinessLogic) checkAndUpdateIfJourneyExistWithSameIntent(ctx context.Context, request *emodmodel.AccountClosureStartRequest, newAccountTransferApp bool, appAccountTransferNumber string,
	isEligibleForAccountTransfer bool, err error, rs emodmodel.AccountClosureStartResponse, newApp bool, appNumber string, isNameMatched bool) (bool, *emodmodel.AccountClosureStartResponse, error) {
	// account transfer journey already initiated
	if !newAccountTransferApp && appAccountTransferNumber != constants.Empty && isEligibleForAccountTransfer {
		err = a.updateClosureReasonForExistingJourney(ctx, request, appAccountTransferNumber, &rs)
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountTransferStart [Already Existing] : Error updating closure reason")
			rs.IsValidPanDetails = isNameMatched
			return true, nil, err
		}
		rs.IsValidPanDetails = isNameMatched
		return true, &rs, nil
	}

	// account closure journey already initiated
	if !newApp && appNumber != constants.Empty {
		err = a.updateClosureReasonForExistingJourney(ctx, request, appNumber, &rs)
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountClosureStart: Error updating closure reason")
			rs.IsValidPanDetails = isNameMatched
			return true, nil, err
		}
		rs.IsValidPanDetails = isNameMatched
		return true, &rs, nil
	}
	return false, nil, err
}

func (a *AccountClosureStartBusinessLogic) checkAndUpdateIfJourneyExistWithAnotherIntent(ctx context.Context, request *emodmodel.AccountClosureStartRequest, newApp bool, appNumber string, isEligibleForAccountTransfer bool, err error, rs emodmodel.AccountClosureStartResponse,
	newAccountTransferApp bool, appAccountTransferNumber string, isNameMatched bool) (bool, *emodmodel.AccountClosureStartResponse, error) {
	// Account closure journey already initiated and isEligibleForAccountTransfer
	// Update the intent
	if !newApp && appNumber != constants.Empty && isEligibleForAccountTransfer {
		if request.Data.ClosureReason != constants.Empty &&
			request.Data.OthersComment != constants.Empty {
			request.Data.ClosureReason += ":" + request.Data.OthersComment
		}

		err = a.emodRepository.UpdateClosureReasonAndIntent(ctx, appNumber, constants.EmodAccountTransferIntent, request.Source, request.Data.ClosureReason)
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountTransferStart [Intent Update]: Error updating closure reason")
			return true, nil, err
		}
		rs.AppNumber = appNumber
		rs.IsValidPanDetails = isNameMatched
		return true, &rs, nil
	}

	if !newAccountTransferApp && appAccountTransferNumber != constants.Empty && !isEligibleForAccountTransfer {
		if request.Data.ClosureReason != constants.Empty &&
			request.Data.OthersComment != constants.Empty {
			request.Data.ClosureReason += ":" + request.Data.OthersComment
		}

		err = a.emodRepository.UpdateClosureReasonAndIntent(ctx, appAccountTransferNumber, constants.EmodAccountClosureIntent, request.Source, request.Data.ClosureReason)
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountTransferStart [Intent Update]: Error updating closure reason")
			return true, nil, err
		}
		rs.AppNumber = appAccountTransferNumber
		rs.IsValidPanDetails = isNameMatched
		return true, &rs, nil
	}
	return false, nil, err
}

func PerformEligibilityChecks(
	ctx context.Context,
	clientCode, tradeToken, nonTradeToken string,
	useS2S bool,
	portfolioExternalProvider PortfolioExternalProvider,
	amxExternalProvider AMXExternalProvider,
	ipoExternalProvider IPOExternalProvider,
	mfExternalProvider MFExternalProvider,
	subscriptionExternalProvider SubscriptionPlanExternalProvider,
) (*business.AccountClosureEligibility, bool, error) {
	chSuperPortfolio := make(chan modelsV1.ChannelGetPortfolioHoldingResponse)
	chRMSLimit := make(chan modelsV1.ChannelGetRMSLimitResponse)
	chOrderbook := make(chan modelsV1.ChannelGetOrderbookResponse)
	chPositions := make(chan modelsV1.ChannelGetPositionsResponse)
	chIPOOrders := make(chan modelsV1.ChannelGetIPOOrdersResponse)
	chMFOrders := make(chan modelsV1.ChannelGetMFOrdersResponse)
	chMFSIPs := make(chan modelsV1.ChannelGetMFSIPsResponse)
	chMFSWPs := make(chan modelsV1.ChannelGetMFSWPsResponse)
	chSubscription := make(chan modelsV1.ChannelGetActiveSubscriptionPlanStatusResponse)
	portfolioSegments := []string{constants.Equity, constants.MF}
	go portfolioExternalProvider.GetPortfolioDetailsWithChannel(ctx, clientCode, nonTradeToken, portfolioSegments, chSuperPortfolio)
	if useS2S {
		go amxExternalProvider.GetRMSLimitUsingS2SWithChannel(ctx, clientCode, chRMSLimit)
		go amxExternalProvider.GetOrderBookUsingS2SWithChannel(ctx, clientCode, chOrderbook)
		go amxExternalProvider.GetPositionsUsingS2SWithChannel(ctx, clientCode, chPositions)
		go ipoExternalProvider.GetIPOOrdersUsingS2SWithChannel(ctx, clientCode, chIPOOrders)
	} else {
		go amxExternalProvider.GetRMSLimitWithChannel(ctx, clientCode, tradeToken, chRMSLimit)
		go amxExternalProvider.GetOrderbookWithChannel(ctx, clientCode, tradeToken, chOrderbook)
		go amxExternalProvider.GetPositionsWithChannel(ctx, clientCode, tradeToken, chPositions)
		go ipoExternalProvider.GetIPOOrdersWithChannel(ctx, clientCode, nonTradeToken, chIPOOrders)
	}
	go mfExternalProvider.GetMFOrdersWithChannel(ctx, clientCode, nonTradeToken, chMFOrders)
	go mfExternalProvider.GetMFSIPsWithChannel(ctx, clientCode, nonTradeToken, chMFSIPs)
	go mfExternalProvider.GetMFSWPsWithChannel(ctx, clientCode, nonTradeToken, chMFSWPs)
	go subscriptionExternalProvider.GetSubscriptionPlanStatusWithChannel(ctx, clientCode, chSubscription)
	chSuperPortfolioRes := <-chSuperPortfolio
	if chSuperPortfolioRes.Error != nil {
		return nil, false, chSuperPortfolioRes.Error
	}
	chRMSLimitRes := <-chRMSLimit
	if chRMSLimitRes.Error != nil {
		return nil, false, chRMSLimitRes.Error
	}
	chOrderbookRes := <-chOrderbook
	if chOrderbookRes.Error != nil {
		return nil, false, chOrderbookRes.Error
	}
	chPositionsRes := <-chPositions
	if chPositionsRes.Error != nil {
		return nil, false, chPositionsRes.Error
	}
	chIPOOrdersRes := <-chIPOOrders
	if chIPOOrdersRes.Error != nil {
		return nil, false, chIPOOrdersRes.Error
	}
	chMFOrdersRes := <-chMFOrders
	if chMFOrdersRes.Error != nil {
		return nil, false, chMFOrdersRes.Error
	}
	chMFSIPsRes := <-chMFSIPs
	if chMFSIPsRes.Error != nil {
		return nil, false, chMFSIPsRes.Error
	}
	chMFSWPsRes := <-chMFSWPs
	if chMFSWPsRes.Error != nil {
		return nil, false, chMFSWPsRes.Error
	}
	chSubscriptionRes := <-chSubscription
	if chSubscriptionRes.Error != nil {
		return nil, false, chSubscriptionRes.Error
	}
	isHoldingsClear := holdingsCheck(&chSuperPortfolioRes.Response.Data)
	isIPOClear := ipoCheck(ctx, chIPOOrdersRes.IPOResponse.Data)
	isFundsClear := fundsCheck(chRMSLimitRes.RMSLimitResponse.Data)
	isPositionsClear := positionsCheck(chPositionsRes.PositionsResponse.Data)
	isOrdersClear := ordersCheck(chOrderbookRes.OrderbookResponse.Data, useS2S)
	isMFClear := mfCheck(chMFOrdersRes.GetMFOrdersResponse.Data, chMFSIPsRes.GetMFSIPsResponse.Data,
		chMFSWPsRes.GetMFSWPsResponse.Data)
	isSubscriptionClear, subscriptionStatus := subscriptionCheck(chSubscriptionRes.ActiveSubscriptionPlanStatusResponse.Data)
	return buildEligibilityResponse(isHoldingsClear, isIPOClear, isFundsClear, isPositionsClear, isOrdersClear, isMFClear, isSubscriptionClear, subscriptionStatus)
}

func buildEligibilityResponse(isHoldingsClear, isIPOClear, isFundsClear, isPositionsClear, isOrdersClear, isMFClear, isSubscriptionClear bool, subscriptionStatus string) (*business.AccountClosureEligibility, bool, error) {
	if isHoldingsClear && isIPOClear && isFundsClear && isPositionsClear && isOrdersClear && isMFClear && isSubscriptionClear {
		return &business.AccountClosureEligibility{Funds: true, Positions: true, Holdings: true,
			IPO: true, MF: true, Orders: true, Subscription: isSubscriptionClear, SubscriptionStatus: subscriptionStatus}, true, nil
	}

	return &business.AccountClosureEligibility{Funds: isFundsClear, Positions: isPositionsClear,
		Holdings: isHoldingsClear, IPO: isIPOClear, MF: isMFClear, Orders: isOrdersClear, Subscription: isSubscriptionClear, SubscriptionStatus: subscriptionStatus}, false, nil
}

func (a *AccountClosureStartBusinessLogic) validateEligibilityChecksForClosureRequest(ctx context.Context, clientCode, nonTradeToken string) (
	*business.AccountClosureEligibility, bool, error) {
	return PerformEligibilityChecks(ctx, clientCode, "", nonTradeToken, true,
		a.portfolioExternalProvider,
		a.amxExternalProvider,
		a.ipoExternalProvider,
		a.mfExternalProvider,
		a.subscriptionExternalProvider)
}
