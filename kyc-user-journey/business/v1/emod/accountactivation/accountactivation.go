package accountactivation

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/nsdl"
)

func (a *AccountActivationBusinessLogic) UploadList(ctx context.Context, req *api.AccountActivationToggleUploadAPIRequest) error {
	file, err := req.Data.File.Open()
	if err != nil {
		return constants.ErrOpeningFile.WithDetails(err)
	}
	defer file.Close()

	records, err := csv.NewReader(file).ReadAll()
	if err != nil {
		return constants.ErrReadingFile.WithDetails(err)
	}
	log.Info(ctx).Interface("length", len(records)).Msg("UploadList: file Read success")

	clientActivationDTO := make([]business.ClientActivationToggle, len(records))
	for i, record := range records {
		// row length check.
		if len(record) != 1 {
			log.Error(ctx).Err(err).Msg("UploadList: invalid format")
			return constants.ErrInvalidFileFormat.Value()
		}
		// clientCode validation.
		clientCode := record[0]
		if !utils.IsValidAgainstRegex(config.Application().Validations.ClientCodeRegex, clientCode) {
			log.Error(ctx).Msg("UploadList: invalid clientCode")
			return constants.ErrInvalidClientCode.WithMessage(fmt.Sprintf("Invalid ClientCode: %s", clientCode))
		}

		clientActivationDTO[i] = business.ClientActivationToggle{
			ClientCode:       clientCode,
			AppNumber:        constants.Empty,
			Flow:             req.Intent,
			Status:           constants.PendingStatus,
			ReasonID:         req.Data.ReasonID,
			Comment:          req.Data.Comment,
			AgentID:          req.AgentID,
			CreateSource:     req.Source,
			LastUpdateSource: req.Source,
		}
	}
	// save records.
	err = a.clientActivationToggleRepository.BulkInsert(ctx, clientActivationDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("UploadList: error updating db")
		return err
	}
	return nil
}

func (a *AccountActivationBusinessLogic) GetReasonsList(ctx context.Context, _ *api.AccountActivationToggleReasonsAPIRequest) (*api.AccountActivationToggleReasonsAPIResponse, error) {
	reasonsDTO, err := a.clientReasonsMasterRepository.FetchReasonsWithFlow(ctx, []string{constants.ReactivationFlowName, constants.DeactivationFlowName})
	if err != nil {
		log.Error(ctx).Err(err).Msg("GetReasonsList: error with db")
		return nil, err
	}

	response := make(api.AccountActivationToggleReasonsAPIResponse)
	for _, reason := range reasonsDTO {
		response[reason.Flow] = append(response[reason.Flow], api.AccountActivationReasons{
			ID:     reason.ID,
			Reason: reason.Reason,
		})
	}
	return &response, nil
}

func (a *AccountActivationBusinessLogic) AccountActivationStart(ctx context.Context, req *api.AccountActivationToggleStartAPIRequest) (
	*api.AccountActivationToggleStartAPIResponse, error) {
	// check eligibility.
	isRetryableError, eligibilityErr := a.checkEligibility(ctx, req)
	if eligibilityErr != nil && isRetryableError {
		log.Error(ctx).Err(eligibilityErr).Msg("AccountActivationStart: eligibility failed")
		return nil, eligibilityErr
	}

	// get appNumber.
	if req.AppNumber == constants.Empty {
		appNumber, err := a.emodStartRepository.GetNextAppNumberForJourneyType(constants.EmodJourneyType)
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountActivationStart: error getting appNumber for emod journey")
			return nil, err
		}
		req.AppNumber = appNumber
	}

	// update db.
	emodStartDTO, stpRejectionDTO, basicDTO := getEmodDTO(req, eligibilityErr)
	err := a.emodRepository.UpsertAccountActivation(ctx, emodStartDTO, stpRejectionDTO, basicDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountActivationStart: error saving in db")
		return nil, err
	}

	// start workflow.
	if eligibilityErr == nil {
		_, err := a.workflowExternalProvider.StartProcessInstance(ctx, getWorkflowName(req.Intent), modelsExternals.ProcessStartRequest{
			BusinessKey: req.ClientCode,
			Variables:   getProcessVariables(req),
		})
		if err != nil {
			log.Error(ctx).Err(err).Msg("AccountActivationStart: error while starting workflow")
			return nil, err
		}
	}

	// all good return success.
	log.Info(ctx).Str(constants.LogAppNumberKey, req.AppNumber).Msg("AccountActivationStart: success")
	return &api.AccountActivationToggleStartAPIResponse{AppNumber: req.AppNumber}, nil
}

// checkEligibility returns error and boolean flag indicating if error is retryable.
func (a *AccountActivationBusinessLogic) checkEligibility(ctx context.Context, req *api.AccountActivationToggleStartAPIRequest) (bool, error) {
	profileRes, clcmBasicRes, clcmSegmentRes, clientSegments, err := a.getExternalDetails(ctx, req.ClientCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountActivationStart: error in external call")
		return true, err
	}
	// do common emod checks.
	_, appNumber, err := a.commonEmodBusinessLogic.ProceedToEmod(ctx, req.Intent, req.JourneyType, profileRes, clientSegments, false)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountActivationStart: error in ProceedToEmod")
		return false, err
	}
	req.AppNumber = appNumber
	req.Mobile = profileRes.Profiles[0].Mobile

	// validate account deactivation value.
	err = validateDeactiveValue(req.Intent, clcmSegmentRes)
	if err != nil {
		log.Error(ctx).Err(err).Msg("AccountActivationStart: invalid deactive value")
		return false, err
	}
	// validate name dob with nsdl.
	if req.Intent == constants.EmodReactivationIntent {
		nsdlResponse, err := a.nsdlExternalProvider.ValidatePANV2(
			ctx, &modelsExternals.NSDLVerifyPanV2Request{
				InputData: []modelsExternals.NSDLVerifyPanV2InputData{{
					Pan:  clcmBasicRes.ClientBasicDetails.PanDetails.Pan,
					Name: clcmBasicRes.ClientBasicDetails.PanDetails.FullName,
					DOB:  clcmBasicRes.ClientBasicDetails.PanDetails.Dob,
				}},
			},
			nsdl.RoundRobinV2.Next(),
		)
		if err != nil {
			return true, constants.ErrNSDLPanValidation.WithDetails(err.Error())
		}
		if len(nsdlResponse.Data) == 0 {
			return true, constants.ErrNSDLPanValidation.WithDetails("Invalid NSDL Response")
		} else if !nsdlResponse.Data[0].IsValid {
			return false, constants.ErrInValidPAN.Value()
		}

		if !nsdlResponse.Data[0].IsNameMatch || !nsdlResponse.Data[0].ISDoBMatch {
			return false, constants.ErrPanNameAndDOBMisMatch.Value()
		}
	}
	return false, nil
}

func (a *AccountActivationBusinessLogic) getExternalDetails(ctx context.Context, clientCode string) (
	*modelsExternals.GetProfileResponse, *modelsExternals.GetClientBasicDetailsReponse, *modelsExternals.FetchSegmentsResponse, *modelsExternals.FetchSegmentsResponseV2, error) {
	// Create channels to collect API responses and errors.
	chProfile := make(chan modelsExternals.ChannelGetProfileResponse)
	chClcmBasicDetails := make(chan modelsExternals.ChannelGetClientBasicDetailsResponse)
	chSegments := make(chan modelsExternals.ChannelFetchSegmentsResponse)

	go a.clcmExternalProvider.GetCLCMProfilesWithChannel(ctx, &modelsExternals.ProfileDetailsFromCLCMRequest{
		ClientID: clientCode,
		Fields:   constants.CLCMProfileAttributesForEmodCheck,
	}, chProfile)
	go a.clcmExternalProvider.GetClientBasicDetailsWithChannel(ctx, clientCode, chClcmBasicDetails)
	go a.clcmExternalProvider.FetchSegmentDetailsWithChannel(ctx, clientCode, chSegments)

	chProfileRes := <-chProfile
	if chProfileRes.Error != nil {
		return nil, nil, nil, nil, chProfileRes.Error
	}
	chClcmBasicDetailsRes := <-chClcmBasicDetails
	if chClcmBasicDetailsRes.Error != nil {
		return nil, nil, nil, nil, chClcmBasicDetailsRes.Error
	}

	chSegmentsRes := <-chSegments
	if chSegmentsRes.Error != nil && !errors.Is(chSegmentsRes.Error, constants.ErrEmptyClcmSegmentsFetch.Value()) {
		return nil, nil, nil, nil, chSegmentsRes.Error
	}

	clientSegments, err := a.clcmExternalProvider.FetchSegmentDetailsV2(ctx, clientCode)
	if err != nil && !errors.Is(err, constants.ErrEmptyClcmSegmentsFetch.Value()) {
		return nil, nil, nil, nil, err
	}

	return chProfileRes.ProfileResponse, chClcmBasicDetailsRes.ClientBasicDetails, chSegmentsRes.SegmentsResponse, clientSegments, nil
}
