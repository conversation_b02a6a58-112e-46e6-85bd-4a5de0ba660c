package accountactivation

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
)

type ClientActivationToggleRepository interface {
	BulkInsert(ctx context.Context, clientActivationToggleDTO []business.ClientActivationToggle) error
}

type ClientReasonsMasterRepository interface {
	FetchReasonsWithFlow(ctx context.Context, flow []string) ([]business.ClientReasonsMaster, error)
}

type EmodStartRepository interface {
	GetNextAppNumberForJourneyType(journeyType string) (string, error)
}

type EmodRepository interface {
	UpsertAccountActivation(ctx context.Context, emodStartDTO *business.EmodStart, stpRejectionDTO *business.STPRejection, basicDTO *business.Basic) error
}

type ClcmExternalProvider interface {
	GetClientBasicDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelGetClientBasicDetailsResponse)
	FetchSegmentDetailsWithChannel(ctx context.Context, clientCode string, ch chan modelsExternals.ChannelFetchSegmentsResponse)
	FetchSegmentDetailsV2(ctx context.Context, clientCode string) (*modelsExternals.FetchSegmentsResponseV2, error)
	GetCLCMProfilesWithChannel(ctx context.Context, request *modelsExternals.ProfileDetailsFromCLCMRequest, ch chan modelsExternals.ChannelGetProfileResponse)
}

type NsdlExternalProvider interface {
	ValidatePANV2(ctx context.Context, request *modelsExternals.NSDLVerifyPanV2Request, userName string) (*modelsExternals.ValidatePanWithNSDLV2FinalResponse, error)
}

type ProfileExternalProvider interface {
	GetProfilesWithChannel(ctx context.Context, request *modelsExternals.GetProfileRequest, ch chan modelsExternals.ChannelGetProfileResponse)
}

type WorkflowExternalProvider interface {
	StartProcessInstance(ctx context.Context, workflowName string, request modelsExternals.ProcessStartRequest) (*modelsExternals.ProcessStartResponse, error)
}

type CommonEmodBusinessLogic interface {
	ProceedToEmod(ctx context.Context, currentIntent, journeyType string, profileResponse *modelsExternals.GetProfileResponse, clientSegments *modelsExternals.FetchSegmentsResponseV2, isMfOnlyUser bool) (bool, string, error)
}

type AccountActivationBusinessLogic struct {
	clientActivationToggleRepository ClientActivationToggleRepository
	clientReasonsMasterRepository    ClientReasonsMasterRepository
	emodStartRepository              EmodStartRepository
	emodRepository                   EmodRepository
	clcmExternalProvider             ClcmExternalProvider
	profileExternalProvider          ProfileExternalProvider
	nsdlExternalProvider             NsdlExternalProvider
	workflowExternalProvider         WorkflowExternalProvider
	commonEmodBusinessLogic          CommonEmodBusinessLogic
}

func NewAccountActivationBusinessLogic(clientActivationToggleRepository ClientActivationToggleRepository,
	clientReasonsMasterRepository ClientReasonsMasterRepository, emodStartRepository EmodStartRepository, emodRepository EmodRepository,
	clcmExternalProvider ClcmExternalProvider, profileExternalProvider ProfileExternalProvider, nsdlExternalProvider NsdlExternalProvider,
	workflowExternalProvider WorkflowExternalProvider, commonEmodBusinessLogic CommonEmodBusinessLogic) *AccountActivationBusinessLogic {
	return &AccountActivationBusinessLogic{
		clientActivationToggleRepository: clientActivationToggleRepository,
		clientReasonsMasterRepository:    clientReasonsMasterRepository,
		emodStartRepository:              emodStartRepository,
		emodRepository:                   emodRepository,
		clcmExternalProvider:             clcmExternalProvider,
		profileExternalProvider:          profileExternalProvider,
		nsdlExternalProvider:             nsdlExternalProvider,
		workflowExternalProvider:         workflowExternalProvider,
		commonEmodBusinessLogic:          commonEmodBusinessLogic,
	}
}
