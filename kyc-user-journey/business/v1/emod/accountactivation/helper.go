package accountactivation

import (
	"slices"

	"github.com/angel-one/go-utils/errors"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
)

func getEmodDTO(req *modelsAPIV1.AccountActivationToggleStartAPIRequest, eligibilityErr error) (*business.EmodStart, *business.STPRejection, *business.Basic) {
	emodStart := business.EmodStart{
		AppNumber:        req.AppNumber,
		Mobile:           req.Mobile,
		ClientCode:       req.ClientCode,
		JourneyType:      constants.EmodJourneyType,
		Intent:           req.Intent,
		Completed:        false,
		Status:           constants.EmodStatusCreated,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
	stpRejection := business.STPRejection{
		AppNumber:        req.AppNumber,
		Reason:           constants.Empty,
		Status:           constants.STPRejectionReasonStatusActive,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
	basic := business.Basic{
		AppNumber:        req.AppNumber,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
	if eligibilityErr != nil { // stp reject case.
		emodStart.Status = constants.EmodStatusFailed
		emodStart.Completed = true

		msg := eligibilityErr.Error()
		var r *errors.Error
		if ok := errors.As(eligibilityErr, &r); ok {
			msg = r.Message
		}

		stpRejection.Reason = msg
		return &emodStart, &stpRejection, &basic
	}
	return &emodStart, nil, nil
}

func getWorkflowName(intent string) string {
	if intent == constants.EmodReactivationIntent {
		return constants.ReactivationFulfilmentWorkflow
	}
	return constants.DeactivationFulfilmentWorkflow
}

func getProcessVariables(request *modelsAPIV1.AccountActivationToggleStartAPIRequest) map[string]modelsExternal.VariableObject {
	variables := map[string]modelsExternal.VariableObject{}
	variables[constants.MobileWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Mobile,
	}
	variables[constants.AppNumberWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.AppNumber,
	}
	variables[constants.ClientCodeWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.ClientCode,
	}
	variables[constants.CorrelationIDWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.ClientCode,
	}
	variables[constants.SourceWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Source,
	}
	variables[constants.IntentWorkflowKey] = modelsExternal.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: request.Intent,
	}
	return variables
}

func validateDeactiveValue(intent string, clcmSegmentRes *modelsExternal.FetchSegmentsResponse) error {
	switch intent {
	case constants.EmodReactivationIntent:
		allowedReasons := []string{"C", "R", "S", "T"}
		for _, segment := range clcmSegmentRes.Data {
			if !slices.Contains(allowedReasons, segment.DeActivationReason) {
				return nil
			}
		}
		return constants.ErrAccountReactivationNotEligible.Value()
	case constants.EmodDeactivationIntent:
		allowedReasons := []string{"C", "I", "S", "T"}
		for _, segment := range clcmSegmentRes.Data {
			if !slices.Contains(allowedReasons, segment.DeActivationReason) {
				return nil
			}
		}
		return constants.ErrAccountDeactivationNotEligible.Value()
	default:
		return nil
	}
}
