package brokerage

import (
	"context"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPI "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
)

func (c *BrokerageBusinessLogic) UpdateBrokeragePlan(ctx context.Context, request *modelsAPI.BrokeragePlanUpdateAPIRequest) error {
	referralDTO, err := c.referralRepository.GetReferralWithAppNumber(ctx, request.AppNumber)
	if err != nil {
		return err
	}

	if !(referralDTO.Type == constants.B2B && config.Feature().Enable.HybridPlan &&
		(referralDTO.PlanName == constants.ITradePrimeVBB || referralDTO.PlanName == constants.B2bAssistedPlanName || referralDTO.PlanName == constants.ITradePremier) &&
		!slices.Contains(config.Referral().Brokerage.HybridPlan.ExcludedBranches, referralDTO.Branch) &&
		!slices.Contains(config.Referral().Brokerage.HybridPlan.CorporateDRATags, referralDTO.SBTag) &&
		referralDTO.LastUpdateTS.After(time.Date(2024, time.June, 28, 0, 0, 0, 0, time.Local))) {
		return constants.ErrUnsupportedPlan.Value()
	}

	referralDTO.PlanName = request.Data.Plan
	err = c.referralRepository.UpdatePlanName(ctx, referralDTO, request.Source)
	if err != nil {
		return err
	}

	return nil
}

func (c *BrokerageBusinessLogic) GetBrokeragePlanComparisonData(ctx context.Context, request *modelsAPI.BrokeragePlanCompareAPIRequest) (*modelsAPI.BrokeragePlanCompareAPIResponse, error) {
	var brokerageDetailsInputData []business.BrokeragePlanDetailsInputData
	err := objectmapper.GetMapperInstance().MapperSlice(&request.Data.Plans, &brokerageDetailsInputData)
	if err != nil {
		return nil, constants.ErrMappingFailed.Value()
	}

	brokerageDetails, err := c.brokerageRepository.GetBrokerageDetails(ctx, brokerageDetailsInputData)
	if err != nil {
		return nil, err
	}
	planInfoList := make([]modelsAPI.BrokeragePlanInfo, 0)
	var brokeragePlanDetails modelsAPI.BrokeragePlanDetails

	for i := range brokerageDetails {
		val := &brokerageDetails[i]
		brokerageInformation := c.getProcessedBrokerageDetails(ctx, val, val.PlanName)
		err = objectmapper.GetMapperInstance().AutoMapper(&brokerageInformation, &brokeragePlanDetails)
		if err != nil {
			return nil, constants.ErrMappingFailed.Value()
		}

		brokeragePlanInfo := modelsAPI.BrokeragePlanInfo{
			PlanName:    val.PlanName,
			PlanDetails: brokeragePlanDetails,
		}

		planInfoList = append(planInfoList, brokeragePlanInfo)
	}

	return &modelsAPI.BrokeragePlanCompareAPIResponse{PlanInfo: planInfoList}, nil
}

func (c *BrokerageBusinessLogic) GetBrokeragePlanDetailsComparisonData(request *modelsAPI.BrokeragePlanDetailsCompareAPIRequest) (*modelsAPI.BrokeragePlanDetailsCompareAPIResponse, error) {
	plans := request.Data.Plans
	planNames := make([]string, 0)
	for _, plan := range plans {
		planNames = append(planNames, plan.Name)
	}

	sort.Slice(planNames, func(i, j int) bool {
		return planNames[i] < planNames[j]
	})
	concatenatedPlanNames := strings.Join(planNames, constants.Dash)
	brokeragePlanComparisonDetailsList := make([]modelsAPI.BrokeragePlanDetailsComparison, 0)

	for _, planName := range planNames {
		planConfig := config.Referral().Brokerage.PlanDetailsComparison[concatenatedPlanNames][planName]
		var brokeragePlanDetailsComparisonDataList []modelsAPI.BrokeragePlanDetailsComparisonData
		err := objectmapper.GetMapperInstance().MapperSlice(&planConfig, &brokeragePlanDetailsComparisonDataList)
		if err != nil {
			return nil, constants.ErrMappingFailed.Value()
		}

		brokeragePlanComparisonData := modelsAPI.BrokeragePlanDetailsComparison{
			PlanName: planName,
			Data:     brokeragePlanDetailsComparisonDataList,
		}

		brokeragePlanComparisonDetailsList = append(brokeragePlanComparisonDetailsList, brokeragePlanComparisonData)
	}

	return &modelsAPI.BrokeragePlanDetailsCompareAPIResponse{PlanDetails: brokeragePlanComparisonDetailsList}, nil
}

func (c *BrokerageBusinessLogic) GetBrokerageDetails(ctx context.Context, request *modelsAPI.BrokerageDetailsAPIRequest) (*modelsAPI.BrokerageDetailsAPIResponse, error) {
	referralDTO, err := c.referralRepository.GetReferralWithAppNumber(ctx, request.AppNumber)
	if err != nil {
		return nil, err
	}

	var planName, clientType string
	if referralDTO.Type == constants.Empty {
		clientType = constants.B2C
	} else {
		clientType = referralDTO.Type
	}

	if referralDTO.PlanName == constants.Empty {
		planName = config.Application().Defaults.ReferralPlan
	} else {
		planName = referralDTO.PlanName
	}

	brokerageDetails, err := c.brokerageRepository.GetBrokerageDetails(ctx, []business.BrokeragePlanDetailsInputData{{
		Name:       planName,
		ClientType: clientType,
	}})
	if err != nil {
		return nil, err
	}

	if len(brokerageDetails) == 0 {
		return nil, constants.ErrNoBrokerageDetailsFound.Value()
	}

	var brokerageDetailsAPIResponse modelsAPI.BrokerageDetailsAPIResponse

	processedBrokerageDetails := c.getProcessedBrokerageDetails(ctx, &brokerageDetails[0], planName)
	err = objectmapper.GetMapperInstance().AutoMapper(&processedBrokerageDetails, &brokerageDetailsAPIResponse)
	if err != nil {
		return nil, constants.ErrMappingFailed.Value()
	}

	return &brokerageDetailsAPIResponse, nil
}

func (c *BrokerageBusinessLogic) getComputedCommodityOptionDetails(ctx context.Context, planName string) *business.CommodityOptionsBrokerageDetails {
	if slices.Contains(config.Referral().Brokerage.NewCommodityOptionsBrokeragePlans, planName) {
		commodityBrokerage, err := c.getCommodityOptionBrokerage(ctx, planName)
		if err != nil {
			log.Info(ctx).Str("PlanName", planName).Msg("getCommodityOptionDetails: Error while fetching commodity options brokerage details")
		} else {
			return commodityBrokerage
		}
	}

	return nil
}

func (c *BrokerageBusinessLogic) getCommodityOptionBrokerage(ctx context.Context, planName string) (*business.CommodityOptionsBrokerageDetails, error) {
	commodityBrokerage, err := c.brokerageRepository.GetCommodityOptionBrokerage(ctx, planName)
	if err != nil {
		return nil, err
	}

	commodityOptions := make(map[string]string)
	for i := 0; i < len(commodityBrokerage); i++ {
		commodityOptions[commodityBrokerage[i].Symbol] = commodityBrokerage[i].PricePerLot
	}

	return &business.CommodityOptionsBrokerageDetails{
		Zinc:     commodityOptions[constants.ZINC],
		Copper:   commodityOptions[constants.COPPER],
		CrudeOil: commodityOptions[constants.CRUDEOIL],
		Gold:     commodityOptions[constants.GOLD],
		Silver:   commodityOptions[constants.SILVER],
		All:      commodityOptions[constants.ALL],
	}, nil
}

func (c *BrokerageBusinessLogic) getProcessedBrokerageDetails(ctx context.Context, brokerageDetails *business.BrokerageDetails, planName string) business.BrokerageInformation {
	var commodityOptionBrokeragePerLotFlatFee string
	var isCommodityOptionBrokerageFlatFee bool
	var commodityOption any
	commodityOptionBrokeragePerLotPlan := c.getComputedCommodityOptionDetails(ctx, planName)
	if commodityOptionBrokeragePerLotPlan == nil {
		commodityOptionBrokeragePerLotFlatFee = getFlatOptionDetails(brokerageDetails.OptionCommodity, planName)
		isCommodityOptionBrokerageFlatFee = true
		commodityOption = commodityOptionBrokeragePerLotFlatFee
	} else {
		commodityOption = commodityOptionBrokeragePerLotPlan
	}

	brokerageInformation := business.BrokerageInformation{
		DpAMCCharges:                          brokerageDetails.DpAMCCharges,
		EquityDelivery:                        addPrefixSuffixToBrokerage(constants.Empty, constants.Percentage, brokerageDetails.OfflineEquityDelivery),
		FixedEquityDelivery:                   addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.FixedEquityDelivery),
		EquityIntraDayFutures:                 addPrefixSuffixToBrokerage(constants.Space, constants.Percentage, brokerageDetails.OffLineIntraDayEachLeg),
		NiftyOption:                           addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.NiftyOption),
		OtherOptions:                          addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.OptionsStock),
		CurrencyOption:                        addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.OptionCurrency),
		CurrencyOptionFutures:                 addPrefixSuffixToBrokerage(constants.Space, constants.Percentage, brokerageDetails.IntradayCurrency),
		CommodityDelivery:                     addPrefixSuffixToBrokerage(constants.Space, constants.Percentage, brokerageDetails.OffLineCommDelivery),
		CommodityIntraDay:                     addPrefixSuffixToBrokerage(constants.Space, constants.Percentage, brokerageDetails.IntraDayCommodity),
		IntraDay:                              addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.AllSegmentsCommonCharges),
		FnO:                                   addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.AllSegmentsCommonCharges),
		CurrencyAndCommodity:                  addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.AllSegmentsCommonCharges),
		UptoFiftyThousandCharges:              addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.OrderValueUptoFiftyThousandCharges),
		AboveFiftyThousandCharges:             addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, brokerageDetails.OrderValueAboveFiftyThousandCharges),
		OnlineTransferCharges:                 brokerageDetails.OnlineFundsTransferCharges,
		CommodityOption:                       commodityOption,
		IsCommodityOptionBrokerageFlatFee:     isCommodityOptionBrokerageFlatFee,
		CommodityOptionBrokeragePerLotPlan:    commodityOptionBrokeragePerLotPlan,
		CommodityOptionBrokeragePerLotFlatFee: commodityOptionBrokeragePerLotFlatFee,
	}

	if planName == constants.B2bAssistedPlanName {
		brokerageInformation.MinBrokeragePerShare = config.Referral().Brokerage.HybridPlan.MinBrokeragePerShare
		brokerageInformation.FixedEquityDelivery = brokerageInformation.EquityDelivery
		brokerageInformation.EquityDelivery = constants.Empty
	} else if planName == constants.ITradePrimeVBB {
		brokerageInformation.MinBrokeragePerShare = "NA"
	} else if planName == constants.ITradePremier {
		brokerageInformation.IsCommodityOptionBrokerageFlatFee = false
	}

	return brokerageInformation
}
