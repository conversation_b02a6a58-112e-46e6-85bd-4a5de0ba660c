package brokerage

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type BrokerageRepository interface {
	GetBrokerageDetails(ctx context.Context, brokeragePlans []business.BrokeragePlanDetailsInputData) ([]business.BrokerageDetails, error)
	GetCommodityOptionBrokerage(ctx context.Context, planName string) ([]business.CommodityOptions, error)
}

type ReferralRepository interface {
	GetReferralWithAppNumber(ctx context.Context, appNumber string) (*business.Referral, error)
	UpdatePlanName(ctx context.Context, referralDTO *business.Referral, source string) error
}

type BrokerageBusinessLogic struct {
	brokerageRepository BrokerageRepository
	referralRepository  ReferralRepository
}

func NewBrokerageBusinessLogic(brokerageRepository BrokerageRepository, referralRepository ReferralRepository) *BrokerageBusinessLogic {
	return &BrokerageBusinessLogic{
		referralRepository:  referralRepository,
		brokerageRepository: brokerageRepository,
	}
}
