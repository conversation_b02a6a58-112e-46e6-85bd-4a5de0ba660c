package brokerage

import (
	"strconv"
	"strings"

	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
)

func getFlatOptionDetails(commodityOption, planName string) string {
	if value, ok := config.Referral().Brokerage.CommodityOption[planName]; ok {
		return value
	}
	return addPrefixSuffixToBrokerage(constants.RUPEE, constants.Empty, commodityOption)
}

func addPrefixSuffixToBrokerage(prefix, suffix, value string) string {
	if _, err := strconv.ParseFloat(value, 32); err != nil {
		return value
	}
	return strings.TrimSpace(prefix + constants.Space + value + constants.Space + suffix)
}
