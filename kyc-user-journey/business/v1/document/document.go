package document

import (
	"bytes"
	"context"
	"fmt"
	"mime/multipart"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/models/business"

	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/kyc-user-journey/utils/pdf"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/constants"
	documentModel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (d *DocumentBusinessLogic) UploadGenericDocument(ctx context.Context, req *documentModel.DocumentUploadRequest) error {
	// get s3 file path.
	fileExtension := utils.GetMultipartFileExtension(req.Data.File)
	filePath := fmt.Sprintf("%s/%s/%s%s", req.AppNumber, req.Headers.Source, req.Data.Type, fileExtension)

	fileData, err := getFileDataFromFile(ctx, req)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericDocument: error getting file data for s3")
		return err
	}

	// Uploading file to S3.
	err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericDocument: error uploading file to s3")
		return constants.ErrUploadingFile.WithDetails([]string{req.Data.Type, err.Error()})
	}
	log.Info(ctx).Str("filePath", filePath).Msg("UploadGenericDocument - Uploaded generic proof to s3")

	// save in DB.
	documentDto := getDocumentDTO(req, filePath)
	err = d.documentRepository.Save(ctx, documentDto)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericDocument: error saving document in db")
		return err
	}

	return nil
}

func (d *DocumentBusinessLogic) UploadGenericNRIDocument(ctx context.Context, req *documentModel.NRIDocumentUploadRequest) error {
	s3Paths := make([]string, 0, constants.IntegerFive)

	suffix := constants.Empty
	if strings.EqualFold(req.Data.SubType, "aadhaar") {
		if req.Data.IsBackFile {
			suffix = "Back"
		} else {
			suffix = "Front"
		}
	}

	// Process file
	path, s3Paths, err := d.processAndUploadFile(ctx, req.Data.File, req.Data.Password, req.AppNumber, req.Data.Type, req.Data.SubType, suffix, s3Paths)
	if err != nil {
		return err
	}

	// for aadhaar file mask and re-upload.
	if strings.EqualFold(req.Data.SubType, "aadhaar") {
		// Fetch Hyperverge response.
		var resp []*modelsExternal.OCRAadharResponse
		resp, err = fetchHypervergeResponse(ctx, d.hypervergeExternalProvider, s3Paths, req.AppNumber)
		if err != nil {
			return err
		}
		log.Info(ctx).Str(constants.LogAppNumberKey, req.AppNumber).Interface("hyperVergeReponse", resp).Msg("UploadGenericNRIDocument: hyperVerge response")

		// Modify the responses received by Hyperverge.
		var aadhaarData *business.AadhaarData
		aadhaarData, err = d.commonAadhaarBusinessLogic.ModifyOcrResponse(resp)
		if err != nil {
			return err
		}
		log.Info(ctx).Str(constants.LogAppNumberKey, req.AppNumber).Interface("aadhaarData", aadhaarData).Msg("UploadGenericNRIDocument: aadhaar data")

		// Overwrite Aadhaar files in S3 with Masked Aadhaar files.
		// upload front
		if strings.HasSuffix(req.Data.DocumentKey, "_front") {
			_, err = d.commonAadhaarBusinessLogic.DownloadAndUploadMaskedAadhaar(ctx, path, aadhaarData.FrontData.URL, true)
			if err != nil {
				log.Error(ctx).Stack().Err(err).Msg("error while uploading masked aadhaar front image")
				return err
			}
		} else {
			_, err = d.commonAadhaarBusinessLogic.DownloadAndUploadMaskedAadhaar(ctx, path, aadhaarData.BackData.URL, false)
			if err != nil {
				log.Error(ctx).Stack().Err(err).Msg("error while uploading masked aadhaar back image")
				return err
			}
		}
	}

	// save in DB.
	documentDTO := getNRIDocumentDTO(req, path)
	err = d.nriDocumentRepository.Save(ctx, documentDTO)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadGenericNRIDocument: error saving document in db")
		return err
	}

	return nil
}

func (d *DocumentBusinessLogic) PreviewGenericNRIDocument(ctx context.Context, req *documentModel.NRIDocumentPreviewRequest) (*documentModel.NRIDocumentPreviewResponse, error) {
	// get s3 file path from table
	documentDTO, err := d.nriDocumentRepository.FetchDocumentByAppNumber(ctx, req.AppNumber, req.Data.Type, req.Data.SubType)
	if err != nil {
		log.Error(ctx).Err(err).Msg("PreviewGenericNRIDocument: error getting nri document details from db")
		return nil, err
	}
	// get s3 signed url
	var frontSignedURL, backSignedURL string
	if documentDTO.FrontPath != constants.Empty {
		frontSignedURL, err = getSignedS3URL(ctx, documentDTO.FrontPath)
		if err != nil {
			log.Error(ctx).Err(err).Msg("PreviewGenericNRIDocument: error getting S3 signed url for nri document")
			return nil, err
		}
	}

	if documentDTO.BackPath != constants.Empty {
		backSignedURL, err = getSignedS3URL(ctx, documentDTO.BackPath)
		if err != nil {
			log.Error(ctx).Err(err).Msg("PreviewGenericNRIDocument: error getting S3 signed url for nri document back")
			return nil, err
		}
	}
	// do it for back file if present
	return &documentModel.NRIDocumentPreviewResponse{
		FrontSignedURL: frontSignedURL,
		BackSignedURL:  backSignedURL,
	}, nil
}

func (d *DocumentBusinessLogic) UploadMultipleDocuments(ctx context.Context, req *documentModel.MultipleDocumentUploadRequest, types, passwords []string) error {
	var password string

	// get s3 file path.
	for i, file := range req.Data.File {
		fileExtension := utils.GetMultipartFileExtension(file)
		filePath := fmt.Sprintf("%s/%s/%s%s", req.AppNumber, constants.DerivativesProof, types[i], fileExtension)

		if len(passwords) > i {
			password = passwords[i]
		} else {
			password = ""
		}

		fileData, err := getFileDataFromMultipleFile(ctx, file, fileExtension, password)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultipleDocument: error getting file data for s3")
			return err
		}

		// Uploading file to S3.
		err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadGenericDocument: error uploading file to s3")
			return constants.ErrUploadingFile.WithDetails([]string{req.Data.Type, err.Error()})
		}
		log.Info(ctx).Str("filePath", filePath).Msg("UploadMultipleDocument - Uploaded generic proof to s3")

		// save in DB.
		documentDto := getMultiDocumentDTO(req.AppNumber, types[i], req.Source, filePath)
		err = d.documentRepository.Save(ctx, documentDto)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultipleDocument: error saving document in db")
			return err
		}
	}

	return nil
}
func (d *DocumentBusinessLogic) UploadMultiplePhysicalDocuments(ctx context.Context, req *documentModel.PhysicalDocumentUploadRequest, passwords []string) error {
	var password string
	folderPrefix := fmt.Sprintf("%s/%s/", req.AppNumber, "proofs")

	objectKeys, err := s3.GetS3Client().ListObjectsWithPrefix(ctx, folderPrefix)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("UploadMultiplePhysicalDocuments: error checking for preexisting S3 objects for deletion")
		return err
	}

	if len(objectKeys) > 0 {
		err := s3.GetS3Client().Delete(ctx, objectKeys)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultiplePhysicalDocuments: error deleting existing files from S3")
			return err
		}
		log.Info(ctx).Int("deleted_files", len(objectKeys)).Msg("UploadMultiplePhysicalDocuments: Deleted existing files before upload")
	}

	for i, file := range req.Data.File {
		fileExtension := utils.GetMultipartFileExtension(file)
		var proofID string
		if req.Data.Type == constants.CmlForm {
			proofID = constants.CMLFile
		} else {
			proofID = fmt.Sprintf("proof_%d", i+1)
		}
		filePath := fmt.Sprintf("%s/%s/%s%s", req.AppNumber, "proofs", proofID, fileExtension) // Hardcoded "proofs"

		if len(passwords) > i {
			password = passwords[i]
		} else {
			password = ""
		}

		fileData, err := getFileDataFromMultipleFile(ctx, file, fileExtension, password)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultiplePhysicalDocuments: error getting file data for S3")
			return err
		}

		err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultiplePhysicalDocuments: error uploading file to S3")
			return constants.ErrUploadingFile.WithDetails([]string{req.Data.Type, err.Error()})
		}
		log.Info(ctx).Str("filePath", filePath).Msg("UploadMultiplePhysicalDocuments - Uploaded physical proof to S3")

		documentDto := getMultiDocumentDTO(req.AppNumber, proofID, req.Source, filePath)
		err = d.documentRepository.Save(ctx, documentDto)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("UploadMultiplePhysicalDocuments: error saving document in DB")
			return err
		}
	}
	return nil
}

func (d *DocumentBusinessLogic) processAndUploadFile(ctx context.Context, file *multipart.FileHeader, password, appNumber, docType, subType, suffix string, s3Paths []string) (string, []string, error) {
	// Get file extension and path
	fileExtension := utils.GetMultipartFileExtension(file)
	filePath := fmt.Sprintf("%s/%s/%s%s%s%d%s", appNumber, docType, subType, suffix, "-", time.Now().Unix(), fileExtension)

	// Read file data
	fileData, err := getFileDataFromFileGeneric(ctx, file)
	if err != nil {
		log.Error(ctx).Err(err).Msg("processAndUploadFile: Error reading file data")
		return filePath, s3Paths, err
	}

	if fileExtension == constants.PDFExtension {
		// Check if file is password-protected
		isPasswordProtected, passErr := pdf.IsPasswordProtectedPDF(fileData)
		if passErr != nil {
			log.Error(ctx).Err(passErr).Msg("processAndUploadFile: Error checking if file is password-protected")
			return filePath, s3Paths, passErr
		}

		if isPasswordProtected {
			if password == constants.Empty {
				log.Warn(ctx).Msg("processAndUploadFile: Missing password for protected file")
				return filePath, s3Paths, constants.ErrMissingFilePassword.Value()
			}
			// Decrypt file
			unlockedFile, decryptErr := pdf.DecryptPDFData(fileData, password)
			if decryptErr != nil {
				log.Error(ctx).Err(decryptErr).Msg("processAndUploadFile: Error decrypting file")
				if decryptErr.Error() == constants.ErrPdfCPUIncorrectPassword.Error() {
					return filePath, s3Paths, constants.ErrIncorrectFilePassword.Value()
				}
				return filePath, s3Paths, decryptErr
			}
			// use decrypted file
			fileData = unlockedFile
		}
	}

	// Upload to S3
	err = s3.GetS3Client().Upload(ctx, filePath, bytes.NewReader(fileData))
	if err != nil {
		log.Error(ctx).Err(err).Msg("processAndUploadFile: Error uploading file to S3")
		return filePath, s3Paths, constants.ErrUploadingFile.Value()
	}

	if strings.EqualFold(subType, "aadhaar") {
		s3Paths = append(s3Paths, filePath)
	}

	log.Info(ctx).Str("filePath", filePath).Msg("processAndUploadFile: File uploaded successfully")
	return filePath, s3Paths, nil
}
