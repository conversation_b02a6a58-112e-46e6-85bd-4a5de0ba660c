package document

import (
	"context"

	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/kyc-user-journey/models/business"
)

type DocumentRepository interface {
	Save(ctx context.Context, documentDto *business.Document) error
}

type NRIDocumentRepository interface {
	Save(ctx context.Context, nriDocumentDTO *business.NRIDocument) error
	FetchDocumentByAppNumber(ctx context.Context, appNumber, docType, subType string) (*business.NRIDocument, error)
}

type HypervergeExternalProvider interface {
	FetchAadharOcr(ctx context.Context, request *modelsV1.OCRAadharRequest) (*modelsV1.OCRAadharResponse, error)
}

type CommonAadhaarBusinessLogic interface {
	ModifyOcrResponse(responses []*modelsV1.OCRAadharResponse) (*business.AadhaarData, error)
	DownloadAndUploadMaskedAadhaar(ctx context.Context, s3Path, downloadURL string, isFront bool) (string, error)
}

type DocumentBusinessLogic struct {
	documentRepository         DocumentRepository
	nriDocumentRepository      NRIDocumentRepository
	hypervergeExternalProvider HypervergeExternalProvider
	commonAadhaarBusinessLogic CommonAadhaarBusinessLogic
}

func NewDocumentBusinessLogic(documentRepo DocumentRepository, nriDocumentRepository NRIDocumentRepository,
	hypervergeExternalProvider HypervergeExternalProvider, commonAadhaarBusinessLogic CommonAadhaarBusinessLogic) *DocumentBusinessLogic {
	return &DocumentBusinessLogic{
		documentRepository:         documentRepo,
		nriDocumentRepository:      nriDocumentRepository,
		hypervergeExternalProvider: hypervergeExternalProvider,
		commonAadhaarBusinessLogic: commonAadhaarBusinessLogic,
	}
}
