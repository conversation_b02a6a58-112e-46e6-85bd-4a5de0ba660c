package document

import (
	"context"
	"io"
	"mime/multipart"
	"time"

	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"

	"github.com/angel-one/kyc-user-journey/utils/pdf"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/constants"
	docModel "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
)

func getFileDataFromFile(ctx context.Context, req *docModel.DocumentUploadRequest) ([]byte, error) {
	file, err := req.Data.File.Open()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFile: error opening file")
		return nil, constants.ErrOpeningFile.WithDetails(err.Error())
	}
	fileData, err := io.ReadAll(file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFile: error reading file")
		return nil, constants.ErrReadingFile.WithDetails(err.Error())
	}
	return fileData, nil
}

func getFileDataFromFileGeneric(ctx context.Context, req *multipart.FileHeader) ([]byte, error) {
	file, err := req.Open()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFileGeneric: error opening file")
		return nil, constants.ErrOpeningFile.WithDetails(err.Error())
	}
	fileData, err := io.ReadAll(file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFileGeneric: error reading file")
		return nil, constants.ErrReadingFile.WithDetails(err.Error())
	}
	return fileData, nil
}

func getFileDataFromMultipleFile(ctx context.Context, inputFile *multipart.FileHeader, fileExtension, password string) ([]byte, error) {
	file, err := inputFile.Open()
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFileForIncomeProof: error opening file")
		return nil, constants.ErrOpeningFile.WithDetails(err.Error())
	}
	fileData, err := io.ReadAll(file)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getFileDataFromFileForIncomeProof: error reading file")
		return nil, constants.ErrReadingFile.WithDetails(err.Error())
	}
	// check and unlock pdf using password.
	if fileExtension == constants.PDFExtension {
		isPasswordProtected, passErr := pdf.IsPasswordProtectedPDF(fileData)
		if passErr != nil {
			log.Error(ctx).Stack().Err(passErr).Msg("getFileDataFromFileForIncomeProof: error checking if password protected")
			return nil, passErr
		}
		if isPasswordProtected {
			if password == constants.Empty {
				log.Warn(ctx).Stack().Msg("getFileDataFromFileForIncomeProof: empty password")
				return nil, constants.ErrMissingFilePassword.Value()
			}
			// decrypt file
			unlockedFile, decryptErr := pdf.DecryptPDFData(fileData, password)
			if decryptErr != nil {
				log.Error(ctx).Stack().Err(decryptErr).Msg("getFileDataFromFileForIncomeProof: error decrypting pdf file")
				return nil, decryptErr
			}
			// use decrypted file
			fileData = unlockedFile
		}
	}
	return fileData, nil
}

func getDocumentDTO(req *docModel.DocumentUploadRequest, filePath string) *business.Document {
	return &business.Document{
		AppNumber:        req.AppNumber,
		Type:             req.Data.Type,
		Path:             filePath,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
}

func getNRIDocumentDTO(req *docModel.NRIDocumentUploadRequest, path string) *business.NRIDocument {
	if req.Data.IsBackFile {
		return &business.NRIDocument{
			AppNumber:        req.AppNumber,
			Type:             req.Data.Type,
			SubType:          req.Data.SubType,
			BackPath:         path,
			CreateSource:     req.Source,
			LastUpdateSource: req.Source,
			IsBackFile:       req.Data.IsBackFile,
		}
	}
	return &business.NRIDocument{
		AppNumber:        req.AppNumber,
		Type:             req.Data.Type,
		SubType:          req.Data.SubType,
		FrontPath:        path,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
}

func getMultiDocumentDTO(appNumber, fileType, source, filePath string) *business.Document {
	return &business.Document{
		AppNumber:        appNumber,
		Type:             fileType,
		Path:             filePath,
		CreateSource:     source,
		LastUpdateSource: source,
	}
}

func getSignedS3URL(ctx context.Context, path string) (string, error) {
	client := s3.GetS3Client()
	signedURL, err := client.GetSignedURL(ctx, path, time.Duration(config.S3().ESignPdfURLExpiryInSeconds)*time.Second)
	if err != nil {
		return constants.Empty, err
	}
	return signedURL, nil
}

func fetchHypervergeResponse(ctx context.Context, provider HypervergeExternalProvider, s3Paths []string, appNumber string) ([]*modelsExternal.OCRAadharResponse, error) {
	var resp = make([]*modelsExternal.OCRAadharResponse, 0, constants.IntegerFive)

	for _, p := range s3Paths {
		signedPath, err := getSignedS3URL(ctx, p)
		if err != nil {
			return nil, err
		}
		req := modelsExternal.OCRAadharRequest{URL: signedPath, AppNumber: appNumber}
		r, err := provider.FetchAadharOcr(ctx, &req)
		if err != nil {
			return nil, err
		}
		resp = append(resp, r)
	}
	return resp, nil
}
