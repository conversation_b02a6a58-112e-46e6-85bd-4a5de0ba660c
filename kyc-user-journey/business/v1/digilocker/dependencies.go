package digilocker

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type DigilockerRepository interface {
	UpsertDigilockerEntities(ctx context.Context, poaDTO *business.POA, permanentAddressDTO *business.Address, correspondenceAddressDTO *business.Address, panDTO *business.Pan, panDocDTO *business.PanDoc, personalDTO *business.Personal, isPanRecordExists bool) error
}

type StartRepository interface {
	GetEntityForAppNumber(appNumber string) (business.Start, error)
}

type PanDocRepository interface {
	GetPANDocWithAppNumberAndSource(appNumber, source string) (business.PanDoc, error)
	FreshUpsert(ctx context.Context, panDocDTO *business.PanDoc) error
}

type PanRepository interface {
	GetPanDTOWithAppNumber(ctx context.Context, appNumber string) (*business.Pan, error)
	CheckAppNumberExists(appNumber string) (bool, error)
}

type NsdlExternalProvider interface {
	ValidatePAN(ctx context.Context, request modelsV1.NSDLPanValidationRequest) (*modelsV1.NSDLPanValidationResponse, error)
	ValidatePANV2(ctx context.Context, request *modelsV1.NSDLVerifyPanV2Request, userName string) (*modelsV1.ValidatePanWithNSDLV2FinalResponse, error)
}

type DigilockerBusinessLogicStruct struct {
	digilockerRepository DigilockerRepository
	startRepository      StartRepository
	panDocRepository     PanDocRepository
	panRepository        PanRepository
	nsdlExternalProvider NsdlExternalProvider
}

func NewDigilockerBusinessLogic(digilockerRepository DigilockerRepository, startRepository StartRepository, panDocRepository PanDocRepository, panRepository PanRepository, nsdlExternalProvider NsdlExternalProvider) *DigilockerBusinessLogicStruct {
	return &DigilockerBusinessLogicStruct{
		digilockerRepository: digilockerRepository,
		startRepository:      startRepository,
		panDocRepository:     panDocRepository,
		panRepository:        panRepository,
		nsdlExternalProvider: nsdlExternalProvider,
	}
}
