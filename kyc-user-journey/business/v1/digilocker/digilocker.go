package digilocker

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/entities"
	"github.com/angel-one/kyc-user-journey/externals"
	apiModelsV1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals" // externals
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/nsdl"
	"github.com/angel-one/kyc-user-journey/utils/security"
)

func (d *DigilockerBusinessLogicStruct) InitiateDigilockerSignInRoute(ctx context.Context, req *apiModelsV1.DigilockerAPIRequest) (*modelsV1.DigilockerSignInResponseData, error) {
	metadata := business.MetadataStruct{
		Source:  req.Headers.Source,
		AgentID: req.Headers.AgentID,
		Mobile:  req.Meta.Mobile,
	}

	metadataEnc, err := handleMetadataEncryption(&metadata)
	if err != nil {
		log.ErrorWarn(ctx, constants.ErrAESEncryption.WithMessage("InitiateDigilockerSignInRoute: Error in Encrypting query params"))
		return nil, err
	}

	response, err := externals.CallDigilockerSignIn(ctx, req.Meta.AppNumber, req.Meta.Mobile, req.Headers.Source, metadataEnc)
	if err != nil {
		log.Error(ctx).Err(err).Msg("InitiateDigilockerSignInRoute: Failed to get response from external service")
		return nil, err
	}
	log.Info(ctx).Interface(constants.LogResponseKey, response).Msg("InitiateDigilockerSignInRoute: Received successful response from Digilocker")
	return &response.Data, nil
}

// Save To S3 And Upsert Record In Db.
func (d *DigilockerBusinessLogicStruct) HandleWebhookDigilocker(ctx context.Context, request *modelsV1.DigilockerCallbackResponseSuccess) (*modelsV1.WebhookResponse, error) {
	startDTO, err := d.startRepository.GetEntityForAppNumber(request.AssetID)
	if err != nil {
		return getErrorResponse(), err
	}

	if isApplicationStatusComplete(&startDTO) {
		log.Info(ctx).Msgf("HandleWebhookDigilocker: for AppNumber %s Application status is %d", startDTO.AppNumber, startDTO.ApplicationStatus)
		return &modelsV1.WebhookResponse{Status: constants.Success}, nil
	}

	aadharXML, xmlBytes, err := decryptXMLResponseToCertAndBytes(request.EncAaadhar)
	if err != nil {
		log.Error(ctx).Err(err).Msg("HandleWebhookDigilocker: Failed to decrypt XML response")
		return getErrorResponse(), err
	}

	// validate the date of birth.
	aadharDOB := utils.CleanDOB(aadharXML.CertificateData.KycRes.UIDData.Poi.Dob)
	if err = utils.IsDOBValid(aadharDOB, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge); err != nil {
		log.Error(ctx).Stack().Err(err).
			Str("dob", aadharDOB).
			Bool("invalidDOB", true).
			Msg("HandleWebhookDigilocker: invalid DOB")
		return getErrorResponse(), constants.ErrInvalidDateOfBirth
	}

	permanentAddress, correspondenceAddress, poa := getPOAAndAddressDTOs(aadharXML, request.AssetID, request.Source)

	var panDTO *business.Pan
	var panDoc business.PanDoc
	personalDTO := getPersonalDTOFromAadharXML(request, aadharXML)
	panRecordExists, err := d.panRepository.CheckAppNumberExists(request.AssetID)
	if err != nil {
		return getErrorResponse(), err
	}
	// pan record may or may not exists for onekyc journey
	if panRecordExists {
		panDoc, err = d.panDocRepository.GetPANDocWithAppNumberAndSource(request.AssetID, constants.AngelDigilockerSource)
		if err != nil {
			return getErrorResponse(), err
		}

		panDTO, err = d.panRepository.GetPanDTOWithAppNumber(ctx, request.AssetID)
		if err != nil {
			return getErrorResponse(), err
		}

		// if either of is_name_match or is_dob_match of pan table is false then validate with NSDL
		if !panDTO.IsNameMatch || !panDTO.IsDOBMatch {
			aadharName := aadharXML.CertificateData.KycRes.UIDData.Poi.Name
			d.ValidateAndUpdateDigilockerPOADetailsWithNSDLResponse(ctx, &panDoc, panDTO, aadharName, aadharDOB)
		}

		if personalDTO.GuardianName != constants.Empty {
			panDTO.FatherName = personalDTO.GuardianName
			panDTO.GuardianType = personalDTO.GuardianType

			panDoc.FatherName = personalDTO.GuardianName
			panDoc.GuardianType = personalDTO.GuardianType
		}
	}

	// upload aadhaar xml to s3
	xmlFilePath := poa.SecondPath
	s3err := s3.GetS3Client().Upload(ctx, xmlFilePath, bytes.NewReader(xmlBytes))
	if s3err != nil {
		log.Error(ctx).Stack().Err(s3err).Msg("HandleWebhookDigilocker: error uploading aadhaar xml to s3")
		return getErrorResponse(), s3err
	}

	// Upload aadhaar photo to s3
	s3err = s3.GetS3Client().UploadBase64File(ctx, poa.PhotoPath, []byte(aadharXML.CertificateData.KycRes.UIDData.Pht))
	if s3err != nil {
		log.Error(ctx).Stack().Err(s3err).Msg("HandleWebhookDigilocker: error uploading aadhaar photo to s3")
		return getErrorResponse(), s3err
	}

	// save path to table
	err = d.digilockerRepository.UpsertDigilockerEntities(ctx, &poa, &permanentAddress, &correspondenceAddress, panDTO, &panDoc, personalDTO, panRecordExists)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("HandleWebhookDigilocker: error saving records in DB")
		return getErrorResponse(), err
	}

	return &modelsV1.WebhookResponse{Status: constants.Success}, nil
}

func (d *DigilockerBusinessLogicStruct) GetRedirectionURL(ctx context.Context, status, metadataEnc string) string {
	metadata, err := handleMetadataDecryption(metadataEnc)
	if err != nil {
		log.ErrorWarn(ctx, constants.ErrAESDecryption.WithMessage("GetRedirectionURL: Error in Decrypting query params"))
		return config.Digilocker().OneKycRedirectURL
	}

	source := metadata.Source
	if slices.Contains(config.Application().Validations.AllowedAdminSources, source) { // kyc-admin (oneKyc)
		mobile := utils.HashUpperCaseWithSHA256(metadata.Mobile)
		agentID := metadata.AgentID

		if status == constants.Success {
			return getSuccessRedirectionURL(mobile, agentID)
		} else {
			return getErrorRedirectionURL(mobile, agentID)
		}
	}
	if slices.Contains(config.Application().Validations.AllowedSources, source) {
		if status == constants.Success {
			return getSuccessRedirectionURLforUJ()
		} else {
			return getErrorRedirectionURLforUJ()
		}
	}

	return config.Digilocker().ErrorRedirectURL
}

func decryptXMLResponseToCertAndBytes(encryptedData string) (*modelsV1.Certificate, []byte, error) {
	aesKey := []byte(config.Digilocker().AesKeyAadhaar)
	decryptedData, err := security.DecryptAesForURLEncodedBase64(aesKey, encryptedData)
	if err != nil {
		return nil, nil, err
	}
	xmlBytes := []byte(decryptedData)

	var xmlData modelsV1.Certificate
	err = xml.Unmarshal([]byte(decryptedData), &xmlData)
	if err != nil {
		return nil, nil, err
	}

	return &xmlData, xmlBytes, nil
}

func getPOAAndAddressDTOs(aadhaarDetails *modelsV1.Certificate, appNumber, source string) (business.Address, business.Address, business.POA) {
	poa := business.POA{
		AppNumber: appNumber,
		ID:        aadhaarDetails.CertificateData.KycRes.UIDData.UID,
		FullName:  strings.TrimSpace(aadhaarDetails.CertificateData.KycRes.UIDData.Poi.Name),
		Type:      constants.DigilockerPOA,
		FirstPath: fmt.Sprintf("%s/%s/%s%s", appNumber, constants.AngelDigilockerFolderName,
			constants.AadhaarDigilockerPhotoFilename, constants.AadhaarDigilockerPhotoFileExt),
		SecondPath: fmt.Sprintf("%s/%s/%s%s", appNumber, constants.AngelDigilockerFolderName,
			constants.AadhaarDigilockerXMLFilename, constants.AadhaarDigilockerXMLFileExt),
		Dob:                     utils.CleanDOB(strings.TrimSpace(aadhaarDetails.CertificateData.KycRes.UIDData.Poi.Dob)),
		Source:                  constants.AngelDigilockerSource,
		Gender:                  utils.FormatGenderDigilocker(aadhaarDetails.CertificateData.KycRes.UIDData.Poi.Gender),
		AreAadhaarNumbersMasked: true,
		CreateSource:            source,
		LastUpdateSource:        source,
	}
	poa.PhotoPath = poa.FirstPath

	city := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist, " ")))
	state := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(strings.ReplaceAll(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State, "&", "AND"), " ")))

	permanentAddress := business.Address{
		AppNumber: appNumber,
		Type:      constants.PermanentAddressType,
		AddressLine1: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.House,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Street),
		AddressLine2: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Loc,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc),
		AddressLine3: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State),
		City:             city,
		State:            state,
		Country:          aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Country,
		Pincode:          aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Pc,
		CreateSource:     source,
		LastUpdateSource: source,
	}
	correspondenceAddress := permanentAddress
	correspondenceAddress.Type = constants.CorrespondenceAddressType
	return permanentAddress, correspondenceAddress, poa
}

func (d *DigilockerBusinessLogicStruct) ValidateAndUpdateDigilockerPOADetailsWithNSDLResponse(ctx context.Context, panDoc *business.PanDoc, panDTO *business.Pan, aadharName, aadharDob string) {
	// NSDL PanDetails Validation
	if panDTO.Pan == constants.Empty {
		return
	}
	name := aadharName
	dob := aadharDob
	if name == constants.Empty {
		name = constants.NsdlDummyName
	}

	if dob == constants.Empty {
		dob = constants.NsdlDummyDoB
	}

	inputData := modelsV1.NSDLVerifyPanV2InputData{
		Pan:  panDTO.Pan,
		Name: name,
		DOB:  dob,
	}

	if !panDTO.IsDOBMatch {
		panDTO.DOB = constants.Empty
		panDoc.DOB = constants.Empty
	}
	if !panDTO.IsNameMatch {
		panDoc.FullName = constants.Empty
		panDTO.FullName = constants.Empty
	}

	nsdlResponse, err := d.nsdlExternalProvider.ValidatePANV2(ctx, &modelsV1.NSDLVerifyPanV2Request{InputData: []modelsV1.NSDLVerifyPanV2InputData{inputData}}, nsdl.RoundRobinV2.Next())

	if err != nil {
		log.Error(ctx).Err(err).Msg("Error from NSDL V2 API")
		return
	}

	if len(nsdlResponse.Data) == 0 {
		log.Error(ctx).Msg("Empty response from NSDL V2 API")
		return
	}

	if nsdlResponse.Data[0].ISDoBMatch {
		panDoc.DOB = dob
		panDTO.DOB = panDoc.DOB
		panDTO.IsDOBMatch = true
		panDTO.DOBMatchSource = "AADHAAR"
	}

	if nsdlResponse.Data[0].IsNameMatch {
		panDoc.FullName = name
		panDTO.FullName = panDoc.FullName
		panDTO.IsNameMatch = true
		panDTO.NameMatchSource = "AADHAAR"
	}
}

func getPersonalDTOFromAadharXML(request *modelsV1.DigilockerCallbackResponseSuccess, aadhaarDetails *modelsV1.Certificate) *business.Personal {
	personal := &business.Personal{
		AppNumber:        request.AssetID,
		CreateTS:         time.Time{},
		CreateSource:     request.Source,
		LastUpdateTS:     time.Time{},
		LastUpdateSource: request.Source,
	}
	aadhaarCareOf := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Co
	var guardianName string
	if strings.Contains(aadhaarCareOf, "/") { // only when guardian's name contains s/o, d/o, c/o
		co := strings.Split(aadhaarCareOf, " ")
		guardianName = strings.TrimSpace(strings.ToUpper(strings.Join(co[1:], " ")))
	}
	if !utils.IsValidAgainstRegex(config.Application().Validations.NameRegex, guardianName) {
		guardianName = constants.Empty
	}
	personal.GuardianName = guardianName
	personal.GuardianType = "F"
	return personal
}

func isApplicationStatusComplete(startDTO *business.Start) bool {
	return startDTO.ApplicationStatus == entities.StatusFulfilmentDone || startDTO.ApplicationStatus == entities.StatusFulfilmentInProgress || startDTO.ApplicationStatus == entities.StatusESignDone
}

func getErrorResponse() *modelsV1.WebhookResponse {
	return &modelsV1.WebhookResponse{Status: constants.Failure}
}
