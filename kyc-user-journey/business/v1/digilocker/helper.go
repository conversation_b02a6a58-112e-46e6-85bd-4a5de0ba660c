package digilocker

import (
	"encoding/json"
	"fmt"

	"net/url"

	utils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils/security"
)

func handleMetadataEncryption(state *business.MetadataStruct) (string, error) {
	stateJSON, err := utils.MarshalJSON(state)
	if err != nil {
		return constants.Empty, err
	}
	aesKeyMetadata := config.Digilocker().AesKeyMetadata
	stateEnc, err := security.EncryptAesForURLEncodedBase64([]byte(aesKeyMetadata), string(stateJSON))
	if err != nil {
		return constants.Empty, err
	}
	return stateEnc, nil
}

func handleMetadataDecryption(message string) (*business.MetadataStruct, error) {
	if message == constants.Empty {
		return nil, constants.ErrEmptyMetadata.Value()
	}

	decryptedStateString, err := security.DecryptAesForURLEncodedBase64([]byte(config.Digilocker().AesKeyMetadata), message)
	if err != nil {
		return nil, err
	}
	var decryptedStateJSON business.MetadataStruct

	if err := json.Unmarshal([]byte(decryptedStateString), &decryptedStateJSON); err != nil {
		return nil, err
	}
	return &decryptedStateJSON, nil
}

func getSuccessRedirectionURL(mobile, agentID string) string {
	query := url.Values{}

	// adding necessary params if the call was made from admin
	query.Add(constants.MobileQueryParamKey, mobile)
	query.Add(constants.AgentIDQueryParamKey, agentID)
	query.Add(constants.StatusQueryParamKey, constants.SuccessStatus)

	redirectURL := config.Digilocker().OneKycRedirectURL
	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return redirectURLWithQueryParams
}

func getErrorRedirectionURL(mobile, agentID string) string {
	query := url.Values{}

	// adding necessary params if the call was made from admin
	query.Add(constants.MobileQueryParamKey, mobile)
	query.Add(constants.AgentIDQueryParamKey, agentID)
	query.Add(constants.StatusQueryParamKey, constants.ErrorStatus)

	redirectURL := config.Digilocker().OneKycRedirectURL
	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return redirectURLWithQueryParams
}

func getSuccessRedirectionURLforUJ() string {
	query := url.Values{}
	query.Add("status", "success")
	redirectURL := config.Digilocker().UserJourneyRedirectURL
	return fmt.Sprintf("%s?%s", redirectURL, query.Encode())
}

func getErrorRedirectionURLforUJ() string {
	query := url.Values{}
	query.Add("status", "error")
	redirectURL := config.Digilocker().UserJourneyRedirectURL
	return fmt.Sprintf("%s?%s", redirectURL, query.Encode())
}
