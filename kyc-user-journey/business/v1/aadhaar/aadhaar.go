package aadhaar

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/angel-one/kyc-user-journey/models/business"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsV1API "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (a *AadharBusinessLogic) AadhaarOcr(ctx context.Context, request *modelsV1API.AadhaarOcrRequest) (*modelsV1API.AadhaarResponseData, error) {
	// Upload Aadhaar Files to S3.
	s3Paths, err := uploadAadharFiles(ctx, request)
	if err != nil {
		return nil, err
	}

	// Fetch Hyperverge response.
	resp, err := fetchHypervergeResponse(ctx, a.hypervergeExternalProvider, s3Paths, request.AppNumber)
	if err != nil {
		go a.prismSQSBusinessLogic.SendMessageToQueue(ctx, a.commonPrismEventMessage.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.OCR, err.Error()))
		return nil, err
	}

	// Modify the responses received by Hyperverge.
	aadhaarData, err := modifyOcrResponse(resp)
	if err != nil {
		return nil, err
	}

	if aadhaarData.FrontData.Face != constants.Empty {
		photoPath := fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.AadharFlowName,
			constants.AadhaarDigilockerPhotoFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerPhotoFileExt)
		// Upload Aadhaar Face to S3.
		err = uploadAadharPhoto(ctx, photoPath, aadhaarData.FrontData.Face)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("error while uploading aadhaar face")
			return nil, err
		}
		s3Paths = append(s3Paths, photoPath)
	}

	pincodeData, err := a.pincodeRepository.FetchRecordByPincode(ctx, aadhaarData.BackData.PinCode)
	if err != nil {
		return nil, err
	}
	aadhaarData.BackData.State = pincodeData.State
	aadhaarData.BackData.City = pincodeData.City
	aadhaarData.S3Paths = s3Paths
	ocrDTO, err := getOcrDTOFromAadhaarOCRResponse(aadhaarData, request.AppNumber)
	if err != nil {
		return nil, err
	}
	err = a.ocrRepository.Save(ctx, ocrDTO)
	if err != nil {
		return nil, err
	}

	// Overwrite Aadhaar files in S3 with Masked Aadhaar files.
	var signedURLBack string
	signedURLFront, err := downloadAndUploadMaskedAadhaar(ctx, s3Paths, 0, aadhaarData.FrontData.URL)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("error while uploading masked aadhaar front image")
		go a.prismSQSBusinessLogic.SendMessageToQueue(ctx, a.commonPrismEventMessage.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.OCR, err.Error()))
		return nil, err
	}
	if len(s3Paths) >= 2 {
		signedURLBack, err = downloadAndUploadMaskedAadhaar(ctx, s3Paths, 1, aadhaarData.BackData.URL)
		if err != nil {
			go a.prismSQSBusinessLogic.SendMessageToQueue(ctx, a.commonPrismEventMessage.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.OCR, err.Error()))
			log.Error(ctx).Stack().Err(err).Msg("error while uploading masked aadhaar back image")
			return nil, err
		}
	}

	return &modelsV1API.AadhaarResponseData{Aadhaar: maskAadhaar(aadhaarData.FrontData.AadhaarNo),
		AadhaarFullName: aadhaarData.FrontData.Name,
		State:           aadhaarData.BackData.State,
		Pincode:         aadhaarData.BackData.PinCode,
		AddressLine1:    aadhaarData.BackData.AddressOne,
		AddressLine2:    aadhaarData.BackData.AddressTwo,
		City:            aadhaarData.BackData.City,
		PoaType:         constants.AadhaarPOAType,
		Gender:          getGenderValueForAadhaar(aadhaarData.FrontData.Gender),
		AadhaarFrontURL: signedURLFront,
		AadhaarBackURL:  signedURLBack,
	}, nil
}

func (a *AadharBusinessLogic) GetPOAData(ctx context.Context, request *modelsV1API.GetPOARequest) (*modelsV1API.GetPOAResponse, error) {
	kraData, err := a.cvlKraRepository.GetKRADataByAppNumber(ctx, request.AppNumber)
	if err != nil {
		return nil, err
	}

	if isEligibleForPOASkip(kraData) && isKRACompliant(kraData) && isKRADataMatchWithKYCData(kraData) && isAddressPrefilledWithKRAData(kraData) {
		return &modelsV1API.GetPOAResponse{SkipPOA: true}, nil
	}

	if kraData.PoaFirstPath == constants.Empty {
		return &modelsV1API.GetPOAResponse{SkipPOA: false}, nil
	}

	getPOAResponse := &modelsV1API.GetPOAResponse{
		SkipPOA:      false,
		AddressLine1: kraData.PermanentAddressLine1,
		AddressLine2: kraData.PermanentAddressLine2,
		Pincode:      kraData.PermanentPincode,
		Aadhaar:      maskAadhaar(kraData.PoaID),
	}

	frontURL, err := getSignedS3Path(ctx, kraData.PoaFirstPath)
	if err != nil {
		return &modelsV1API.GetPOAResponse{SkipPOA: false}, err
	}

	getPOAResponse.FrontURL = frontURL
	var backURL string
	if kraData.PoaSecondPath != constants.Empty {
		backURL, err = getSignedS3Path(ctx, kraData.PoaSecondPath)
		if err != nil {
			return &modelsV1API.GetPOAResponse{SkipPOA: false}, err
		}
	}

	getPOAResponse.BackURL = backURL
	return getPOAResponse, nil
}

func (a *AadharBusinessLogic) AadhaarConfirm(ctx context.Context, req *modelsV1API.AadhaarConfirmRequest) (*modelsV1API.AadhaarResponseData, error) {
	aadhaarAndOCRData, err := a.poaRepository.GetPoaAndOCRData(ctx, req.AppNumber)
	if err != nil {
		return nil, err
	}

	if aadhaarAndOCRData.AppNumber == constants.Empty && aadhaarAndOCRData.OCRData == constants.Empty {
		return nil, constants.ErrOCRNotPerformed.Value()
	}
	var photoPath, photoURL string
	pincodeData, err := a.pincodeRepository.FetchRecordByPincode(ctx, req.Data.Pincode)
	if err != nil {
		return nil, err
	}
	if pincodeData.Pincode == constants.Empty {
		return nil, constants.ErrInvalidPincode.Value()
	}

	if aadhaarAndOCRData.OCRData == constants.Empty {
		poaDTO := &business.POA{
			ID: maskAadhaar(req.Data.Aadhaar),
		}

		addressDTO := &business.Address{
			AddressLine1: req.Data.AddressLine1,
			AddressLine2: req.Data.AddressLine2,
			City:         pincodeData.City,
			State:        pincodeData.State,
			Pincode:      req.Data.Pincode,
		}
		err = a.poaRepository.UpdateAadhaarAndAddress(ctx, addressDTO, poaDTO, req.AppNumber, req.Source)
		if err != nil {
			return nil, err
		}
	} else {
		var aadhaarData business.AadhaarData
		err = json.Unmarshal([]byte(aadhaarAndOCRData.OCRData), &aadhaarData)
		if err != nil {
			return nil, err
		}

		// Overriding state, city if user edits pincode.
		aadhaarData.BackData.State = pincodeData.State
		aadhaarData.BackData.City = pincodeData.City

		// Fetch data to upsert to DB.
		poa, addresses := getAadhaarOCREntitiesToUpsert(ctx, req, &aadhaarData)

		photoPath = poa.PhotoPath
		// Upsert POA.
		err = a.poaRepository.FreshUpsert(ctx, &poa)
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("error while upserting POA data")
			return nil, err
		}
		// upsert addresses.
		for i := 0; i < len(addresses); i++ {
			err = a.addressRepository.FreshUpsert(ctx, &addresses[i])
			if err != nil {
				log.Error(ctx).Stack().Err(err).Msg("error while upserting address data")
				return nil, err
			}
		}
	}

	go func() {
		err = a.kraBusinessLogic.CheckAndFetchKRAInfo(ctx, req.AppNumber, req.Mobile, req.Source)
		if err != nil {
			log.Info(ctx).Err(err).Msg("Error checking and fetching kra info")
		}
	}()

	if photoPath != constants.Empty {
		photoURL, err = getSignedS3Path(ctx, photoPath)
		if err != nil {
			log.Info(ctx).Err(err).Msg("Error generating signed url for photoPath")
			return nil, err
		}
	}
	go a.prismSQSBusinessLogic.SendMessageToQueue(ctx, a.commonPrismEventMessage.GetPrismSuccessEventData(req.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.OCR))
	go a.prismSQSBusinessLogic.SendMessageToQueue(ctx, a.commonPrismEventMessage.GetPrismAppAddressMetadata(&business.PrismEventMessage{
		AppNumber: req.AppNumber, Pincode: pincodeData.Pincode, City: pincodeData.City, State: pincodeData.State,
	}))

	// Create Aadhaar OCR Response.
	return &modelsV1API.AadhaarResponseData{Aadhaar: maskAadhaar(req.Data.Aadhaar),
		Pincode:      req.Data.Pincode,
		AddressLine1: req.Data.AddressLine1,
		AddressLine2: req.Data.AddressLine2,
		PhotoURL:     photoURL,
	}, nil
}

func uploadAadharFiles(ctx context.Context, request *modelsV1API.AadhaarOcrRequest) ([]string, error) {
	s3Paths := make([]string, 0, constants.IntegerFive)

	for name, file := range getAadharFiles(request) {
		path := fmt.Sprintf("%s/%s/%s%s%d%s", request.Meta.AppNumber, constants.AadharFlowName, name, "-", time.Now().Unix(), utils.GetMultipartFileExtension(file))
		err := uploadAadharToS3(ctx, path, file)
		if err != nil {
			return nil, err
		}
		s3Paths = append(s3Paths, path)

		// Check for Password-Protected PDF.
		if utils.GetMultipartFileExtension(file) == constants.PDFExtension && !config.Application().Validations.Aadhaar.PassProtectedPdfSupported {
			passProtected, err := isAadharPdfPasswordProtected(file)
			if err != nil {
				return nil, err
			}
			if passProtected {
				return nil, constants.ErrPasswordProtectedFileUploaded.Value()
			}
		}
	}
	return s3Paths, nil
}
