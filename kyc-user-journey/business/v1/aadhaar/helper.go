package aadhaar

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"mime/multipart"
	"slices"
	"strings"
	"time"

	goutils "github.com/angel-one/go-utils"

	"github.com/angel-one/kyc-user-journey/utils/fetch"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/s3"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/api"
	modelsBusiness "github.com/angel-one/kyc-user-journey/models/business"
	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/pdf"
)

func getAadharFiles(request *api.AadhaarOcrRequest) map[string]*multipart.FileHeader {
	files := map[string]*multipart.FileHeader{
		constants.AadhaarFrontFormKey: request.Data.FrontFile,
	}
	if !request.Data.IsSingleSide {
		files[constants.AadhaarBackFormKey] = request.Data.BackFile
	}
	return files
}

func uploadAadharToS3(ctx context.Context, path string, file *multipart.FileHeader) error {
	// opening file
	f, err := file.Open()
	if err != nil {
		return constants.ErrOpeningFile.WithDetails(err.Error())
	}
	// upload file
	client := s3.GetS3Client()
	err = client.Upload(ctx, path, f)
	if err != nil {
		return constants.ErrUploadingFile.WithDetails(err.Error())
	}
	return nil
}

func getSignedS3Path(ctx context.Context, path string) (string, error) {
	client := s3.GetS3Client()
	signedPath, err := client.GetSignedURL(ctx, path, time.Duration(config.S3().SignURLExpiryInSeconds)*time.Second)
	if err != nil {
		return constants.Empty, err
	}
	return signedPath, nil
}

func isAadharPdfPasswordProtected(file *multipart.FileHeader) (bool, error) {
	isPasswordProtected := false

	f, err := file.Open()
	if err != nil {
		return isPasswordProtected, constants.ErrOpeningFile.WithDetails(err.Error())
	}

	fileData, err := io.ReadAll(f)
	if err != nil {
		return isPasswordProtected, constants.ErrReadingFile.WithDetails(err.Error())
	}

	isPasswordProtected, err = pdf.IsPasswordProtectedPDF(fileData)
	if err != nil {
		return isPasswordProtected, constants.ErrReadingFile.WithDetails(err.Error())
	}

	return isPasswordProtected, nil
}

// Map Hyperverge responses to Aadhaar Parts (like FrontTop, FrontBottom, Back).
// Further Aadhaar Parts are consolidated to form Aadhaar Data.
func modifyOcrResponse(responses []*modelsExternal.OCRAadharResponse) (*modelsBusiness.AadhaarData, error) {
	var frontTop modelsBusiness.AadhaarFrontTop
	var frontBottom modelsBusiness.AadhaarFrontBottom
	var aadhaarBack modelsBusiness.AadhaarBack

	for _, r := range responses {
		for _, component := range r.Result {
			switch component.Type {
			case constants.AadhaarImageFrontTop:
				err := utils.ConvertStruct(component.Details, &frontTop)
				if err != nil {
					return nil, err
				}
				frontTop.IsPresent = true
			case constants.AadhaarImageFrontBottom:
				err := utils.ConvertStruct(component.Details, &frontBottom)
				if err != nil {
					return nil, err
				}
				frontBottom.IsPresent = true
			case constants.AadhaarImageBack:
				err := utils.ConvertStruct(component.Details, &aadhaarBack)
				if err != nil {
					return nil, err
				}
				aadhaarBack.IsPresent = true
			}
		}
	}

	aadharData, err := consolidateAadharParts(&frontTop, &frontBottom, &aadhaarBack)
	if err != nil {
		return nil, err
	}

	return &aadharData, nil
}

func setAddressData(back *modelsBusiness.BackImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Address data.
	if frontTop.Address.Value != "" && frontTop.Address.Conf > aadhaarBack.Address.Conf {
		back.Address = frontTop.Address.Value
		back.AddressOne = frontTop.Address.Line1
		back.AddressTwo = frontTop.Address.Line2
		back.City = frontTop.Address.City
		back.PinCode = frontTop.Pin.Value
		back.State = frontTop.Address.State
		confidenceScore["address"] = frontTop.Address.Conf
	} else if aadhaarBack.Address.Value != "" {
		back.Address = aadhaarBack.Address.Value
		back.AddressOne = aadhaarBack.Address.Line1
		back.AddressTwo = aadhaarBack.Address.Line2
		back.City = aadhaarBack.Address.City
		back.PinCode = aadhaarBack.Pin.Value
		back.State = aadhaarBack.Address.State
		confidenceScore["address"] = aadhaarBack.Address.Conf
	}
}

func setNameData(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom) {
	// Set Name data.
	if frontTop.Name.Value != "" && frontTop.Name.Conf > frontBottom.Name.Conf {
		front.Name = frontTop.Name.Value
		confidenceScore["name"] = frontTop.Name.Conf
	} else if frontBottom.Name.Value != "" {
		front.Name = frontBottom.Name.Value
		confidenceScore["name"] = frontBottom.Name.Conf
	}
}

func setPersonalInfoData(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontBottom *modelsBusiness.AadhaarFrontBottom) {
	// Set Personal Info data.
	if frontBottom.Aadhaar.Value != "" || frontBottom.Dob.Value != "" || frontBottom.Gender.Value != "" || frontBottom.Mother.Value != "" {
		front.Gender = frontBottom.Gender.Value
		front.URL = frontBottom.URL
		front.DateOfBirth = frontBottom.Dob.Value
		front.MotherName = frontBottom.Mother.Value
		confidenceScore["mother"] = frontBottom.Mother.Conf
		confidenceScore["dob"] = frontBottom.Dob.Conf
		confidenceScore["gender"] = frontBottom.Gender.Conf
	}
}

func setAadhaarNumber(front *modelsBusiness.FrontImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) error {
	// Set Aadhaar Number data.
	// first get the possible aadhaar numbers.
	aadhaars := make([]string, 0)
	if frontBottom.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, frontBottom.Aadhaar.Value)
		confidenceScore["aadhaar"] = frontBottom.Aadhaar.Conf
	}
	if frontTop.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, frontTop.Aadhaar.Value)
		if confidenceScore["aadhaar"] < frontTop.Aadhaar.Conf {
			confidenceScore["aadhaar"] = frontTop.Aadhaar.Conf
		}
	}
	if aadhaarBack.Aadhaar.Value != "" {
		aadhaars = append(aadhaars, aadhaarBack.Aadhaar.Value)
		if confidenceScore["aadhaar"] < aadhaarBack.Aadhaar.Conf {
			confidenceScore["aadhaar"] = aadhaarBack.Aadhaar.Conf
		}
	}
	if len(aadhaars) > 0 {
		aadhaar := aadhaars[0]
		for i := 1; i < len(aadhaars); i += 1 {
			if aadhaars[i] != aadhaar {
				// different value.
				return constants.ErrDifferentFrontAndBackAadhaar.Value()
			}
		}
		front.AadhaarNo = aadhaar
	}
	// set AadhaarNo.
	if frontBottom.Aadhaar.Value != "" && frontBottom.Aadhaar.Conf > frontTop.Aadhaar.Conf && frontBottom.Aadhaar.Conf > aadhaarBack.Aadhaar.Conf {
		front.AadhaarNo = frontBottom.Aadhaar.Value
		confidenceScore["aadhaar"] = frontBottom.Aadhaar.Conf
	} else if frontTop.Aadhaar.Value != "" && frontTop.Aadhaar.Conf > aadhaarBack.Aadhaar.Conf {
		front.AadhaarNo = frontTop.Aadhaar.Value
		confidenceScore["aadhaar"] = frontTop.Aadhaar.Conf
	} else {
		front.AadhaarNo = aadhaarBack.Aadhaar.Value
		confidenceScore["aadhaar"] = aadhaarBack.Aadhaar.Conf
	}
	return nil
}

func setSpouseAndFatherName(front *modelsBusiness.FrontImageData, back *modelsBusiness.BackImageData, confidenceScore map[string]int, frontTop *modelsBusiness.AadhaarFrontTop, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Spouse and Father Name data.
	if aadhaarBack.Husband.Value != "" && aadhaarBack.Husband.Conf > frontTop.Husband.Conf {
		back.SpouseName = aadhaarBack.Husband.Value
		confidenceScore["spouse"] = aadhaarBack.Husband.Conf
	} else if frontTop.Husband.Value != "" {
		back.SpouseName = frontTop.Husband.Value
		confidenceScore["spouse"] = frontTop.Husband.Conf
	}

	if aadhaarBack.Father.Value != "" && aadhaarBack.Father.Conf > frontTop.Father.Conf {
		front.FatherName = aadhaarBack.Father.Value
		confidenceScore["father"] = aadhaarBack.Father.Conf
	} else if frontTop.Father.Value != "" {
		front.FatherName = frontTop.Father.Value
		confidenceScore["father"] = frontTop.Father.Conf
	}
}

func setURLData(front *modelsBusiness.FrontImageData, back *modelsBusiness.BackImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set URL data.
	if frontBottom.URL != "" {
		front.URL = frontBottom.URL
	} else if frontTop.URL != "" {
		front.URL = frontTop.URL
	}
	if aadhaarBack.URL != "" {
		back.URL = aadhaarBack.URL
	} else if frontTop.URL != "" {
		back.URL = frontTop.URL
	}
}

func setFaceData(front *modelsBusiness.FrontImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set Face data.
	if strings.TrimSpace(strings.ToLower(frontTop.Face.Present)) == constants.Yes {
		front.Face = frontTop.Face.FaceString
	} else if strings.TrimSpace(strings.ToLower(frontBottom.Face.Present)) == constants.Yes {
		front.Face = frontBottom.Face.FaceString
	} else if strings.TrimSpace(strings.ToLower(aadhaarBack.Face.Present)) == constants.Yes {
		front.Face = aadhaarBack.Face.FaceString
	}
}

func setQRData(front *modelsBusiness.FrontImageData, frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom, aadhaarBack *modelsBusiness.AadhaarBack) {
	// Set QR data.
	if frontTop.Qr.Value != "" || frontBottom.Qr.Value != "" || aadhaarBack.Qr.Value != "" {
		front.QR = true
	}
}

func consolidateAadharParts(frontTop *modelsBusiness.AadhaarFrontTop, frontBottom *modelsBusiness.AadhaarFrontBottom,
	aadhaarBack *modelsBusiness.AadhaarBack) (modelsBusiness.AadhaarData, error) {
	var front modelsBusiness.FrontImageData
	var back modelsBusiness.BackImageData
	var areAadhaarsMasked = true
	confidenceScore := make(map[string]int)
	// set response into front and back and set into aadhaar response.
	// set maximum confidence score into response and send minimum value in response.
	// set Address and find maximum confidence score value and set into address model.
	setAddressData(&back, confidenceScore, frontTop, aadhaarBack)
	setNameData(&front, confidenceScore, frontTop, frontBottom)
	setPersonalInfoData(&front, confidenceScore, frontBottom)
	err := setAadhaarNumber(&front, confidenceScore, frontTop, frontBottom, aadhaarBack)
	if err != nil {
		return modelsBusiness.AadhaarData{}, err
	}
	setSpouseAndFatherName(&front, &back, confidenceScore, frontTop, aadhaarBack)
	setURLData(&front, &back, frontTop, frontBottom, aadhaarBack)
	setFaceData(&front, frontTop, frontBottom, aadhaarBack)
	setQRData(&front, frontTop, frontBottom, aadhaarBack)

	// get minimum confidence score.
	score := getConfidenceMinimumScore(confidenceScore)

	// are all aadhaars masked.
	if frontTop.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && frontTop.MaskingInfo.IsMaskingDone == constants.Yes
	}
	if frontBottom.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && frontBottom.MaskingInfo.IsMaskingDone == constants.Yes
	}
	if aadhaarBack.IsPresent {
		areAadhaarsMasked = areAadhaarsMasked && aadhaarBack.MaskingInfo.IsMaskingDone == constants.Yes
	}

	// set value into aadhaar response.
	aadhaarData := modelsBusiness.AadhaarData{
		FrontData:            front,
		BackData:             back,
		ConfidenceScore:      score,
		AreAllAadhaarsMasked: areAadhaarsMasked,
	}

	return aadhaarData, nil
}

func getConfidenceMinimumScore(score map[string]int) int {
	confidenceList := config.Application().Validations.Aadhaar.ConfidenceScoreParams

	minConfidence := 100
	for _, key := range confidenceList {
		if s, ok := score[key]; ok {
			if s < minConfidence {
				minConfidence = s
			}
		}
	}
	return minConfidence
}

func maskAadhaar(s string) string {
	if !utils.IsValidAgainstRegex(config.Application().Validations.AadhaarRegex, s) {
		return s
	}
	return fmt.Sprintf("XXXXXXXX%s", s[8:])
}

func getGenderValueForAadhaar(gender string) string {
	if strings.ToUpper(strings.TrimSpace(gender)) == constants.DigioSingleCharacterMaleGender || strings.ToUpper(strings.TrimSpace(gender)) == constants.KYC2MaleGender {
		return constants.KYC2MaleGender
	} else if strings.ToUpper(strings.TrimSpace(gender)) == constants.DigioSingleCharacterFemaleGender || strings.ToUpper(strings.TrimSpace(gender)) == constants.KYC2FemaleGender {
		return constants.KYC2FemaleGender
	} else if strings.ToUpper(strings.TrimSpace(gender)) == constants.DigioSingleCharacterOthersGender || strings.ToUpper(strings.TrimSpace(gender)) == constants.KYC2OthersGender {
		return constants.KYC2OthersGender
	} else {
		return constants.Empty
	}
}

func getAadhaarOCREntitiesToUpsert(ctx context.Context, req *api.AadhaarConfirmRequest, ocrDTO *modelsBusiness.AadhaarData) (modelsBusiness.POA, []modelsBusiness.Address) {
	adresses := []modelsBusiness.Address{}
	poa := modelsBusiness.POA{AppNumber: req.AppNumber}
	s3Paths := ocrDTO.S3Paths
	poa.FirstPath = s3Paths[0]
	if len(s3Paths) >= 2 {
		poa.SecondPath = s3Paths[1]
	}
	if ocrDTO.FrontData.Face != constants.Empty && len(s3Paths) >= 3 {
		poa.PhotoPath = s3Paths[2]
	}
	requestData := req.Data
	// save the poa details.
	poa.ID = maskAadhaar(requestData.Aadhaar)
	poa.FullName = ocrDTO.FrontData.Name
	poa.Type = constants.AadhaarPOA
	poa.IsQRCodeRead = ocrDTO.FrontData.QR
	poa.AreAadhaarNumbersMasked = ocrDTO.AreAllAadhaarsMasked
	poa.CreateSource = req.Source
	poa.LastUpdateSource = req.Source
	poa.Source = constants.AadhaarPOA
	poa.Gender = getGenderValueForAadhaar(ocrDTO.FrontData.Gender)
	poa.OCRData = strings.ToLower(fmt.Sprintf("%s|%s", poa.ID, ocrDTO.BackData.PinCode))
	// Update Dob.
	dob := strings.TrimSpace(ocrDTO.FrontData.DateOfBirth)
	if strings.TrimSpace(dob) != constants.Empty {
		if utils.IsValidAgainstRegex(config.Application().Validations.DOBRegex, dob) {
			poa.Dob = dob
		}
	} else {
		// old aadhaar image do not have dob.
		log.Info(ctx).Msg("dob is missing in aadhaar ocr")
	}

	// save Address Details.
	addressLineOne := requestData.AddressLine1
	if addressLineOne == constants.Empty {
		addressLineOne = ocrDTO.BackData.Address
	}
	addressLineTwo := requestData.AddressLine2
	if addressLineTwo == constants.Empty {
		addressLineTwo = fmt.Sprintf("%s %s", ocrDTO.BackData.City, ocrDTO.BackData.State)
	}
	permanentAddress := modelsBusiness.Address{
		AppNumber:        req.AppNumber,
		Type:             constants.PermanentAddressType,
		AddressLine1:     addressLineOne,
		AddressLine2:     addressLineTwo,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
		City:             ocrDTO.BackData.City,
		State:            ocrDTO.BackData.State,
		Pincode:          requestData.Pincode,
		Country:          constants.India,
	}
	correspondenceAddress := permanentAddress
	correspondenceAddress.Type = constants.CorrespondenceAddressType

	adresses = append(adresses, permanentAddress, correspondenceAddress)
	return poa, adresses
}
func downloadAndUploadMaskedAadhaar(ctx context.Context, s3Paths []string, pathIdx int, downloadURL string) (string, error) {
	uploadPath := s3Paths[pathIdx]
	// first check if url exists.
	if uploadPath == constants.Empty || downloadURL == constants.Empty {
		if pathIdx == 0 {
			return constants.Empty, constants.ErrMissingAadhaarFrontImage.Value()
		} else {
			return constants.Empty, constants.ErrMissingAadhaarBackImage.Value()
		}
	}
	imageBytes, err := fetch.GetFileBytes(ctx, downloadURL)
	if err != nil {
		return constants.Empty, err
	}
	// upload file.
	client := s3.GetS3Client()
	err = client.Upload(ctx, uploadPath, bytes.NewReader(imageBytes))
	if err != nil {
		return constants.Empty, constants.ErrUploadingFile.WithDetails(err.Error())
	}
	// Get Signed S3 path after upload.
	signedPath, err := getSignedS3Path(ctx, uploadPath)
	if err != nil {
		return constants.Empty, err
	}

	return signedPath, nil
}

func uploadAadharPhoto(ctx context.Context, photoPath, face string) error {
	data := []byte(face)
	decode := make([]byte, base64.StdEncoding.DecodedLen(len(data)))
	_, err := base64.StdEncoding.Decode(decode, data)
	if err != nil {
		return err
	}
	client := s3.GetS3Client()
	return client.Upload(ctx, photoPath, bytes.NewReader(decode))
}

func fetchHypervergeResponse(ctx context.Context, provider HypervergeExternalProvider, s3Paths []string, appNumber string) ([]*modelsExternal.OCRAadharResponse, error) {
	var resp = make([]*modelsExternal.OCRAadharResponse, 0, constants.IntegerFive)

	for _, p := range s3Paths {
		signedPath, err := getSignedS3Path(ctx, p)
		if err != nil {
			return nil, err
		}
		req := modelsExternal.OCRAadharRequest{URL: signedPath, AppNumber: appNumber}
		r, err := provider.FetchAadharOcr(ctx, &req)
		if err != nil {
			return nil, err
		}
		resp = append(resp, r)
	}
	return resp, nil
}

func getOcrDTOFromAadhaarOCRResponse(aadhaarData *modelsBusiness.AadhaarData, appNumber string) (*modelsBusiness.OCR, error) {
	ocrByteData, err := goutils.MarshalJSON(aadhaarData)
	if err != nil {
		return nil, err
	}
	return &modelsBusiness.OCR{
		AppNumber: appNumber,
		Type:      constants.AadhaarSourceOcr,
		Data:      string(ocrByteData),
	}, nil
}

func isEligibleForPOASkip(kraData *modelsBusiness.KRAData) bool {
	return kraData.PoaID == constants.Empty && kraData.PoaType == constants.Empty && kraData.CvlKraPermanentAddressLine1 != constants.Empty
}

func isKRACompliant(kraData *modelsBusiness.KRAData) bool {
	return slices.Contains(config.KRA().KRACompliantStatuses, kraData.CvlKraStatus)
}

func isKRADataMatchWithKYCData(kraData *modelsBusiness.KRAData) bool {
	return len(kraData.CvlKraMobile) >= 10 && strings.EqualFold(kraData.CvlKraMobile[len(kraData.CvlKraMobile)-10:], kraData.Mobile) &&
		strings.EqualFold(kraData.CvlKraEmail, kraData.Email)
}

func isAddressPrefilledWithKRAData(kraData *modelsBusiness.KRAData) bool {
	return kraData.PermanentAddressLine1 != constants.Empty && kraData.PermanentPincode != constants.Empty
}
