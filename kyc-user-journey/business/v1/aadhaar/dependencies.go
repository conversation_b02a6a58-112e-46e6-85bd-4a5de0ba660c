package aadhaar

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type HypervergeExternalProvider interface {
	FetchAadharOcr(ctx context.Context, request *modelsV1.OCRAadharRequest) (*modelsV1.OCRAadharResponse, error)
}

type POARepository interface {
	FreshUpsert(ctx context.Context, poaDTO *business.POA) error
	Update(ctx context.Context, poaDTO *business.POA) error
	GetPoaAndOCRData(ctx context.Context, appNumber string) (*business.PoaAndOCRData, error)
	UpdateAadhaarAndAddress(ctx context.Context, addressDTO *business.Address, poaDTO *business.POA, appNumber, source string) error
}

type AddressRepository interface {
	FreshUpsert(ctx context.Context, addressDTO *business.Address) error
	Update(ctx context.Context, addressDTO *business.Address) error
}

type OCRRepository interface {
	Save(ctx context.Context, octDTO *business.OCR) error
	GetOCRData(ctx context.Context, appNumber, ocrType string) (*business.OCR, error)
}

type PincodeRepository interface {
	FetchRecordByPincode(ctx context.Context, pincode string) (*business.Pincode, error)
}

type KRABusinessLogic interface {
	CheckAndFetchKRAInfo(ctx context.Context, appNumber, mobile, source string) error
}

type CvlKRARepository interface {
	GetKRADataByAppNumber(ctx context.Context, appNumber string) (*business.KRAData, error)
}
type PrismSQSBusinessLogic interface {
	SendMessageToQueue(ctx context.Context, prismEventMessage *business.PrismEventMessage)
}
type CommonPrismBusinessLogic interface {
	GetPrismErrorEventData(appNumber, eventName, subEventName, activityID, remark string) *business.PrismEventMessage
	GetPrismSuccessEventData(appNumber, eventName, subEventName, activityID string) *business.PrismEventMessage
	GetPrismAppAddressMetadata(eventMsg *business.PrismEventMessage) *business.PrismEventMessage
}

type AadharBusinessLogic struct {
	hypervergeExternalProvider HypervergeExternalProvider
	poaRepository              POARepository
	addressRepository          AddressRepository
	ocrRepository              OCRRepository
	pincodeRepository          PincodeRepository
	kraBusinessLogic           KRABusinessLogic
	cvlKraRepository           CvlKRARepository
	prismSQSBusinessLogic      PrismSQSBusinessLogic
	commonPrismEventMessage    CommonPrismBusinessLogic
}

func NewAadhaarBusinessLogic(hypervergeProvider HypervergeExternalProvider, poaRepo POARepository,
	addressRepo AddressRepository, ocrRepository OCRRepository, pincodeRepository PincodeRepository, kraBusinessLogic KRABusinessLogic, cvlKraRepository CvlKRARepository, prismSQSBusinessLogic PrismSQSBusinessLogic, commonPrismEventMessage CommonPrismBusinessLogic) *AadharBusinessLogic {
	return &AadharBusinessLogic{hypervergeExternalProvider: hypervergeProvider,
		poaRepository:           poaRepo,
		addressRepository:       addressRepo,
		ocrRepository:           ocrRepository,
		pincodeRepository:       pincodeRepository,
		kraBusinessLogic:        kraBusinessLogic,
		cvlKraRepository:        cvlKraRepository,
		prismSQSBusinessLogic:   prismSQSBusinessLogic,
		commonPrismEventMessage: commonPrismEventMessage,
	}
}
