package digio

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (d *DigioEmodBusinessLogic) InitiateEmodDigio(ctx context.Context, request *modelsAPIv1.EmodInitiateDigioRequest) (*modelsAPIv1.InitiateDigioResponse, error) {
	panDTO, err := d.fetchPanData(ctx, request)
	if err != nil {
		return nil, err
	}

	digioInitiateRequest := getEmodDigioInitiateRequest(request, &panDTO)
	response, err := d.digioExternalProvider.Initiate(ctx, &digioInitiateRequest)
	if err != nil {
		return nil, err
	}

	if request.IsKRA02Flow {
		request.Intent = constants.EmodPersonalDetailsKRA02ConfigKey
	}
	encryptedDigioRedirectDataString, err := getEmodEncryptedDigioRedirectDataString(request)
	if err != nil {
		return nil, err
	}

	digioLoginURL := getEmodDigioLoginURL(encryptedDigioRedirectDataString, request, &response)
	err = d.panDocRepository.UpdatePanDocWithDigioRequestID(request.AppNumber, constants.PanDigio, request.Source, response.ID, constants.DigioInitiated)
	if err != nil {
		return nil, err
	}

	return &modelsAPIv1.InitiateDigioResponse{
		RedirectURL: digioLoginURL,
	}, nil
}

func (d *DigioEmodBusinessLogic) RedirectionFromEmodDigio(ctx context.Context, request *modelsAPIv1.EmodDigioRedirectAPIRequest) (*modelsAPIv1.DigioRedirectResponse, error) {
	err := parseAndPopulateEmodDigioRedirectURLData(ctx, request)
	if err != nil {
		return nil, err
	}

	err = d.panDocRepository.UpdateDigioStatusWithAppNumberAndSource(request.AppNumber, constants.PanDigio, request.Source, constants.DigioRedirected, request.DigioDocID)
	if err != nil {
		return nil, err
	}

	redirectURL := config.Digio().Flow.Emod.Intent[request.Intent].Redirect.URL
	query := url.Values{}
	if strings.EqualFold(constants.Success, request.Status) {
		panDoc, err := d.panDocRepository.GetPANDocWithDigioRequestID(ctx, constants.PanDigio, request.DigioDocID, request.AppNumber)
		if err != nil {
			return nil, err
		}
		pan, err := d.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
		if err != nil {
			return nil, err
		}
		digioStatusResponse, err := d.getEmodDigioKycStatusResponse(ctx, request)
		if err != nil {
			return nil, err
		}
		query = d.getQueryParamsForDigioSuccess(ctx, request, &panDoc, &pan, &digioStatusResponse)
	} else {
		log.Info(ctx).Msg("RedirectionFromDigio: digio failure")
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.Emod.Intent[request.Intent].Redirect.Message.Failure)
	}

	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return &modelsAPIv1.DigioRedirectResponse{
		RedirectURL: redirectURLWithQueryParams,
	}, nil
}

func (d *DigioEmodBusinessLogic) getQueryParamsForDigioSuccess(ctx context.Context, request *modelsAPIv1.EmodDigioRedirectAPIRequest, panDoc *business.PanDoc, pan *business.Pan, digioStatusResponse *modelsExternals.Actions) url.Values {
	query := url.Values{}
	if !strings.EqualFold(panDoc.Pan, digioStatusResponse.Details.Pan.IDNumber) {
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.Emod.Intent[request.Intent].Redirect.Message.PanMisMatch)
	} else {
		err := d.getPANAndAadhaarDetailsFromDigio(ctx, request, panDoc, pan, digioStatusResponse)
		if err != nil {
			log.Info(ctx).Msg("RedirectionFromDigio: digio pan fetch failure")
			failureMessage := config.Digio().Flow.Emod.Intent[request.Intent].Redirect.Message.Failure
			errMessage := err.Error()
			if errMessage != constants.Empty {
				failureMessage = errMessage
			}
			query.Add(constants.FailureCallBackParam, failureMessage)
		} else {
			log.Info(ctx).Msg("RedirectionFromDigio: digio success")
			query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
			query.Add(constants.Message, config.Digio().Flow.Emod.Intent[request.Intent].Redirect.Message.Success)
			if request.Intent == constants.EmodPersonalDetailsIntent {
				query = addAddressQueryParamsEmod(d, ctx, request.AppNumber, query)
			}
		}
	}
	return query
}

func (d *DigioEmodBusinessLogic) getPANAndAadhaarDetailsFromDigio(ctx context.Context, request *modelsAPIv1.EmodDigioRedirectAPIRequest,
	panDoc *business.PanDoc, pan *business.Pan, digioStatusResponse *modelsExternals.Actions) error {
	var err error

	// validate the date of birth.
	if err = utils.IsDOBValid(digioStatusResponse.Details.Pan.Dob, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge); err != nil {
		log.Error(ctx).Stack().Err(err).
			Str("dob", digioStatusResponse.Details.Pan.Dob).
			Bool("invalidDOB", true).
			Msg("getPANAndAadhaarDetailsFromDigio: invalid DOB")
		return err
	}

	// fetch the aadhaar xml and pan from digio.
	chAadhaar := make(chan business.ChannelDigioMedia)
	chPAN := make(chan business.ChannelDigioMedia)

	// get PAN from digio.
	go d.getDigioMediaEmod(ctx, digioStatusResponse.ExecutionRequestID, request, "false", constants.Pan, chPAN)
	// get Aadhaar from digio.
	go d.getDigioMediaEmod(ctx, digioStatusResponse.ExecutionRequestID, request, "true", constants.Aadhaar, chAadhaar)

	aadhaarFileResponse := <-chAadhaar
	panFileResponse := <-chPAN
	if aadhaarFileResponse.Error != nil { // if aadhaar fails then the entire digio fails
		log.Error(ctx).Stack().Err(aadhaarFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting aadhaar xml file from digio")
		return aadhaarFileResponse.Error
	}

	if panFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(panFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting PAN PDF from digio")
		return panFileResponse.Error
	}

	// parse aadhaar byte array.
	var aadhaarDetails business.Certificate
	err = xml.Unmarshal(aadhaarFileResponse.File, &aadhaarDetails)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error unmarshalling aadhaar XML")
		return err
	}

	permanentAddress, correspondenceAddress, poa := d.getPOAAndAddressDTOsEmod(&aadhaarDetails, request, digioStatusResponse)
	pan, panDoc, err = getPanDTOsEmod(panDoc, pan, request, digioStatusResponse)
	if err != nil {
		return err
	}

	err = uploadPANAndAadhaarToS3Emod(ctx, poa.PhotoPath, poa.SecondPath, panDoc.ImagePath,
		digioStatusResponse, aadhaarFileResponse, panFileResponse)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading files to s3")
		return err
	}

	// save the records.
	err = d.digioRepository.UpsertDigioEntities(ctx, pan, panDoc, &permanentAddress, &correspondenceAddress, &poa)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error saving records in DB")
		err = constants.ErrDatabase.WithDetails(err.Error())
		return err
	}
	return nil
}

func (d *DigioEmodBusinessLogic) getEmodDigioKycStatusResponse(ctx context.Context, request *modelsAPIv1.EmodDigioRedirectAPIRequest) (modelsExternals.Actions, error) {
	digioResponse := modelsExternals.Actions{}
	externalResponse, err := d.digioExternalProvider.GetKycStatusResponse(ctx, modelsExternals.DigioKycStatusRequest{
		DigioRequestID: request.DigioDocID,
		AppNumber:      request.AppNumber,
	})

	if err != nil {
		log.Error(ctx).Err(err).Msg("getPANAndAadhaarDetilsFromDigio: error getting external PAN and aadhaar details from Digio")
		return digioResponse, err
	}

	if len(externalResponse.Actions) == 0 {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: external PAN and aadhaar details API didn't action")
		return digioResponse, constants.ErrEmptyDigioAction.WithMessage("Empty Digio action")
	}
	digioResponse = externalResponse.Actions[0]
	return digioResponse, nil
}

func (d *DigioEmodBusinessLogic) getDigioMediaEmod(ctx context.Context, executionRequestID string, request *modelsAPIv1.EmodDigioRedirectAPIRequest,
	isXML string, docType string, ch chan<- business.ChannelDigioMedia) {
	response, err := d.digioExternalProvider.GetMediaForPanAadhaar(ctx, modelsExternals.DigioKYCMediaRequest{
		ExecutionRequestID: executionRequestID,
		AppNumber:          request.AppNumber,
		XML:                isXML,
		DocType:            docType,
	})

	if err != nil {
		log.Error(ctx).Stack().Err(err).Str("doc_type", docType).Msg("getDigioMediaEmod: getting media from digio")
		ch <- business.ChannelDigioMedia{File: nil, Error: err}
		return
	}
	ch <- business.ChannelDigioMedia{File: response.FileBytes, Error: nil}
}

func (d *DigioEmodBusinessLogic) getPOAAndAddressDTOsEmod(aadhaarDetails *business.Certificate, request *modelsAPIv1.EmodDigioRedirectAPIRequest,
	digioStatusResponse *modelsExternals.Actions) (business.Address, business.Address, business.POA) {
	poa := business.POA{
		AppNumber: request.AppNumber,
		ID:        digioStatusResponse.Details.Aadhaar.IDNumber,
		FullName:  strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Name),
		Type:      constants.DigilockerPOA,
		FirstPath: fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerPhotoFilename, constants.AadhaarDigilockerPhotoFileExt),
		SecondPath: fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerXMLFilename, constants.AadhaarDigilockerXMLFileExt),
		Dob:              strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Dob),
		Source:           constants.AadhaarDigio,
		Gender:           d.commonDigioBusinessLogic.GetGenderValueForAadhaarFromDigioGenderString(digioStatusResponse.Details.Aadhaar.Gender),
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	poa.PhotoPath = poa.FirstPath
	cityFromDigio := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc
	if cityFromDigio == constants.Empty {
		cityFromDigio = aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist
	}
	city := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(cityFromDigio, " ")))
	state := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(strings.ReplaceAll(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State, "&", "AND"), " ")))

	permanentAddress := business.Address{
		AppNumber: request.AppNumber,
		Type:      constants.PermanentAddressType,
		AddressLine1: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.House,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Street),
		AddressLine2: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Loc,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc),
		AddressLine3: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State),
		City:             city,
		State:            state,
		Country:          aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Country,
		Pincode:          digioStatusResponse.Details.Aadhaar.PermanentAddressDetails.Pincode,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	correspondenceAddress := permanentAddress
	correspondenceAddress.Type = constants.CorrespondenceAddressType
	return permanentAddress, correspondenceAddress, poa
}

func (d *DigioEmodBusinessLogic) fetchPanData(ctx context.Context, request *modelsAPIv1.EmodInitiateDigioRequest) (business.Pan, error) {
	panDTO, err := d.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
	if err == nil {
		return panDTO, nil
	}

	// else fetch pan from profile
	pan, err := d.clcmExternalProvider.FetchPanDetails(ctx, request.ClientCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg(fmt.Sprintf("Error getting pan details from clcm for client code %s", request.ClientCode))
		return panDTO, err
	}

	panDTO.Pan = pan.Data.Pan
	panDTO.FullName = pan.Data.FullName
	return panDTO, nil
}
