package digio

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/nsdl"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (d *DigioBusinessLogic) InitiateDigio(ctx context.Context, request *modelsAPIv1.InitiateDigioRequest) (*modelsAPIv1.InitiateDigioResponse, error) {
	panDTO, err := d.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
	if err != nil {
		return nil, err
	}
	err = validatePANStatus(&panDTO)
	if err != nil {
		return nil, err
	}

	digioInitiateRequest := getDigioInitiateRequest(request, &panDTO)
	response, err := d.digioExternalProvider.Initiate(ctx, &digioInitiateRequest)
	if err != nil {
		return nil, err
	}

	encryptedDigioRedirectDataString, err := getEncryptedDigioRedirectDataString(request, &panDTO)
	if err != nil {
		return nil, err
	}

	digioLoginURL := getDigioLoginURL(encryptedDigioRedirectDataString, request, &response)
	err = d.panDocRepository.UpdatePanDocWithDigioRequestID(request.AppNumber, constants.PanDigio, request.Source, response.ID, constants.DigioInitiated)
	if err != nil {
		return nil, err
	}

	return &modelsAPIv1.InitiateDigioResponse{
		RedirectURL: digioLoginURL,
	}, nil
}

func (d *DigioBusinessLogic) RedirectionFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (*modelsAPIv1.DigioRedirectResponse, error) {
	err := parseAndPopulateDigioRedirectURLData(ctx, request)
	if err != nil {
		return nil, err
	}

	err = d.panDocRepository.UpdateDigioStatusWithAppNumberAndSource(request.AppNumber, constants.PanDigio, request.Source, constants.DigioRedirected, request.DigioDocID)
	if err != nil {
		return nil, err
	}

	redirectURL := config.Digio().Flow.Correction.Redirect.URL
	query := url.Values{}
	if strings.EqualFold(constants.Success, request.Status) {
		panDoc, err := d.panDocRepository.GetPANDocWithDigioRequestID(ctx, constants.PanDigio, request.DigioDocID, request.AppNumber)
		if err != nil {
			return nil, err
		}
		pan, err := d.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
		if err != nil {
			return nil, err
		}
		digioStatusResponse, err := d.getDigioKycStatusResponse(ctx, request)
		if err != nil {
			return nil, err
		}
		query = d.getQueryParamsForDigioSuccess(ctx, request, &panDoc, &pan, &digioStatusResponse)
	} else {
		log.Info(ctx).Msg("RedirectionFromDigio: digio failure")
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.Correction.Redirect.Message.Failure)
	}

	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return &modelsAPIv1.DigioRedirectResponse{
		RedirectURL: redirectURLWithQueryParams,
	}, nil
}

func (d *DigioBusinessLogic) getQueryParamsForDigioSuccess(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest, panDoc *business.PanDoc, pan *business.Pan, digioStatusResponse *modelsV1.Actions) url.Values {
	query := url.Values{}
	if !strings.EqualFold(panDoc.Pan, digioStatusResponse.Details.Pan.IDNumber) {
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.Correction.Redirect.Message.PanMisMatch)
	} else {
		err := d.getPANAndAadhaarDetailsFromDigio(ctx, request, panDoc, pan, digioStatusResponse)
		if err != nil {
			log.Info(ctx).Msg("RedirectionFromDigio: digio pan fetch failure")
			failureMessage := config.Digio().Flow.Correction.Redirect.Message.Failure
			errMessage := err.Error()
			if errMessage != constants.Empty {
				failureMessage = errMessage
			}
			query.Add(constants.FailureCallBackParam, failureMessage)
		} else {
			log.Info(ctx).Msg("RedirectionFromDigio: digio success")
			query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
			query.Add(constants.Message, config.Digio().Flow.Correction.Redirect.Message.Success)
		}
	}
	return query
}

func (d *DigioBusinessLogic) getPANAndAadhaarDetailsFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest,
	panDoc *business.PanDoc, pan *business.Pan, digioStatusResponse *modelsV1.Actions) error {
	var err error

	// validate the date of birth.
	if err = utils.IsDOBValid(digioStatusResponse.Details.Pan.Dob, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge); err != nil {
		log.Error(ctx).Stack().Err(err).
			Str("dob", digioStatusResponse.Details.Pan.Dob).
			Bool("invalidDOB", true).
			Msg("getPANAndAadhaarDetailsFromDigio: invalid DOB")
		return err
	}

	// fetch the aadhaar xml and pan from digio.
	chAadhaar := make(chan business.ChannelDigioMedia)
	chPAN := make(chan business.ChannelDigioMedia)

	// get PAN from digio.
	go d.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "false", constants.Pan, chPAN)
	// get Aadhaar from digio.
	go d.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "true", constants.Aadhaar, chAadhaar)

	aadhaarFileResponse := <-chAadhaar
	panFileResponse := <-chPAN
	if aadhaarFileResponse.Error != nil { // if aadhaar fails then the entire digio fails
		log.Error(ctx).Stack().Err(aadhaarFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting aadhaar xml file from digio")
		return aadhaarFileResponse.Error
	}

	if panFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(panFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting PAN PDF from digio")
		return panFileResponse.Error
	}

	// parse aadhaar byte array.
	var aadhaarDetails business.Certificate
	err = xml.Unmarshal(aadhaarFileResponse.File, &aadhaarDetails)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error unmarshalling aadhaar XML")
		return err
	}

	permanentAddress, correspondenceAddress, poa := d.getPOAAndAddressDTOs(&aadhaarDetails, request, digioStatusResponse)
	pan, panDoc, err = getPanDTOs(panDoc, pan, request, digioStatusResponse)
	if err != nil {
		return err
	}

	d.UpdatePanDetailsWithNSDLResponse(ctx, panDoc, pan, digioStatusResponse)

	err = uploadPANAndAadhaarToS3(ctx, poa.PhotoPath, poa.SecondPath, panDoc.ImagePath,
		digioStatusResponse, aadhaarFileResponse, panFileResponse)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading files to s3")
		return err
	}

	if panFileResponse.Error != nil {
		pan.Pan = constants.Empty
		panDoc.Pan = constants.Empty
	}

	// save the records.
	err = d.digioRepository.UpsertDigioEntities(ctx, pan, panDoc, &permanentAddress, &correspondenceAddress, &poa)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error saving records in DB")
		err = constants.ErrDatabase.WithDetails(err.Error())
		return err
	}
	selfieMatchScore := d.getSelfieMatchScore(ctx, pan.AppNumber)
	err = d.selfieRepository.UpdateSelfieMatch(ctx, selfieMatchScore, request.Source, request.AppNumber)
	if err != nil {
		return err
	}
	return nil
}

func (d *DigioBusinessLogic) getSelfieMatchScore(ctx context.Context, appNumber string) int64 {
	validateSelfieResponse, err := d.selfieBusinessLogic.ValidateSelfieForAppNumber(ctx, appNumber)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("validateSelfie: selfie validation failed")
		return 0.0
	}
	return validateSelfieResponse.SelfieMatchScore
}

func (d *DigioBusinessLogic) getDigioKycStatusResponse(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (modelsV1.Actions, error) {
	digioResponse := modelsV1.Actions{}
	externalResponse, err := d.digioExternalProvider.GetKycStatusResponse(ctx, modelsV1.DigioKycStatusRequest{
		DigioRequestID: request.DigioDocID,
		AppNumber:      request.AppNumber,
	})

	if err != nil {
		log.Error(ctx).Err(err).Msg("getPANAndAadhaarDetilsFromDigio: error getting external PAN and aadhaar details from Digio")
		return digioResponse, err
	}

	if len(externalResponse.Actions) == 0 {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: external PAN and aadhaar details API didn't action")
		return digioResponse, constants.ErrEmptyDigioAction.WithMessage("Empty Digio action")
	}
	digioResponse = externalResponse.Actions[0]
	return digioResponse, nil
}

func (d *DigioBusinessLogic) getDigioMedia(ctx context.Context, executionRequestID string, request *modelsAPIv1.DigioRedirectAPIRequest,
	isXML string, docType string, ch chan<- business.ChannelDigioMedia) {
	response, err := d.digioExternalProvider.GetMediaForPanAadhaar(ctx, modelsV1.DigioKYCMediaRequest{
		ExecutionRequestID: executionRequestID,
		AppNumber:          request.AppNumber,
		XML:                isXML,
		DocType:            docType,
	})

	if err != nil {
		log.Error(ctx).Stack().Err(err).Str("doc_type", docType).Msg("getDigioMedia: getting media from digio")
		ch <- business.ChannelDigioMedia{File: nil, Error: err}
		return
	}
	ch <- business.ChannelDigioMedia{File: response.FileBytes, Error: nil}
}

func (d *DigioBusinessLogic) getPOAAndAddressDTOs(aadhaarDetails *business.Certificate, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) (business.Address, business.Address, business.POA) {
	poa := business.POA{
		AppNumber: request.AppNumber,
		ID:        digioStatusResponse.Details.Aadhaar.IDNumber,
		FullName:  strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Name),
		Type:      constants.DigilockerPOA,
		FirstPath: fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerPhotoFilename, constants.AadhaarDigilockerPhotoFileExt),
		SecondPath: fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerXMLFilename, constants.AadhaarDigilockerXMLFileExt),
		Dob:              strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Dob),
		Source:           constants.AadhaarDigio,
		Gender:           d.commonDigioBusinessLogic.GetGenderValueForAadhaarFromDigioGenderString(digioStatusResponse.Details.Aadhaar.Gender),
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	poa.PhotoPath = poa.FirstPath

	city := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist, " ")))
	state := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(strings.ReplaceAll(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State, "&", "AND"), " ")))

	permanentAddress := business.Address{
		AppNumber: request.AppNumber,
		Type:      constants.PermanentAddressType,
		AddressLine1: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.House,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Street),
		AddressLine2: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Loc,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc),
		AddressLine3: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State),
		City:             city,
		State:            state,
		Country:          aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Country,
		Pincode:          digioStatusResponse.Details.Aadhaar.PermanentAddressDetails.Pincode,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	correspondenceAddress := permanentAddress
	correspondenceAddress.Type = constants.CorrespondenceAddressType
	return permanentAddress, correspondenceAddress, poa
}

func (d *DigioBusinessLogic) UpdatePanDetailsWithNSDLResponse(ctx context.Context, panDoc *business.PanDoc, panDTO *business.Pan, digioStatusResponse *modelsV1.Actions) {
	// NSDL PanDetails Validation
	inputData := modelsV1.NSDLVerifyPanV2InputData{
		Pan:  panDTO.Pan,
		Name: digioStatusResponse.Details.Pan.Name,
		DOB:  digioStatusResponse.Details.Pan.Dob,
	}
	panDoc.FullName = constants.Empty
	panDTO.FullName = constants.Empty
	panDTO.DOB = constants.Empty
	panDoc.DOB = constants.Empty

	nsdlResponse, err := d.nsdlExternalProvider.ValidatePANV2(ctx, &modelsV1.NSDLVerifyPanV2Request{InputData: []modelsV1.NSDLVerifyPanV2InputData{inputData}}, nsdl.RoundRobinV2.Next())

	if err != nil {
		log.Error(ctx).Err(err).Msg("Error from NSDL V2 API")
		return
	}

	if len(nsdlResponse.Data) == 0 {
		log.Error(ctx).Msg("Empty response from NSDL V2 API")
		return
	}

	if nsdlResponse.Data[0].ISDoBMatch {
		panDoc.DOB = digioStatusResponse.Details.Pan.Dob
		panDTO.DOB = panDoc.DOB
		panDTO.IsDOBMatch = true
	}

	if nsdlResponse.Data[0].IsNameMatch {
		panDoc.FullName = digioStatusResponse.Details.Pan.Name
		panDTO.FullName = panDoc.FullName
		panDTO.IsNameMatch = true
	}
}
