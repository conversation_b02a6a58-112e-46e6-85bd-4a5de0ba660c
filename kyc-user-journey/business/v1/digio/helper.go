package digio

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/devfeel/mapper"

	"github.com/angel-one/kyc-user-journey/aws/s3"

	"github.com/angel-one/go-pii-utils/log"
	goUtils "github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/entities"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
	objectmapper "github.com/angel-one/kyc-user-journey/utils/mapper"
	"github.com/angel-one/kyc-user-journey/utils/security"
)

func getDigioLoginURL(encryptedDigioRedirectDataString string, request *modelsAPIv1.InitiateDigioRequest, response *modelsV1.DigioInitiateResponse) string {
	baseRedirectURL := config.Digio().Flow.Correction.CallBackURL

	digioBaseLoginURL := config.Digio().BaseLogInURL

	digioLoginURL := fmt.Sprintf("%s%s/%s/%s", digioBaseLoginURL, response.ID, response.TransactionID, response.CustomerIdentifier)

	digioLoginQueryParams := getDigioLoginQueryParams(encryptedDigioRedirectDataString, request, response, baseRedirectURL)

	digioLoginURLWithQueryParams := fmt.Sprintf("%s?%s", digioLoginURL, digioLoginQueryParams.Encode())
	return digioLoginURLWithQueryParams
}

func getOnboardingDigioLoginURL(encryptedDigioRedirectDataString string, request *modelsAPIv1.InitiateDigioRequest, response *modelsV1.DigioInitiateResponse) string {
	baseRedirectURL := config.Digio().Flow.Onboarding.CallBackURL

	digioBaseLoginURL := config.Digio().BaseLogInURL

	digioLoginURL := fmt.Sprintf("%s%s/%s/%s", digioBaseLoginURL, response.ID, response.TransactionID, response.CustomerIdentifier)

	digioLoginQueryParams := getDigioLoginQueryParams(encryptedDigioRedirectDataString, request, response, baseRedirectURL)

	digioLoginURLWithQueryParams := fmt.Sprintf("%s?%s", digioLoginURL, digioLoginQueryParams.Encode())
	return digioLoginURLWithQueryParams
}

func getDigioLoginQueryParams(encryptedDigioRedirectDataString string, request *modelsAPIv1.InitiateDigioRequest, response *modelsV1.DigioInitiateResponse, baseRedirectURL string) url.Values {
	redirectURLQuery := url.Values{}
	redirectURLQuery.Add(constants.DigioEncryptedRedirectDataStringQueryParamKey, encryptedDigioRedirectDataString)
	redirectURLQuery.Add(constants.DigioSourceQueryParamKey, request.Source)
	kycRedirectURL := fmt.Sprintf("%s?%s", baseRedirectURL, redirectURLQuery.Encode())

	digioLoginQuery := url.Values{}
	digioLoginQuery.Add(constants.DigioRedirectURLQueryParamKey, kycRedirectURL)
	digioLoginQuery.Add(constants.DigioTokenIDQueryParamKey, response.AccessToken.ID)

	additionalQueryParams := config.Digio().QueryParameters
	for key, value := range additionalQueryParams {
		digioLoginQuery.Add(key, value)
	}
	return digioLoginQuery
}

func getDigioInitiateRequest(request *modelsAPIv1.InitiateDigioRequest, panDTO *business.Pan) modelsV1.DigioInitiateRequest {
	if panDTO.FullName == constants.Empty {
		panDTO.FullName = " "
	}
	return modelsV1.DigioInitiateRequest{
		CustomerIdentifier:  request.Mobile,
		TemplateName:        config.Digio().TemplateName,
		NotifyCustomer:      config.Digio().NotifyCustomer,
		GenerateAccessToken: config.Digio().GenerateAccessToken,
		AppNumber:           request.AppNumber,
		DigilockerDocumentAttributes: modelsV1.DigiLockerAttributeData{
			PAN: modelsV1.PANData{
				PANNumber: panDTO.Pan,
			},
		},
	}
}

func getDigioRedirectData(request *modelsAPIv1.InitiateDigioRequest, panDTO *business.Pan) business.DigioRedirectURLData {
	data := business.DigioRedirectURLData{
		Source:             request.Source,
		Platform:           request.Platform,
		AppNumber:          request.AppNumber,
		Mobile:             request.Mobile,
		FullName:           panDTO.FullName,
		NSDLSeededCheckTS:  panDTO.NSDLSeededCheckTS,
		IsAadhaarPanSeeded: panDTO.IsAadhaarPanSeeded,
		BOUserID:           panDTO.BOUserID,
		Pan:                panDTO.Pan,
		Intent:             request.Intent,
		AgentID:            request.AgentID,
		GuardianName:       panDTO.FatherName,
		GuardianType:       panDTO.GuardianType,
	}
	return data
}

func getNRIDigioLoginURL(encryptedDigioRedirectDataString string, request *modelsAPIv1.InitiateNRIDigioRequest, response *modelsV1.DigioInitiateResponse) string {
	baseRedirectURL := config.Digio().Flow.NRI.CallBackURL

	digioBaseLoginURL := config.Digio().BaseLogInURL

	digioLoginURL := fmt.Sprintf("%s%s/%s/%s", digioBaseLoginURL, response.ID, response.TransactionID, response.CustomerIdentifier)

	digioLoginQueryParams := getNRIDigioLoginQueryParams(encryptedDigioRedirectDataString, request, response, baseRedirectURL)

	digioLoginURLWithQueryParams := fmt.Sprintf("%s?%s", digioLoginURL, digioLoginQueryParams.Encode())
	return digioLoginURLWithQueryParams
}

func getNRIDigioLoginQueryParams(encryptedDigioRedirectDataString string, request *modelsAPIv1.InitiateNRIDigioRequest, response *modelsV1.DigioInitiateResponse, baseRedirectURL string) url.Values {
	redirectURLQuery := url.Values{}
	redirectURLQuery.Add(constants.DigioEncryptedRedirectDataStringQueryParamKey, encryptedDigioRedirectDataString)
	redirectURLQuery.Add(constants.DigioSourceQueryParamKey, request.Source)
	kycRedirectURL := fmt.Sprintf("%s?%s", baseRedirectURL, redirectURLQuery.Encode())

	digioLoginQuery := url.Values{}
	digioLoginQuery.Add(constants.DigioRedirectURLQueryParamKey, kycRedirectURL)
	digioLoginQuery.Add(constants.DigioTokenIDQueryParamKey, response.AccessToken.ID)

	additionalQueryParams := config.Digio().QueryParameters
	for key, value := range additionalQueryParams {
		digioLoginQuery.Add(key, value)
	}
	return digioLoginQuery
}

func getNRIDigioInitiateRequest(request *modelsAPIv1.InitiateNRIDigioRequest, panDTO *business.Pan) modelsV1.DigioInitiateRequest {
	return modelsV1.DigioInitiateRequest{
		CustomerIdentifier:  request.Mobile,
		TemplateName:        config.Digio().TemplateName,
		NotifyCustomer:      config.Digio().NotifyCustomer,
		GenerateAccessToken: config.Digio().GenerateAccessToken,
		AppNumber:           request.AppNumber,
		DigilockerDocumentAttributes: modelsV1.DigiLockerAttributeData{
			PAN: modelsV1.PANData{
				PANNumber: panDTO.Pan,
			},
		},
	}
}

func getNRIDigioRedirectData(request *modelsAPIv1.InitiateNRIDigioRequest, panDTO *business.Pan) business.DigioRedirectURLData {
	data := business.DigioRedirectURLData{
		Source:             request.Source,
		Platform:           request.Platform,
		AppNumber:          request.AppNumber,
		Mobile:             request.Mobile,
		FullName:           panDTO.FullName,
		NSDLSeededCheckTS:  panDTO.NSDLSeededCheckTS,
		IsAadhaarPanSeeded: panDTO.IsAadhaarPanSeeded,
		Pan:                panDTO.Pan,
	}
	return data
}

func parseAndPopulateDigioRedirectURLData(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) error {
	// decrypt the request data.
	digioRedirectURLDataString, err := security.DecryptAES([]byte(config.Security().AESEncryptionKey), request.RequestData)
	if err != nil {
		return err
	}
	var digioRedirectURLData business.DigioRedirectURLData
	err = goUtils.UnmarshalJSON([]byte(digioRedirectURLDataString), &digioRedirectURLData)
	if err != nil {
		return err
	}
	err = digioRedirectURLData.Validate()
	if err != nil {
		log.Error(ctx).Err(err).Msg("parseAndPopulateDigioRedirectURLData: invalid digio redirect URL data")
		return err
	}
	err = objectmapper.GetMapperInstance().AutoMapper(&digioRedirectURLData, request)
	if err != nil {
		return constants.ErrMappingFailed.WithDetails(err.Error())
	}
	return err
}

func getPanDTOFromRedirectDigioRequest(req *modelsAPIv1.DigioRedirectAPIRequest) *business.Pan {
	return &business.Pan{
		FullName:           req.FullName,
		NSDLSeededCheckTS:  req.NSDLSeededCheckTS,
		IsAadhaarPanSeeded: req.IsAadhaarPanSeeded,
		BOUserID:           req.BOUserID,
		Pan:                req.Pan,
		AppNumber:          req.AppNumber,
	}
}

func getPanDTOs(panDoc *business.PanDoc, pan *business.Pan, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) (*business.Pan, *business.PanDoc, error) {
	careOf := pan.FatherName

	panDoc.FatherName = careOf
	panDoc.DOB = digioStatusResponse.Details.Pan.Dob
	panDoc.FullName = digioStatusResponse.Details.Pan.Name
	panDoc.ImagePath = fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
		constants.AadhaarDigioDigilockerPanFilename, constants.AadhaarDigilockerPanPDFExt)
	panDoc.GuardianType = constants.FatherGuardianType
	panDoc.Source = constants.PanDigio
	panDoc.LastUpdateSource = request.Source
	panDoc.CreateSource = request.Source
	err := objectmapper.GetMapperInstance().AutoMapper(panDoc, pan)
	if err != nil {
		return pan, panDoc, err
	}
	return pan, panDoc, nil
}

func getNRIPanDTOs(panDoc *business.PanDoc, pan *business.Pan, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) (*business.Pan, *business.PanDoc) {
	careOf := pan.FatherName

	panDoc.FatherName = careOf
	panDoc.DOB = digioStatusResponse.Details.Pan.Dob
	panDoc.FullName = digioStatusResponse.Details.Pan.Name
	panDoc.ImagePath = fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
		constants.AadhaarDigioDigilockerPanFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerPanPDFExt)
	panDoc.GuardianType = constants.FatherGuardianType
	panDoc.Source = constants.PanDigio
	panDoc.LastUpdateSource = request.Source
	panDoc.CreateSource = request.Source
	pan.ImagePath = panDoc.ImagePath
	return pan, panDoc
}

func getOnboardingPanDTOs(panDoc *business.PanDoc, pan *business.Pan, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions, aadhaarDetails *business.Certificate) (*business.Pan, *business.PanDoc, error) {
	digioPan := &digioStatusResponse.Details.Pan
	aadhaarCareOf := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Co
	var guardianName string
	if strings.Contains(aadhaarCareOf, "/") { // only when guardian's name contains s/o, d/o, c/o
		co := strings.Split(aadhaarCareOf, " ")
		guardianName = strings.TrimSpace(strings.ToUpper(strings.Join(co[1:], " ")))
	}
	if !utils.IsValidAgainstRegex(config.Application().Validations.NameRegex, guardianName) {
		guardianName = constants.Empty
	}
	panDoc.FatherName = guardianName
	panDoc.DOB = digioPan.Dob
	panDoc.FullName = digioPan.Name
	panDoc.ImagePath = fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
		constants.AadhaarDigioDigilockerPanFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerPanPDFExt)
	panDoc.GuardianType = constants.FatherGuardianType
	panDoc.Source = constants.PanDigio
	panDoc.LastUpdateSource = request.Source
	panDoc.CreateSource = request.Source
	panDoc.Pan = digioPan.IDNumber
	err := objectmapper.GetMapperInstance().AutoMapper(panDoc, pan)
	if err != nil {
		return pan, panDoc, err
	}
	return pan, panDoc, nil
}

func getOnboardingPOADetailsPanDTOs(panDoc *business.PanDoc, pan *business.Pan, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions, aadhaarDetails *business.Certificate) (*business.Pan, *business.PanDoc, error) {
	aadhaarCareOf := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Co
	var guardianName string
	if strings.Contains(aadhaarCareOf, "/") { // only when guardian's name contains s/o, d/o, c/o
		co := strings.Split(aadhaarCareOf, " ")
		guardianName = strings.TrimSpace(strings.ToUpper(strings.Join(co[1:], " ")))
	}
	if !utils.IsValidAgainstRegex(config.Application().Validations.NameRegex, guardianName) {
		guardianName = constants.Empty
	}
	panDoc.FatherName = guardianName
	panDoc.DOB = digioStatusResponse.Details.Aadhaar.Dob
	panDoc.FullName = digioStatusResponse.Details.Aadhaar.Name
	panDoc.GuardianType = constants.FatherGuardianType
	panDoc.Source = constants.PanDigio
	panDoc.LastUpdateSource = request.Source
	panDoc.CreateSource = request.Source
	err := objectmapper.GetMapperInstance().AutoMapper(panDoc, pan)
	if err != nil {
		return pan, panDoc, err
	}
	return pan, panDoc, nil
}

func uploadPANAndAadhaarToS3(ctx context.Context, aadhaarPhotoPath, aadhaarXMLPath, panPath string, response *modelsV1.Actions,
	aadhaarFileResponse business.ChannelDigioMedia, panFileResponse business.ChannelDigioMedia) error {
	// Upload aadhaar photo to s3.
	err := s3.GetS3Client().UploadBase64File(ctx, aadhaarPhotoPath, []byte(response.Details.Aadhaar.Image))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading photo to s3")
		return err
	}

	// upload aadhaar xml.
	err = s3.GetS3Client().Upload(ctx, aadhaarXMLPath, bytes.NewReader(aadhaarFileResponse.File))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading aadhaar xml to s3")
		return err
	}

	// upload pan.
	if panFileResponse.Error == nil {
		err = s3.GetS3Client().Upload(ctx, panPath, bytes.NewReader(panFileResponse.File))
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading pan to s3")
			return err
		}
	}

	return nil
}

func nriUploadPANAndAadhaarToS3(ctx context.Context, aadhaarPhotoPath, aadhaarXMLPath, panPath string, response *modelsV1.Actions,
	aadhaarFileResponse business.ChannelDigioMedia, panFileResponse business.ChannelDigioMedia) (error, error, error) {
	// Upload aadhaar photo to s3.
	err := s3.GetS3Client().UploadBase64File(ctx, aadhaarPhotoPath, []byte(response.Details.Aadhaar.Image))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading photo to s3")
		return err, nil, nil
	}

	// upload aadhaar xml.
	err = s3.GetS3Client().Upload(ctx, aadhaarXMLPath, bytes.NewReader(aadhaarFileResponse.File))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading aadhaar xml to s3")
		return nil, err, nil
	}

	// upload pan.
	if panFileResponse.Error == nil {
		err = s3.GetS3Client().Upload(ctx, panPath, bytes.NewReader(panFileResponse.File))
		if err != nil {
			log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading pan to s3")
			return nil, nil, err
		}
	}

	return nil, nil, nil
}

func uploadAadhaarToS3(ctx context.Context, aadhaarPhotoPath, aadhaarXMLPath string, response *modelsV1.Actions,
	aadhaarFileResponse business.ChannelDigioMedia) error {
	// Upload aadhaar photo to s3.
	err := s3.GetS3Client().UploadBase64File(ctx, aadhaarPhotoPath, []byte(response.Details.Aadhaar.Image))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadAadhaarToS3: error uploading photo to s3")
		return err
	}

	// upload aadhaar xml.
	err = s3.GetS3Client().Upload(ctx, aadhaarXMLPath, bytes.NewReader(aadhaarFileResponse.File))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadAadhaarToS3: error uploading aadhaar xml to s3")
		return err
	}

	return nil
}

func validatePANStatus(panData *business.Pan) error {
	if panData.SEBIStatus != entities.SEBINotBannedStatus {
		return constants.ErrSEBICheck.WithMessage("SEBI banned check failed")
	}

	if panData.NSDLStatus != entities.NSDLPanValidStatus {
		return constants.ErrNSDLCheck.WithMessage("NSDL validation failed")
	} else if !panData.IsAadhaarPanSeeded {
		return constants.ErrPANAadhaarSeeding.WithMessage("PAN and Aadhaar are not seeded")
	}

	if panData.PANDuplicateStatus != entities.PANNotDuplicateStatus {
		return constants.ErrDuplicatePAN.WithMessage("PAN duplicate check failed")
	}
	return nil
}

func getEncryptedDigioRedirectDataString(request *modelsAPIv1.InitiateDigioRequest, panDTO *business.Pan) (string, error) {
	// construct request.
	data := getDigioRedirectData(request, panDTO)
	dataJSON, err := goUtils.MarshalJSON(data)
	if err != nil {
		return constants.Empty, err
	}
	dataEncryptedString, err := security.EncryptAES([]byte(config.Security().AESEncryptionKey), string(dataJSON))
	if err != nil {
		return constants.Empty, err
	}
	return dataEncryptedString, nil
}

func getEncryptedNRIDigioRedirectDataString(request *modelsAPIv1.InitiateNRIDigioRequest, panDTO *business.Pan) (string, error) {
	// construct request.
	data := getNRIDigioRedirectData(request, panDTO)
	dataJSON, err := goUtils.MarshalJSON(data)
	if err != nil {
		return constants.Empty, err
	}
	dataEncryptedString, err := security.EncryptAES([]byte(config.Security().AESEncryptionKey), string(dataJSON))
	if err != nil {
		return constants.Empty, err
	}
	return dataEncryptedString, nil
}

func getEmodDigioLoginURL(encryptedDigioRedirectDataString string, request *modelsAPIv1.EmodInitiateDigioRequest, response *modelsV1.DigioInitiateResponse) string {
	baseRedirectURL := config.Digio().Flow.Emod.Intent[request.Intent].CallBackURL

	digioBaseLoginURL := config.Digio().BaseLogInURL

	digioLoginURL := fmt.Sprintf("%s%s/%s/%s", digioBaseLoginURL, response.ID, response.TransactionID, response.CustomerIdentifier)

	digioLoginQueryParams := getEmodDigioLoginQueryParams(encryptedDigioRedirectDataString, request, response, baseRedirectURL)

	digioLoginURLWithQueryParams := fmt.Sprintf("%s?%s", digioLoginURL, digioLoginQueryParams.Encode())
	return digioLoginURLWithQueryParams
}

func getEmodDigioLoginQueryParams(encryptedDigioRedirectDataString string, request *modelsAPIv1.EmodInitiateDigioRequest, response *modelsV1.DigioInitiateResponse, baseRedirectURL string) url.Values {
	redirectURLQuery := url.Values{}
	redirectURLQuery.Add(constants.DigioEncryptedRedirectDataStringQueryParamKey, encryptedDigioRedirectDataString)
	redirectURLQuery.Add(constants.DigioSourceQueryParamKey, request.Source)
	kycRedirectURL := fmt.Sprintf("%s?%s", baseRedirectURL, redirectURLQuery.Encode())

	digioLoginQuery := url.Values{}
	digioLoginQuery.Add(constants.DigioRedirectURLQueryParamKey, kycRedirectURL)
	digioLoginQuery.Add(constants.DigioTokenIDQueryParamKey, response.AccessToken.ID)

	additionalQueryParams := config.Digio().QueryParameters
	for key, value := range additionalQueryParams {
		digioLoginQuery.Add(key, value)
	}
	return digioLoginQuery
}

func getEmodDigioInitiateRequest(request *modelsAPIv1.EmodInitiateDigioRequest, panDTO *business.Pan) modelsV1.DigioInitiateRequest {
	return modelsV1.DigioInitiateRequest{
		CustomerIdentifier:  request.Mobile,
		TemplateName:        config.Digio().TemplateName,
		NotifyCustomer:      config.Digio().NotifyCustomer,
		GenerateAccessToken: config.Digio().GenerateAccessToken,
		AppNumber:           request.AppNumber,
		DigilockerDocumentAttributes: modelsV1.DigiLockerAttributeData{
			PAN: modelsV1.PANData{
				PANNumber: panDTO.Pan,
				Name:      panDTO.FullName,
			},
		},
	}
}

func getEmodDigioRedirectData(request *modelsAPIv1.EmodInitiateDigioRequest) business.DigioRedirectURLData {
	data := business.DigioRedirectURLData{
		Source:    request.Source,
		Platform:  request.Platform,
		AppNumber: request.AppNumber,
		Mobile:    request.Mobile,
		Intent:    request.Intent,
	}
	return data
}

func addAddressQueryParamsEmod(d *DigioEmodBusinessLogic, ctx context.Context, appNumber string, query url.Values) url.Values {
	address, err := d.addressRepository.FetchAddress(ctx, appNumber)
	if err != nil {
		log.Error(ctx).Msg("RedirectionFromDigio: unable to fetch address")
	}
	query.Add(constants.AddressLine1Key, address.AddressLine1)
	query.Add(constants.AddressLine2Key, address.AddressLine2)
	query.Add(constants.AddressLine3Key, address.AddressLine3)
	query.Add(constants.City, address.City)
	query.Add(constants.Pincode, address.Pincode)
	query.Add(constants.State, address.State)
	query.Add(constants.Country, address.Country)
	return query
}

func parseAndPopulateEmodDigioRedirectURLData(ctx context.Context, request *modelsAPIv1.EmodDigioRedirectAPIRequest) error {
	// decrypt the request data.
	digioRedirectURLDataString, err := security.DecryptAES([]byte(config.Security().AESEncryptionKey), request.RequestData)
	if err != nil {
		return err
	}
	var digioRedirectURLData business.DigioRedirectURLData
	err = goUtils.UnmarshalJSON([]byte(digioRedirectURLDataString), &digioRedirectURLData)
	if err != nil {
		return err
	}
	err = digioRedirectURLData.Validate()
	if err != nil {
		log.Error(ctx).Err(err).Msg("parseAndPopulateEmodDigioRedirectURLData: invalid digio redirect URL data")
		return err
	}
	err = objectmapper.GetMapperInstance().AutoMapper(&digioRedirectURLData, request)
	if err != nil {
		return constants.ErrMappingFailed.WithDetails(err.Error())
	}
	return err
}

func getPanDTOsEmod(panDoc *business.PanDoc, pan *business.Pan, request *modelsAPIv1.EmodDigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) (*business.Pan, *business.PanDoc, error) {
	careOf := pan.FatherName

	panDoc.FatherName = careOf
	panDoc.DOB = digioStatusResponse.Details.Pan.Dob
	panDoc.FullName = pan.FullName
	panDoc.ImagePath = fmt.Sprintf("%s/%s/%s%s", request.AppNumber, constants.PanDigio,
		constants.AadhaarDigioDigilockerPanFilename, constants.AadhaarDigilockerPanPDFExt)
	panDoc.GuardianType = constants.FatherGuardianType
	panDoc.Source = constants.PanDigio
	panDoc.LastUpdateSource = request.Source
	panDoc.CreateSource = request.Source
	err := objectmapper.GetMapperInstance().AutoMapper(panDoc, pan)
	if err != nil {
		return pan, panDoc, err
	}
	return pan, panDoc, nil
}

func uploadPANAndAadhaarToS3Emod(ctx context.Context, aadhaarPhotoPath, aadhaarXMLPath, panPath string, response *modelsV1.Actions,
	aadhaarFileResponse business.ChannelDigioMedia, panFileResponse business.ChannelDigioMedia) error {
	// Upload aadhaar photo to s3.
	err := s3.GetS3Client().UploadBase64File(ctx, aadhaarPhotoPath, []byte(response.Details.Aadhaar.Image))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading photo to s3")
		return err
	}

	// upload aadhaar xml.
	err = s3.GetS3Client().Upload(ctx, aadhaarXMLPath, bytes.NewReader(aadhaarFileResponse.File))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading aadhaar xml to s3")
		return err
	}

	// upload pan.
	err = s3.GetS3Client().Upload(ctx, panPath, bytes.NewReader(panFileResponse.File))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("uploadFilesToS3: error uploading pan to s3")
		return err
	}

	return nil
}

func getEmodEncryptedDigioRedirectDataString(request *modelsAPIv1.EmodInitiateDigioRequest) (string, error) {
	// construct request.
	data := getEmodDigioRedirectData(request)

	dataJSON, err := goUtils.MarshalJSON(data)
	if err != nil {
		return constants.Empty, err
	}
	dataEncryptedString, err := security.EncryptAES([]byte(config.Security().AESEncryptionKey), string(dataJSON))
	if err != nil {
		return constants.Empty, err
	}
	return dataEncryptedString, nil
}

func getPanDTOFromInitiateDigioRequest(req *modelsAPIv1.InitiateDigioRequest) business.Pan {
	return business.Pan{
		AppNumber:    req.AppNumber,
		Pan:          strings.ToUpper(req.Data.Pan),
		GuardianType: constants.FatherGuardianType,
		Source:       constants.PanDigio,
		//FatherName:       req.Data.FatherName,
		LastUpdateSource: req.Source,
		CreateSource:     req.Source,
	}
}

func getPersonalDTOFromDigio(request *modelsAPIv1.DigioRedirectAPIRequest, aadhaarDetails *business.Certificate) *business.Personal {
	personal := &business.Personal{
		AppNumber:        request.AppNumber,
		CreateTS:         time.Time{},
		CreateSource:     request.Source,
		LastUpdateTS:     time.Time{},
		LastUpdateSource: request.Source,
	}
	aadhaarCareOf := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Co
	var guardianName string
	if strings.Contains(aadhaarCareOf, "/") { // only when guardian's name contains s/o, d/o, c/o
		co := strings.Split(aadhaarCareOf, " ")
		guardianName = strings.TrimSpace(strings.ToUpper(strings.Join(co[1:], " ")))
	}
	if !utils.IsValidAgainstRegex(config.Application().Validations.NameRegex, guardianName) {
		guardianName = constants.Empty
	}
	personal.GuardianName = guardianName
	personal.GuardianType = "F"
	return personal
}

func getPanDocDTOFromPanDTO(panDTO *business.Pan) (business.PanDoc, error) {
	var panDoc business.PanDoc
	err := mapper.AutoMapper(panDTO, &panDoc)
	if err != nil {
		return panDoc, constants.ErrMappingFailed.WithDetails(err)
	}
	return panDoc, nil
}

func getResponseForFulfilmentCases(redirectURL string) (*modelsAPIv1.DigioRedirectResponse, error) {
	query := url.Values{}
	query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
	query.Add(constants.Message, "Application is submitted")
	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return &modelsAPIv1.DigioRedirectResponse{
		RedirectURL: redirectURLWithQueryParams,
	}, nil
}

func getClientOptOutDTO(req *modelsAPIv1.DigioSkipRequest, flow string) *business.ClientOptOut {
	return &business.ClientOptOut{
		AppNumber:        req.AppNumber,
		Flow:             flow,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
	}
}
