package digio

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

func (n *NRIDigioBusinessLogic) InitiateDigio(ctx context.Context, request *modelsAPIv1.InitiateNRIDigioRequest) (*modelsAPIv1.InitiateDigioResponse, error) {
	panDTO, err := n.initiatePanDetails(ctx, request)
	if err != nil {
		return nil, err
	}
	digioInitiateRequest := getNRIDigioInitiateRequest(request, panDTO)
	response, err := n.digioExternalProvider.Initiate(ctx, &digioInitiateRequest)
	if err != nil {
		return nil, err
	}

	encryptedDigioRedirectDataString, err := getEncryptedNRIDigioRedirectDataString(request, panDTO)
	if err != nil {
		return nil, err
	}

	digioLoginURL := getNRIDigioLoginURL(encryptedDigioRedirectDataString, request, &response)
	err = n.panDocRepository.UpdatePanDocWithDigioRequestID(request.AppNumber, constants.PanDigio, request.Source, response.ID, constants.DigioInitiated)
	if err != nil {
		return nil, err
	}

	return &modelsAPIv1.InitiateDigioResponse{
		RedirectURL: digioLoginURL,
	}, nil
}

func (n *NRIDigioBusinessLogic) RedirectionFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (*modelsAPIv1.DigioRedirectResponse, error) {
	err := parseAndPopulateDigioRedirectURLData(ctx, request)
	if err != nil {
		return nil, err
	}

	err = n.panDocRepository.UpdateDigioStatusWithAppNumberAndSource(request.AppNumber, constants.PanDigio, request.Source, constants.DigioRedirected, request.DigioDocID)
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.CorrelationIDContextKey, request.AppNumber)
	redirectURL := config.Digio().Flow.NRI.Redirect.URL
	query := url.Values{}
	if strings.EqualFold(constants.Success, request.Status) {
		panDoc, err := n.panDocRepository.GetPANDocWithDigioRequestID(ctx, constants.PanDigio, request.DigioDocID, request.AppNumber)
		if err != nil {
			return nil, err
		}
		panDTO := getPanDTOFromRedirectDigioRequest(request)
		digioStatusResponse, err := n.getDigioKycStatusResponse(ctx, request)
		if err != nil {
			return nil, err
		}
		query = n.getQueryParamsForDigioSuccess(ctx, request, &panDoc, panDTO, &digioStatusResponse)
	} else {
		log.Info(ctx).Msg("RedirectionFromDigio: digio failure")
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.NRI.Redirect.Message.Failure)
	}

	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	return &modelsAPIv1.DigioRedirectResponse{
		RedirectURL: redirectURLWithQueryParams,
	}, nil
}

func (n *NRIDigioBusinessLogic) SkipDigio(ctx context.Context, request *modelsAPIv1.DigioSkipRequest) error {
	if !request.Data.IsOptIn {
		clientOptOutDTO := getClientOptOutDTO(request, constants.NriDigioFlowName)
		err := n.poaAndClientOptOutRepository.SaveOptOutAndDeleteNRIDigio(ctx, clientOptOutDTO)
		if err != nil {
			log.Error(ctx).Err(err).Msg("SkipDigio: error inserting client opt out data and deleting poa data")
			return err
		}
	} else {
		err := n.clientOptOutRepository.Delete(ctx, request.AppNumber, constants.NriDigioFlowName)
		if err != nil {
			log.Error(ctx).Err(err).Msg("SkipDigio: error deleting client opt out data")
			return err
		}
	}
	return nil
}

func (n *NRIDigioBusinessLogic) getQueryParamsForDigioSuccess(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest, panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) url.Values {
	query := url.Values{}
	digioPanID := digioStatusResponse.Details.Pan.IDNumber
	if digioPanID != constants.Empty && !strings.EqualFold(panDoc.Pan, digioPanID) {
		log.Error(ctx).Msg("RedirectionFromDigio: digio pan doesnt match w/ user")
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.NRI.Redirect.Message.PanMisMatch)
	} else {
		err := n.getPANAndAadhaarDetailsFromDigio(ctx, request, panDoc, panData, digioStatusResponse)
		if err != nil {
			log.Info(ctx).Err(err).Msg("RedirectionFromDigio: digio pan / aadhaar fetch failure")
			failureMessage := config.Digio().Flow.NRI.Redirect.Message.Failure
			errMessage := err.Error()
			if errMessage != constants.Empty {
				failureMessage = errMessage
			}
			query.Add(constants.FailureCallBackParam, failureMessage)
		} else {
			log.Info(ctx).Msg("RedirectionFromDigio: digio success")
			query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
			query.Add(constants.Message, config.Digio().Flow.NRI.Redirect.Message.Success)
		}
	}
	return query
}

func (n *NRIDigioBusinessLogic) getPANAndAadhaarDetailsFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest,
	panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) error {
	var err error
	// fetch the aadhaar xml and pan from digio.
	chAadhaar := make(chan business.ChannelDigioMedia)
	chPAN := make(chan business.ChannelDigioMedia)

	// get PAN from digio.
	go n.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "false", constants.Pan, chPAN)
	// get Aadhaar from digio.
	go n.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "true", constants.Aadhaar, chAadhaar)

	aadhaarFileResponse := <-chAadhaar
	panFileResponse := <-chPAN

	// parse aadhaar byte array.
	var aadhaarDetails business.Certificate

	var poa business.POA
	if aadhaarFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(aadhaarFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting aadhaar xml file from digio")
	} else {
		poa = n.getPOADTO(request, digioStatusResponse)
	}

	err = xml.Unmarshal(aadhaarFileResponse.File, &aadhaarDetails)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error unmarshalling aadhaar XML")
	}

	var panDocumentDTO business.NRIDocument
	if panFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(panFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting PAN PDF from digio")
	} else {
		panData, panDoc = getNRIPanDTOs(panDoc, panData, request, digioStatusResponse)
		panDocumentDTO = n.getPanDocumentDTO(panDoc, request)
	}

	// return if both failed
	if panFileResponse.Error != nil && aadhaarFileResponse.Error != nil {
		return panFileResponse.Error
	}

	poaPhotoUploadErr, poaSecondUploadErr, panUploadErr := nriUploadPANAndAadhaarToS3(ctx, poa.PhotoPath, poa.SecondPath, panDoc.ImagePath,
		digioStatusResponse, aadhaarFileResponse, panFileResponse)
	if poaPhotoUploadErr != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading poa Photo to s3")
		poa.PhotoPath = constants.Empty
	}
	if poaSecondUploadErr != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading poa second to s3")
		poa.SecondPath = constants.Empty
	}
	if panUploadErr != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading pan to s3")
		panDoc.ImagePath = constants.Empty
		panDocumentDTO.FrontPath = constants.Empty
	}
	if panFileResponse.Error != nil {
		panData.Pan = constants.Empty
		panDoc.Pan = constants.Empty
	}

	if panDoc.Pan == constants.Empty && poa.PhotoPath == constants.Empty && poa.SecondPath == constants.Empty {
		// return error if uploading both poa and pan failed
		return panUploadErr
	}
	// save the records.
	err = n.digioRepository.UpsertNRIDigioEntities(ctx, panData, panDoc, &poa, &panDocumentDTO)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error saving records in DB")
		err = constants.ErrDatabase.WithDetails(err.Error())
		return err
	}
	return nil
}

func (n *NRIDigioBusinessLogic) getDigioKycStatusResponse(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (modelsV1.Actions, error) {
	digioResponse := modelsV1.Actions{}
	externalResponse, err := n.digioExternalProvider.GetKycStatusResponse(ctx, modelsV1.DigioKycStatusRequest{
		DigioRequestID: request.DigioDocID,
		AppNumber:      request.AppNumber,
	})

	if err != nil {
		log.Error(ctx).Err(err).Msg("getPANAndAadhaarDetilsFromDigio: error getting external PAN and aadhaar details from Digio")
		return digioResponse, err
	}

	if len(externalResponse.Actions) == 0 {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: external PAN and aadhaar details API didn't action")
		return digioResponse, constants.ErrEmptyDigioAction.WithMessage("Empty Digio action")
	}
	digioResponse = externalResponse.Actions[0]
	return digioResponse, nil
}

func (n *NRIDigioBusinessLogic) getDigioMedia(ctx context.Context, executionRequestID string, request *modelsAPIv1.DigioRedirectAPIRequest,
	isXML string, docType string, ch chan<- business.ChannelDigioMedia) {
	response, err := n.digioExternalProvider.GetMediaForPanAadhaar(ctx, modelsV1.DigioKYCMediaRequest{
		ExecutionRequestID: executionRequestID,
		AppNumber:          request.AppNumber,
		XML:                isXML,
		DocType:            docType,
	})

	if err != nil {
		log.Error(ctx).Stack().Err(err).Str("doc_type", docType).Msg("getDigioMedia: getting media from digio")
		ch <- business.ChannelDigioMedia{File: nil, Error: err}
		return
	}
	ch <- business.ChannelDigioMedia{File: response.FileBytes, Error: nil}
}

func (n *NRIDigioBusinessLogic) getPanDocumentDTO(panDoc *business.PanDoc, request *modelsAPIv1.DigioRedirectAPIRequest) business.NRIDocument {
	return business.NRIDocument{
		AppNumber:        request.AppNumber,
		Type:             constants.NRIDocumentPanType,
		SubType:          constants.NRIDocumentPanSubType,
		FrontPath:        panDoc.ImagePath,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
}

func (n *NRIDigioBusinessLogic) getPOADTO(request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) business.POA {
	poa := business.POA{
		AppNumber: request.AppNumber,
		ID:        digioStatusResponse.Details.Aadhaar.IDNumber,
		FullName:  strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Name),
		Type:      constants.DigilockerPOA,
		FirstPath: fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerPhotoFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerPhotoFileExt),
		SecondPath: fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerXMLFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerXMLFileExt),
		Dob:              strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Dob),
		Source:           constants.AadhaarDigio,
		Gender:           n.commonDigioBusinessLogic.GetGenderValueForAadhaarFromDigioGenderString(digioStatusResponse.Details.Aadhaar.Gender),
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	poa.PhotoPath = poa.FirstPath
	return poa
}

func (n *NRIDigioBusinessLogic) initiatePanDetails(ctx context.Context, request *modelsAPIv1.InitiateNRIDigioRequest) (*business.Pan, error) {
	panDTO, err := n.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
	if err != nil {
		return &panDTO, err
	}
	panDocDTO, err := getPanDocDTOFromPanDTO(&panDTO)
	if err != nil {
		return nil, err
	}
	panDocDTO.Source = constants.PanDigio
	err = n.panDocRepository.FreshUpsert(ctx, &panDocDTO)
	if err != nil {
		return nil, err
	}

	return &panDTO, nil
}
