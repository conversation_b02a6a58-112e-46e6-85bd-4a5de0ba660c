package digio

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/url"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/business/v1/pan"

	"github.com/angel-one/kyc-user-journey/entities"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"

	"github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/nsdl"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

func (o *OnboardingDigioBusinessLogic) InitiateOnboardingDigio(ctx context.Context, request *modelsAPIv1.InitiateDigioRequest) (*modelsAPIv1.InitiateDigioResponse, error) {
	panDTO, err := o.initiatePanDetails(ctx, request)
	if err != nil {
		return nil, err
	}
	digioInitiateRequest := getDigioInitiateRequest(request, panDTO)

	if percentagerollout.IsFeatureRolloutEnabled(ctx, panDTO.AppNumber, constants.ApplicationRolloutPanImageConfigKey, config.Feature().Enable.PanImage, false) {
		digioInitiateRequest.TemplateName = config.Digio().OnlyPOA.TemplateName
		digioInitiateRequest.DigilockerDocumentAttributes.PAN.PANNumber = constants.Empty
		err = o.panRepository.FreshUpsertPOASource(ctx, &business.Pan{AppNumber: request.AppNumber, Pan: request.Data.Pan, FullName: constants.Empty, DOB: constants.Empty, Source: constants.OnlyAadhaarDigioSource, CreateSource: request.Source,
			LastUpdateSource: request.Source, IsNameMatch: false, IsDOBMatch: false, NameMatchSource: constants.Empty, DOBMatchSource: constants.Empty, FatherName: panDTO.FatherName, GuardianType: panDTO.GuardianType, IsAadhaarPanSeeded: panDTO.IsAadhaarPanSeeded, NSDLSeededCheckTS: panDTO.NSDLSeededCheckTS})
		if err != nil {
			return nil, err
		}
	}
	response, err := o.digioExternalProvider.Initiate(ctx, &digioInitiateRequest)
	if err != nil {
		go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, err.Error()))
		return nil, err
	}

	encryptedDigioRedirectDataString, err := getEncryptedDigioRedirectDataString(request, panDTO)
	if err != nil {
		return nil, err
	}

	digioLoginURL := getOnboardingDigioLoginURL(encryptedDigioRedirectDataString, request, &response)
	err = o.panDocRepository.UpdatePanDocWithDigioRequestID(request.AppNumber, constants.PanDigio, request.Source, response.ID, constants.DigioInitiated)
	if err != nil {
		return nil, err
	}

	return &modelsAPIv1.InitiateDigioResponse{
		RedirectURL: digioLoginURL,
	}, nil
}

func (o *OnboardingDigioBusinessLogic) RedirectionFromOnboardingDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (*modelsAPIv1.DigioRedirectResponse, error) {
	err := parseAndPopulateDigioRedirectURLData(ctx, request)
	if err != nil {
		return nil, err
	}

	err = o.panDocRepository.UpdateDigioStatusWithAppNumberAndSource(request.AppNumber, constants.PanDigio, request.Source, constants.DigioRedirected, request.DigioDocID)
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.CorrelationIDContextKey, request.AppNumber)
	redirectURL := config.Digio().Flow.Onboarding.Redirect.URL
	startDTO, err := o.startRepository.GetEntityForMobile(ctx, request.Mobile)
	if err != nil {
		return nil, err
	}

	if startDTO.ApplicationStatus == entities.StatusFulfilmentDone || startDTO.ApplicationStatus == entities.StatusFulfilmentInProgress || startDTO.ApplicationStatus == entities.StatusESignDone {
		return getResponseForFulfilmentCases(redirectURL)
	}

	panDoc, err := o.panDocRepository.GetPANDocWithDigioRequestID(ctx, constants.PanDigio, request.DigioDocID, request.AppNumber)
	if err != nil {
		return nil, err
	}
	panDTO := getPanDTOFromRedirectDigioRequest(request)
	digioStatusResponse, err := o.getDigioKycStatusResponse(ctx, request)
	if err != nil {
		go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, err.Error()))
		return nil, err
	}
	var query url.Values
	if percentagerollout.IsFeatureRolloutEnabled(ctx, panDTO.AppNumber, constants.ApplicationRolloutPanImageConfigKey, config.Feature().Enable.PanImage, false) {
		query = o.getPOAQueryParamsForDigioSuccess(ctx, request, &panDoc, panDTO, &digioStatusResponse)
	} else {
		query = o.getQueryParamsForDigioSuccess(ctx, request, &panDoc, panDTO, &digioStatusResponse)
	}

	// adding necessary params if the call was made from admin
	if slices.Contains(config.Application().Validations.AllowedAdminSources, request.Source) {
		query.Add(constants.Mobile, utils.HashUpperCaseWithSHA256(request.Mobile))
		query.Add(constants.AgentID, request.AgentID)

		redirectURL = config.Digio().Flow.Onboarding.RedirectAdmin.URL
	}

	redirectURLWithQueryParams := fmt.Sprintf("%s?%s", redirectURL, query.Encode())
	go func() {
		err = o.kraBusinessLogic.CheckAndFetchKRAInfo(ctx, request.AppNumber, request.Mobile, request.Source)
		if err != nil {
			log.Info(ctx).Err(err).Msg("Error checking and fetching kra info")
		}
	}()

	return &modelsAPIv1.DigioRedirectResponse{
		RedirectURL: redirectURLWithQueryParams,
	}, nil
}

func (o *OnboardingDigioBusinessLogic) initiatePanDetails(ctx context.Context, req *modelsAPIv1.InitiateDigioRequest) (*business.Pan, error) {
	panDTO := getPanDTOFromInitiateDigioRequest(req)
	panDocDTO, err := getPanDocDTOFromPanDTO(&panDTO)

	_ = o.getGuardianNameFallback(ctx, &panDTO, &panDocDTO)

	if err != nil {
		return nil, err
	}
	err = o.panDocRepository.FreshUpsert(ctx, &panDocDTO)
	if err != nil {
		return nil, err
	}

	// SEBI Ban PanDetails check
	isSEBIBanned, sebiBannedErr := o.sebiBannedPANRepository.IsSEBIBannedPAN(ctx, panDTO.Pan)

	if sebiBannedErr != nil {
		return nil, sebiBannedErr
	}
	if isSEBIBanned {
		if strings.HasPrefix(req.AppNumber, "EQ") {
			o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, req.AppNumber, constants.SebiBannedLSQEvent)
		}
		return nil, constants.ErrSEBIBannedPAN.WithDetails("This PAN is banned by SEBI")
	}
	isDuplicatePAN, checkIfDuplicatePanErr := o.panBusinessLogic.CheckIfDuplicatePANWithCLCM(ctx, req.AppNumber, panDTO.Pan)

	if checkIfDuplicatePanErr != nil {
		return nil, checkIfDuplicatePanErr
	}
	if isDuplicatePAN {
		if strings.HasPrefix(req.AppNumber, "EQ") {
			o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, req.AppNumber, constants.PanAlreadyExistsLSQEvent)
		}
		return nil, constants.ErrDuplicatePAN.WithDetails("This PAN is already used")
	}

	// NSDL PanDetails Validation
	inputData := modelsV1.NSDLVerifyPanV2InputData{
		Pan:  panDTO.Pan,
		Name: constants.NsdlDummyName,
		DOB:  constants.NsdlDummyDoB,
	}

	nsdlResponse, err := o.nsdlExternalProvider.ValidatePANV2(ctx, &modelsV1.NSDLVerifyPanV2Request{InputData: []modelsV1.NSDLVerifyPanV2InputData{inputData}}, nsdl.RoundRobinV2.Next())

	if err != nil {
		return nil, err
	}

	if len(nsdlResponse.Data) == 0 {
		return nil, constants.ErrNSDLPanValidation.WithDetails("Empty response from NSDL for pan validation")
	}

	if !nsdlResponse.Data[0].IsValid {
		if strings.HasPrefix(req.AppNumber, "EQ") {
			o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, req.AppNumber, constants.InvalidPanLSQEvent)
		}
		return nil, constants.ErrInValidPAN.WithMessage("Incorrect PAN. Enter valid PAN")
	}
	pan.UpdatePanDTOWithNSDLV2Response(nsdlResponse, &panDTO)
	return &panDTO, nil
}

func (o *OnboardingDigioBusinessLogic) getQueryParamsForDigioSuccess(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest, panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) url.Values {
	query := url.Values{}
	digioPanID := digioStatusResponse.Details.Pan.IDNumber
	if digioPanID != constants.Empty && !strings.EqualFold(panDoc.Pan, digioPanID) {
		if o.isDigioMandatory(ctx, panDoc.AppNumber, panDoc.LastUpdateSource) {
			query.Add(constants.ToastMessageParam, config.Digio().Flow.Onboarding.Redirect.Message.DigioMandatory)
		}
		query.Add(constants.FailureCallBackParam, config.Digio().Flow.Onboarding.Redirect.Message.PanMisMatch)
		go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, config.Digio().Flow.Onboarding.Redirect.Message.PanMisMatch))
	} else {
		err := o.getPANAndAadhaarDetailsFromDigio(ctx, request, panDoc, panData, digioStatusResponse)
		if err != nil {
			log.Info(ctx).Msg("RedirectionFromDigio: digio pan fetch failure")
			failureMessage := config.Digio().Flow.Onboarding.Redirect.Message.Failure
			errMessage := err.Error()
			if errMessage != constants.Empty {
				failureMessage = errMessage
			}
			if failureMessage != constants.ErrFetchingPan.Error() {
				go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, failureMessage))
			} else {
				go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig))
			}
			if o.isDigioMandatory(ctx, panDoc.AppNumber, panDoc.LastUpdateSource) {
				query.Add(constants.ToastMessageParam, config.Digio().Flow.Onboarding.Redirect.Message.DigioMandatory)
			}
			query.Add(constants.FailureCallBackParam, failureMessage)
		} else {
			log.Info(ctx).Msg("RedirectionFromDigio: digio success")
			query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
			query.Add(constants.Message, config.Digio().Flow.Onboarding.Redirect.Message.Success)
			go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig))
		}
	}
	return query
}

func (o *OnboardingDigioBusinessLogic) getPANAndAadhaarDetailsFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest,
	panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) error {
	var err error
	dob := digioStatusResponse.Details.Pan.Dob
	if dob == constants.Empty {
		dob = digioStatusResponse.Details.Aadhaar.Dob
	}
	// validate the date of birth.
	if err = utils.IsDOBValid(dob, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge); err != nil {
		log.Error(ctx).Stack().Err(err).
			Str("dob", dob).
			Bool("invalidDOB", true).
			Msg("getPANAndAadhaarDetailsFromDigio: invalid DOB")
		if err.Error() != constants.ErrParsingDOB.Value().Error() {
			return constants.ErrInvalidDateOfBirth
		}

		if err.Error() == constants.ErrMinorApplication.Value().Error() {
			o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.MinorPanLSQEvent)
		}
	}

	// fetch the aadhaar xml and pan from digio.
	chAadhaar := make(chan business.ChannelDigioMedia)
	chPAN := make(chan business.ChannelDigioMedia)

	// get PAN from digio.
	go o.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "false", constants.Pan, chPAN)
	// get Aadhaar from digio.
	go o.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "true", constants.Aadhaar, chAadhaar)

	aadhaarFileResponse := <-chAadhaar
	panFileResponse := <-chPAN

	var fetchError error

	// parse aadhaar byte array.
	var aadhaarDetails business.Certificate

	if aadhaarFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(aadhaarFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting aadhaar xml file from digio")
		return constants.ErrFetchingAadhaarFromDigio.WithDetails(aadhaarFileResponse.Error)
	}

	err = xml.Unmarshal(aadhaarFileResponse.File, &aadhaarDetails)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error unmarshalling aadhaar XML")
		return err
	}

	if panFileResponse.Error != nil {
		fetchError = constants.ErrFetchingPan
		log.Error(ctx).Stack().Err(panFileResponse.Error).Msg("getPANAndAadhaarDetailsFromDigio: error getting PAN PDF from digio")
	}

	permanentAddress, correspondenceAddress, poa := o.getPOAAndAddressDTOs(&aadhaarDetails, request, digioStatusResponse)
	panData, panDoc, err = getOnboardingPanDTOs(panDoc, panData, request, digioStatusResponse, &aadhaarDetails)
	if err != nil {
		return err
	}
	personalDTO := getPersonalDTOFromDigio(request, &aadhaarDetails)

	o.UpdatePanDetailsWithNSDLResponse(ctx, panDoc, panData, digioStatusResponse)

	err = uploadPANAndAadhaarToS3(ctx, poa.PhotoPath, poa.SecondPath, panDoc.ImagePath,
		digioStatusResponse, aadhaarFileResponse, panFileResponse)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error uploading files to s3")
		return err
	}

	if panFileResponse.Error != nil {
		panData.Pan = constants.Empty
		panDoc.Pan = constants.Empty
	}

	// If not able to fetch father name from digio but father name was already present, use the existing father name.
	if panData.FatherName == constants.Empty {
		panData.FatherName = request.GuardianName
		panData.GuardianType = request.GuardianType

		panDoc.FatherName = request.GuardianName
		panDoc.GuardianType = request.GuardianType

		personalDTO.GuardianName = request.GuardianName
		personalDTO.GuardianType = request.GuardianType
	}

	// save the records.
	err = o.digioRepository.UpsertDigioAndPersoanlEntities(ctx, panData, panDoc, &permanentAddress, &correspondenceAddress, &poa, personalDTO)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: error saving records in DB")
		err = constants.ErrDatabase.WithDetails(err.Error())
		return err
	}
	if strings.HasPrefix(request.AppNumber, "EQ") {
		o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.PanSuccessLSQEvent)
	}

	if fetchError != nil {
		return fetchError
	}
	go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismAppAddressMetadata(&business.PrismEventMessage{
		AppNumber: request.AppNumber, Pincode: permanentAddress.Pincode, City: permanentAddress.City, State: permanentAddress.State,
	}))
	return nil
}

func (o *OnboardingDigioBusinessLogic) getPOAQueryParamsForDigioSuccess(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest, panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) url.Values {
	query := url.Values{}
	digioPOAID := digioStatusResponse.Details.Aadhaar.IDNumber
	if digioPOAID == constants.Empty {
		if o.isDigioMandatory(ctx, panDoc.AppNumber, panDoc.LastUpdateSource) {
			query.Add(constants.ToastMessageParam, config.Digio().Flow.Onboarding.Redirect.Message.DigioMandatory)
		}
		query.Add(constants.FailureCallBackParam, config.Digio().OnlyPOA.Message.Failure)
		go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, config.Digio().OnlyPOA.Message.Failure))
	} else {
		err := o.getAadhaarDetailsFromDigio(ctx, request, panDoc, panData, digioStatusResponse)
		if err != nil {
			log.Info(ctx).Msg("getPOAQueryParamsForDigioSuccess: digio pan fetch failure")
			failureMessage := config.Digio().OnlyPOA.Message.Failure
			errMessage := err.Error()
			if errMessage != constants.Empty {
				failureMessage = errMessage
			}
			if o.isDigioMandatory(ctx, panDoc.AppNumber, panDoc.LastUpdateSource) {
				query.Add(constants.ToastMessageParam, config.Digio().Flow.Onboarding.Redirect.Message.DigioMandatory)
			}
			query.Add(constants.FailureCallBackParam, failureMessage)
			go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismErrorEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig, failureMessage))
		} else {
			log.Info(ctx).Msg("getPOAQueryParamsForDigioSuccess: digio success")
			query.Add(constants.SuccessCallBackParam, constants.SuccessStatus)
			query.Add(constants.Message, config.Digio().OnlyPOA.Message.Success)
			go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismSuccessEventData(request.AppNumber, constants.PrismPoaStepCompletionEvent, constants.PrismPoaStepCompletionEvent, constants.DigioConfig))
		}
	}
	return query
}

func (o *OnboardingDigioBusinessLogic) getAadhaarDetailsFromDigio(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest,
	panDoc *business.PanDoc, panData *business.Pan, digioStatusResponse *modelsV1.Actions) error {
	var err error
	dob := digioStatusResponse.Details.Aadhaar.Dob

	// validate the date of birth.
	if err = utils.IsDOBValid(dob, config.Application().Thresholds.MinimumAge, config.Application().Thresholds.MaximumAge); err != nil {
		log.Error(ctx).Stack().Err(err).
			Str("dob", dob).
			Bool("invalidDOB", true).
			Msg("getAadhaarDetailsFromDigio: invalid DOB")
		if err.Error() != constants.ErrParsingDOB.Value().Error() {
			return constants.ErrInvalidDateOfBirth
		}
	}

	// fetch the aadhaar xml and pan from digio.
	chAadhaar := make(chan business.ChannelDigioMedia)

	// get Aadhaar from digio.
	go o.getDigioMedia(ctx, digioStatusResponse.ExecutionRequestID, request, "true", constants.Aadhaar, chAadhaar)

	aadhaarFileResponse := <-chAadhaar

	var fetchError error

	// parse aadhaar byte array.
	var aadhaarDetails business.Certificate

	if aadhaarFileResponse.Error != nil {
		log.Error(ctx).Stack().Err(aadhaarFileResponse.Error).Msg("getAadhaarDetailsFromDigio: error getting aadhaar xml file from digio")
		return constants.ErrFetchingAadhaarFromDigio.WithDetails(aadhaarFileResponse.Error)
	}

	err = xml.Unmarshal(aadhaarFileResponse.File, &aadhaarDetails)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getAadhaarDetailsFromDigio: error unmarshalling aadhaar XML")
		return err
	}

	permanentAddress, correspondenceAddress, poa := o.getPOAAndAddressDTOs(&aadhaarDetails, request, digioStatusResponse)
	panData, panDoc, err = getOnboardingPOADetailsPanDTOs(panDoc, panData, request, digioStatusResponse, &aadhaarDetails)
	if err != nil {
		return err
	}
	panData.Source = constants.OnlyAadhaarDigioSource
	personalDTO := getPersonalDTOFromDigio(request, &aadhaarDetails)

	o.ValidateAndUpdateDigioPOADetailsWithNSDLResponse(ctx, panDoc, panData)

	err = uploadAadhaarToS3(ctx, poa.PhotoPath, poa.SecondPath,
		digioStatusResponse, aadhaarFileResponse)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getAadhaarDetailsFromDigio: error uploading files to s3")
		return err
	}

	// If not able to fetch father name from digio but father name was already present, use the existing father name.
	if panData.FatherName == constants.Empty {
		panData.FatherName = request.GuardianName
		panData.GuardianType = request.GuardianType

		panDoc.FatherName = request.GuardianName
		panDoc.GuardianType = request.GuardianType

		personalDTO.GuardianName = request.GuardianName
		personalDTO.GuardianType = request.GuardianType
	}

	// save the records.
	err = o.digioRepository.UpsertDigioAndPersoanlEntities(ctx, panData, panDoc, &permanentAddress, &correspondenceAddress, &poa, personalDTO)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("getAadhaarDetailsFromDigio: error saving records in DB")
		err = constants.ErrDatabase.WithDetails(err.Error())
		return err
	}

	if strings.HasPrefix(request.AppNumber, "EQ") {
		o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.PanSuccessLSQEvent)
	}

	if fetchError != nil {
		return fetchError
	}
	go o.prismSQSBusinessLogic.SendMessageToQueue(ctx, o.commonPrismBusinessLogic.GetPrismAppAddressMetadata(&business.PrismEventMessage{
		AppNumber: request.AppNumber, Pincode: permanentAddress.Pincode, City: permanentAddress.City, State: permanentAddress.State,
	}))
	return nil
}

func (o *OnboardingDigioBusinessLogic) getDigioKycStatusResponse(ctx context.Context, request *modelsAPIv1.DigioRedirectAPIRequest) (modelsV1.Actions, error) {
	digioResponse := modelsV1.Actions{}
	externalResponse, err := o.digioExternalProvider.GetKycStatusResponse(ctx, modelsV1.DigioKycStatusRequest{
		DigioRequestID: request.DigioDocID,
		AppNumber:      request.AppNumber,
	})

	if err != nil {
		log.Error(ctx).Err(err).Msg("getPANAndAadhaarDetilsFromDigio: error getting external PAN and aadhaar details from Digio")
		return digioResponse, err
	}

	if len(externalResponse.Actions) == 0 {
		log.Error(ctx).Stack().Err(err).Msg("getPANAndAadhaarDetailsFromDigio: external PAN and aadhaar details API didn't action")
		return digioResponse, constants.ErrEmptyDigioAction.WithMessage("Empty Digio action")
	}
	digioResponse = externalResponse.Actions[0]
	return digioResponse, nil
}

func (o *OnboardingDigioBusinessLogic) getDigioMedia(ctx context.Context, executionRequestID string, request *modelsAPIv1.DigioRedirectAPIRequest,
	isXML string, docType string, ch chan<- business.ChannelDigioMedia) {
	response, err := o.digioExternalProvider.GetMediaForPanAadhaar(ctx, modelsV1.DigioKYCMediaRequest{
		ExecutionRequestID: executionRequestID,
		AppNumber:          request.AppNumber,
		XML:                isXML,
		DocType:            docType,
	})

	if err != nil {
		log.Error(ctx).Stack().Err(err).Str("doc_type", docType).Msg("getDigioMedia: getting media from digio")
		ch <- business.ChannelDigioMedia{File: nil, Error: err}
		return
	}
	ch <- business.ChannelDigioMedia{File: response.FileBytes, Error: nil}
}

func (o *OnboardingDigioBusinessLogic) getPOAAndAddressDTOs(aadhaarDetails *business.Certificate, request *modelsAPIv1.DigioRedirectAPIRequest,
	digioStatusResponse *modelsV1.Actions) (business.Address, business.Address, business.POA) {
	poa := business.POA{
		AppNumber: request.AppNumber,
		ID:        digioStatusResponse.Details.Aadhaar.IDNumber,
		FullName:  strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Name),
		Type:      constants.DigilockerPOA,
		FirstPath: fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerPhotoFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerPhotoFileExt),
		SecondPath: fmt.Sprintf("%s/%s/%s%s%d%s", request.AppNumber, constants.PanDigio,
			constants.AadhaarDigilockerXMLFilename, "-", time.Now().Unix(), constants.AadhaarDigilockerXMLFileExt),
		Dob:              strings.TrimSpace(digioStatusResponse.Details.Aadhaar.Dob),
		Source:           constants.AadhaarDigio,
		Gender:           o.commonDigioBusinessLogic.GetGenderValueForAadhaarFromDigioGenderString(digioStatusResponse.Details.Aadhaar.Gender),
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	poa.PhotoPath = poa.FirstPath
	cityFromDigio := aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc
	cityFromDigio = strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(cityFromDigio, "")))
	if cityFromDigio == constants.Empty {
		cityFromDigio = aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist
	}
	city := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(cityFromDigio, " ")))
	state := strings.ToUpper(strings.TrimSpace(regexp.MustCompile(config.Application().Validations.AllowedCharacters.CityAndState).
		ReplaceAllString(strings.ReplaceAll(aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State, "&", "AND"), " ")))

	permanentAddress := business.Address{
		AppNumber: request.AppNumber,
		Type:      constants.PermanentAddressType,
		AddressLine1: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.House,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Street),
		AddressLine2: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Loc,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Vtc),
		AddressLine3: fmt.Sprintf("%s, %s", aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Dist,
			aadhaarDetails.CertificateData.KycRes.UIDData.Poa.State),
		City:             city,
		State:            state,
		Country:          aadhaarDetails.CertificateData.KycRes.UIDData.Poa.Country,
		Pincode:          digioStatusResponse.Details.Aadhaar.PermanentAddressDetails.Pincode,
		CreateSource:     request.Source,
		LastUpdateSource: request.Source,
	}
	correspondenceAddress := permanentAddress
	correspondenceAddress.Type = constants.CorrespondenceAddressType
	return permanentAddress, correspondenceAddress, poa
}

func (o *OnboardingDigioBusinessLogic) UpdatePanDetailsWithNSDLResponse(ctx context.Context, panDoc *business.PanDoc, panDTO *business.Pan, digioStatusResponse *modelsV1.Actions) {
	// NSDL PanDetails Validation
	if panDTO.Pan == constants.Empty {
		return
	}
	name := digioStatusResponse.Details.Pan.Name
	dob := digioStatusResponse.Details.Pan.Dob
	if name == constants.Empty {
		name = constants.NsdlDummyName
	}

	if dob == constants.Empty {
		dob = constants.NsdlDummyDoB
	}

	inputData := modelsV1.NSDLVerifyPanV2InputData{
		Pan:  panDTO.Pan,
		Name: name,
		DOB:  dob,
	}
	panDoc.FullName = constants.Empty
	panDTO.FullName = constants.Empty
	panDTO.DOB = constants.Empty
	panDoc.DOB = constants.Empty

	nsdlResponse, err := o.nsdlExternalProvider.ValidatePANV2(ctx, &modelsV1.NSDLVerifyPanV2Request{InputData: []modelsV1.NSDLVerifyPanV2InputData{inputData}}, nsdl.RoundRobinV2.Next())

	if err != nil {
		log.Error(ctx).Err(err).Msg("Error from NSDL V2 API")
		return
	}

	if len(nsdlResponse.Data) == 0 {
		log.Error(ctx).Msg("Empty response from NSDL V2 API")
		return
	}

	if nsdlResponse.Data[0].ISDoBMatch {
		panDoc.DOB = digioStatusResponse.Details.Pan.Dob
		panDTO.DOB = panDoc.DOB
		panDTO.IsDOBMatch = true
		panDTO.DOBMatchSource = constants.PanSource
	}

	if nsdlResponse.Data[0].IsNameMatch {
		panDoc.FullName = digioStatusResponse.Details.Pan.Name
		panDTO.FullName = panDoc.FullName
		panDTO.IsNameMatch = true
		panDTO.NameMatchSource = constants.PanSource
	}
}
func (o *OnboardingDigioBusinessLogic) ValidateAndUpdateDigioPOADetailsWithNSDLResponse(ctx context.Context, panDoc *business.PanDoc, panDTO *business.Pan) {
	// NSDL PanDetails Validation
	if panDTO.Pan == constants.Empty {
		return
	}
	name := panDTO.FullName
	dob := panDTO.DOB
	if name == constants.Empty {
		name = constants.NsdlDummyName
	}

	if dob == constants.Empty {
		dob = constants.NsdlDummyDoB
	}

	inputData := modelsV1.NSDLVerifyPanV2InputData{
		Pan:  panDTO.Pan,
		Name: name,
		DOB:  dob,
	}
	panDoc.FullName = constants.Empty
	panDTO.FullName = constants.Empty
	panDTO.DOB = constants.Empty
	panDoc.DOB = constants.Empty

	nsdlResponse, err := o.nsdlExternalProvider.ValidatePANV2(ctx, &modelsV1.NSDLVerifyPanV2Request{InputData: []modelsV1.NSDLVerifyPanV2InputData{inputData}}, nsdl.RoundRobinV2.Next())

	if err != nil {
		log.Error(ctx).Err(err).Msg("Error from NSDL V2 API")
		return
	}

	if len(nsdlResponse.Data) == 0 {
		log.Error(ctx).Msg("Empty response from NSDL V2 API")
		return
	}

	if nsdlResponse.Data[0].ISDoBMatch {
		panDoc.DOB = dob
		panDTO.DOB = panDoc.DOB
		panDTO.IsDOBMatch = true
		panDTO.DOBMatchSource = "PAN"
	}

	if nsdlResponse.Data[0].IsNameMatch {
		panDoc.FullName = name
		panDTO.FullName = panDoc.FullName
		panDTO.IsNameMatch = true
		panDTO.NameMatchSource = "PAN"
	}
}

func (o *OnboardingDigioBusinessLogic) InitiateOnboardingDigioForPanAlredyExistInSystem(ctx context.Context, request *modelsAPIv1.InitiateDigioRequest) (*modelsAPIv1.InitiateDigioResponse, error) {
	panDTO, err := o.fetchPanData(ctx, request)
	if err != nil {
		return nil, err
	}
	digioInitiateRequest := getDigioInitiateRequest(request, panDTO)
	response, err := o.digioExternalProvider.Initiate(ctx, &digioInitiateRequest)
	if err != nil {
		return nil, err
	}

	encryptedDigioRedirectDataString, err := getEncryptedDigioRedirectDataString(request, panDTO)
	if err != nil {
		return nil, err
	}

	digioLoginURL := getOnboardingDigioLoginURL(encryptedDigioRedirectDataString, request, &response)
	err = o.panDocRepository.UpdatePanDocWithDigioRequestIDAndPan(request.AppNumber, constants.PanDigio, request.Source, response.ID, constants.DigioInitiated, panDTO.Pan)
	if err != nil {
		return nil, err
	}
	return &modelsAPIv1.InitiateDigioResponse{
		RedirectURL: digioLoginURL,
	}, nil
}

func (o *OnboardingDigioBusinessLogic) fetchPanData(ctx context.Context, request *modelsAPIv1.InitiateDigioRequest) (*business.Pan, error) {
	panDTO, err := o.panRepository.FetchPanForAppNumberWithContext(ctx, request.AppNumber)
	if err != nil {
		return &panDTO, err
	}

	request.Data.Pan = panDTO.Pan
	// SEBI Ban PanDetails check
	isSEBIBanned, sebiBannedErr := o.sebiBannedPANRepository.IsSEBIBannedPAN(ctx, panDTO.Pan)

	if sebiBannedErr != nil {
		return nil, sebiBannedErr
	}
	if isSEBIBanned {
		if strings.HasPrefix(request.AppNumber, "EQ") {
			o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, request.AppNumber, constants.SebiBannedLSQEvent)
		}
		return nil, constants.ErrSEBIBannedPAN.WithDetails("This PAN is banned by SEBI")
	}
	return &panDTO, nil
}

func (o *OnboardingDigioBusinessLogic) getGuardianNameFallback(ctx context.Context, panDTO *business.Pan, panDocDTO *business.PanDoc) error {
	personal, err := o.personalRepository.GetPersonalData(ctx, panDTO.AppNumber)
	if err != nil {
		return err
	}

	// If Guardian Name is not already present, return.
	if personal.GuardianName == constants.Empty {
		return nil
	}

	// This means Guardian Name is present.
	panDTO.FatherName = personal.GuardianName
	panDTO.GuardianType = personal.GuardianType

	panDocDTO.FatherName = personal.GuardianName
	panDocDTO.GuardianType = personal.GuardianType

	return nil
}

func (o *OnboardingDigioBusinessLogic) isDigioMandatory(ctx context.Context, appNumber, source string) bool {
	var rolloutTime time.Time
	if v, ok := config.Feature().RolloutTimeStamp[constants.ApplicationRolloutDigioMandatoryConfigKey]; ok {
		rolloutTime = v
	}

	startDTO, err := o.startRepository.FetchStartEntryForAppNumberWithContext(ctx, appNumber)
	if err != nil {
		return false
	}

	poaDTO, err := o.poaRepository.FetchPOAForAppNumberWithContext(ctx, appNumber)
	if err != nil {
		return true
	}

	addressDTO, err := o.addressRepository.FetchAddress(ctx, appNumber)
	if err != nil {
		return true
	}

	oneKycVariables := o.getOneKycVariables(ctx, startDTO.Mobile)
	includeOneKycStatus := true
	if source == constants.KycAdminSource || source == constants.KycAdminSBSource {
		includeOneKycStatus = false
	}

	if config.Feature().Enable.Digio &&
		config.Feature().Enable.DigioMandatory &&
		!rolloutTime.IsZero() &&
		startDTO.CreateTS.After(rolloutTime) &&
		percentagerollout.IsFeatureRolloutEnabled(ctx, appNumber, constants.ApplicationRolloutDigioMandatoryConfigKey,
			config.Feature().Enable.DigioMandatory, false) &&
		startDTO.KycType == constants.DemEqDemMfJourney &&
		getPoaFlowStatus(&poaDTO, &addressDTO, includeOneKycStatus, oneKycVariables) == constants.OnboardingFlowStatusIncomplete {
		return true
	}
	return false
}

func (o *OnboardingDigioBusinessLogic) getOneKycVariables(ctx context.Context, mobile string) map[string]any {
	workflowInstances, err := o.workflowExternalProvider.GetProcessInstanceByBusinessKeyAndName(ctx, mobile, constants.OneKYCWorkflow)
	if err != nil {
		log.Error(ctx).Err(err).Msg("Error getting one kyc process instance")
		return nil
	}

	if len(workflowInstances) > 1 {
		log.Error(ctx).Err(err).Msg("Multiple one kyc workflow instances found")
		return nil
	}

	var variables map[string]any
	if len(workflowInstances) == 1 {
		variables, err = o.workflowExternalProvider.GetProcessInstanceVariables(ctx, workflowInstances[0].ID, nil)
		if err != nil {
			log.Error(ctx).Err(err).Msg("Error getting one kyc process variables")
			return nil
		}
	}

	return variables
}

func getPoaFlowStatus(poaDTO *business.POA, addressDTO *business.Address, includeOneKYCStatus bool, oneKYCVariables map[string]any) string {
	if includeOneKYCStatus && isUpdatedByAdmin(poaDTO.LastUpdateSource) {
		if poaFlowStatus, ok := oneKYCVariables[constants.PoaFlowStatusVariableKey]; ok {
			return getOneKYCFlowStatus(poaFlowStatus)
		}
	}
	if poaDTO.ID != constants.Empty && (poaDTO.Type == constants.AadhaarPOAType || poaDTO.FullName != constants.Empty) && poaDTO.Type != constants.Empty &&
		poaDTO.FirstPath != constants.Empty &&
		(poaDTO.Type != constants.DigilockerPOAType || (poaDTO.SecondPath != constants.Empty && poaDTO.PhotoPath != constants.Empty)) {
		if isAddressComplete(addressDTO) {
			return constants.OnboardingFlowStatusComplete
		}
	}

	return constants.OnboardingFlowStatusIncomplete
}

func isUpdatedByAdmin(source string) bool {
	if source == constants.KycAdminSource || source == constants.KycAdminSBSource || source == "kyc-services" {
		return true
	}
	return false
}

func getOneKYCFlowStatus(flowStatus any) string {
	if flowStatus.(string) != constants.ZERO {
		return constants.OnboardingFlowStatusComplete
	}
	return constants.OnboardingFlowStatusIncomplete
}

func isAddressComplete(dto *business.Address) bool {
	if dto.AddressLine1 != constants.Empty && dto.Pincode != constants.Empty {
		return true
	}

	return false
}
