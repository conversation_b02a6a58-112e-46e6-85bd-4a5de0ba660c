package digio

import (
	"context"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type PanRepository interface {
	FetchPanForAppNumberWithContext(ctx context.Context, appNumber string) (business.Pan, error)
	UpdateSEBIStatus(ctx context.Context, panDTO *business.Pan) error
	UpdateNSDLStatus(ctx context.Context, panDTO *business.Pan) error
	UpdateDuplicatePanStatus(ctx context.Context, panDTO *business.Pan) error
	FreshUpsert(ctx context.Context, panDTO *business.Pan) error
	FreshUpsertPOASource(ctx context.Context, panDTO *business.Pan) error
}

type PanDocRepository interface {
	UpdatePanDocWithDigioRequestID(appNumber, source, updateSource, digioRequestID, digioStatus string) error
	GetPANDocWithDigioRequestID(ctx context.Context, source, digioRequestID, appNumber string) (business.PanDoc, error)
	FreshUpsert(ctx context.Context, panDocDTO *business.PanDoc) error
	UpdateDigioStatusWithAppNumberAndSource(appNumber, source, updateSource, digioStatus, digioDocID string) error
	UpdatePanDocWithDigioRequestIDAndPan(appNumber, source, updateSource, digioRequestID, digioStatus, pan string) error
}

type PanAndPanDocRepository interface {
	FreshUpsert(ctx context.Context, panDTO *business.Pan, panDocDTO *business.PanDoc) error
}

type NsdlExternalProvider interface {
	ValidatePAN(ctx context.Context, request modelsV1.NSDLPanValidationRequest) (*modelsV1.NSDLPanValidationResponse, error)
	ValidatePANV2(ctx context.Context, request *modelsV1.NSDLVerifyPanV2Request, userName string) (*modelsV1.ValidatePanWithNSDLV2FinalResponse, error)
}

type PanBusinessLogic interface {
	CheckIfDuplicatePAN(ctx context.Context, appNumber, pan string) (*business.CheckIfDuplicatePanResponse, error)
	CheckIfDuplicatePANWithCLCM(ctx context.Context, appNumber, pan string) (bool, error)
}

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type DigioRepository interface {
	UpsertDigioEntities(ctx context.Context, panDTO *business.Pan, panDocDTO *business.PanDoc, permanentAddressDTO *business.Address,
		correspondenceAddressDTO *business.Address, PoaDTO *business.POA) error
	UpsertDigioAndPersoanlEntities(ctx context.Context, panDTO *business.Pan, panDocDTO *business.PanDoc, permanentAddressDTO *business.Address,
		correspondenceAddressDTO *business.Address, poaDTO *business.POA, personalDTO *business.Personal) error
	UpsertNRIDigioEntities(ctx context.Context, panDTO *business.Pan, panDocDTO *business.PanDoc,
		poaDTO *business.POA, panNRIDocument *business.NRIDocument) error
}

type SelfieRepository interface {
	UpdateSelfieMatch(ctx context.Context, matchScore int64, source, appNumber string) error
}

type PersonalRepository interface {
	GetPersonalData(ctx context.Context, appNumber string) (business.Personal, error)
}

type AddressRepository interface {
	FetchAddress(ctx context.Context, appNumber string) (business.Address, error)
}

type ClientOptOutRepository interface {
	Delete(ctx context.Context, appNumber, flow string) error
}

type PoaAndClientOptOutRepository interface {
	SaveOptOutAndDeleteNRIDigio(ctx context.Context, clientOptOutDTO *business.ClientOptOut) error
}

type SelfieBusinessLogic interface {
	Selfie(ctx context.Context, request *modelsAPIv1.SelfieAPIRequest) (*modelsAPIv1.SelfieAPIResponse, error)
	ValidateSelfieForAppNumber(ctx context.Context, appNumber string) (business.ValidateSelfieResponse, error)
}

type DigioExternalProvider interface {
	Initiate(ctx context.Context, request *modelsV1.DigioInitiateRequest) (modelsV1.DigioInitiateResponse, error)
	GetKycStatusResponse(ctx context.Context, request modelsV1.DigioKycStatusRequest) (modelsV1.DigioKycStatusResponse, error)
	GetMediaForPanAadhaar(ctx context.Context, request modelsV1.DigioKYCMediaRequest) (*modelsV1.DigioKYCMediaResponse, error)
}

type ClcmExternalProvider interface {
	FetchPanDetails(ctx context.Context, clientCode string) (*modelsV1.FetchPanFromCLCMResponse, error)
}

type CommonDigioBusinessLogic interface {
	GetGenderValueForAadhaarFromDigioGenderString(gender string) string
}

type KRABusinessLogic interface {
	CheckAndFetchKRAInfo(ctx context.Context, appNumber, mobile, source string) error
}

type StartRepository interface {
	GetEntityForMobile(ctx context.Context, mobile string) (business.Start, error)
	FetchStartEntryForAppNumberWithContext(ctx context.Context, appNumber string) (*business.Start, error)
}

type POARepository interface {
	FetchPOAForAppNumberWithContext(ctx context.Context, appNumber string) (business.POA, error)
}

type PrismSQSBusinessLogic interface {
	SendMessageToQueue(ctx context.Context, prismEventMessage *business.PrismEventMessage)
}
type CommonPrismBusinessLogic interface {
	GetPrismErrorEventData(appNumber, eventName, subEventName, activityID, remark string) *business.PrismEventMessage
	GetPrismSuccessEventData(appNumber, eventName, subEventName, activityID string) *business.PrismEventMessage
	GetPrismAppAddressMetadata(eventmsg *business.PrismEventMessage) *business.PrismEventMessage
}

type SEBIBannedPANRepository interface {
	IsSEBIBannedPAN(ctx context.Context, pan string) (bool, error)
}

type WorkflowExternalProvider interface {
	GetProcessInstanceByBusinessKeyAndName(ctx context.Context, key, workflow string) ([]modelsV1.WorkflowInstance, error)
	GetProcessInstanceVariables(ctx context.Context, processInstanceID string, _ map[string]string) (map[string]any, error)
}

type DigioBusinessLogic struct {
	panRepository            PanRepository
	panDocRepository         PanDocRepository
	digioRepository          DigioRepository
	selfieRepository         SelfieRepository
	selfieBusinessLogic      SelfieBusinessLogic
	digioExternalProvider    DigioExternalProvider
	commonDigioBusinessLogic CommonDigioBusinessLogic
	nsdlExternalProvider     NsdlExternalProvider
	prismSQSBusinessLogic    PrismSQSBusinessLogic
	commonPrismBusinessLogic CommonPrismBusinessLogic
}

func NewDigioBusinessLogic(panRepository PanRepository, panDocRepository PanDocRepository, digioRepository DigioRepository,
	selfieRepository SelfieRepository, selfieBusinessLogic SelfieBusinessLogic, digioExternalProvider DigioExternalProvider,
	commonDigioBusinessLogic CommonDigioBusinessLogic, nsdlExternalProvider NsdlExternalProvider, prismSQSBusinessLogic PrismSQSBusinessLogic, commonPrismBusinessLogic CommonPrismBusinessLogic) *DigioBusinessLogic {
	return &DigioBusinessLogic{
		panRepository:            panRepository,
		panDocRepository:         panDocRepository,
		digioRepository:          digioRepository,
		selfieRepository:         selfieRepository,
		selfieBusinessLogic:      selfieBusinessLogic,
		digioExternalProvider:    digioExternalProvider,
		commonDigioBusinessLogic: commonDigioBusinessLogic,
		nsdlExternalProvider:     nsdlExternalProvider,
		prismSQSBusinessLogic:    prismSQSBusinessLogic,
		commonPrismBusinessLogic: commonPrismBusinessLogic,
	}
}

type DigioEmodBusinessLogic struct {
	panRepository            PanRepository
	panDocRepository         PanDocRepository
	digioRepository          DigioRepository
	addressRepository        AddressRepository
	digioExternalProvider    DigioExternalProvider
	clcmExternalProvider     ClcmExternalProvider
	commonDigioBusinessLogic CommonDigioBusinessLogic
	commonPrismBusinessLogic CommonPrismBusinessLogic
}

func NewDigioEmodBusinessLogic(panRepository PanRepository, panDocRepository PanDocRepository, digioRepository DigioRepository,
	addressRepository AddressRepository, digioExternalProvider DigioExternalProvider, commonDigioBusinessLogic CommonDigioBusinessLogic,
	clcmExternalProvider ClcmExternalProvider, commonPrismBusinessLogic CommonPrismBusinessLogic) *DigioEmodBusinessLogic {
	return &DigioEmodBusinessLogic{
		panRepository:            panRepository,
		panDocRepository:         panDocRepository,
		digioRepository:          digioRepository,
		addressRepository:        addressRepository,
		digioExternalProvider:    digioExternalProvider,
		commonDigioBusinessLogic: commonDigioBusinessLogic,
		clcmExternalProvider:     clcmExternalProvider,
		commonPrismBusinessLogic: commonPrismBusinessLogic,
	}
}

type OnboardingDigioBusinessLogic struct {
	panRepository            PanRepository
	panDocRepository         PanDocRepository
	panAndPanDocRepository   PanAndPanDocRepository
	digioRepository          DigioRepository
	sebiBannedPANRepository  SEBIBannedPANRepository
	digioExternalProvider    DigioExternalProvider
	nsdlExternalProvider     NsdlExternalProvider
	clcmExternalProvider     ClcmExternalProvider
	commonDigioBusinessLogic CommonDigioBusinessLogic
	panBusinessLogic         PanBusinessLogic
	kraBusinessLogic         KRABusinessLogic
	lsqBusinessLogic         LSQBusinessLogic
	startRepository          StartRepository
	poaRepository            POARepository
	addressRepository        AddressRepository
	personalRepository       PersonalRepository
	prismSQSBusinessLogic    PrismSQSBusinessLogic
	commonPrismBusinessLogic CommonPrismBusinessLogic
	workflowExternalProvider WorkflowExternalProvider
}

type NRIDigioBusinessLogic struct {
	panRepository                PanRepository
	panDocRepository             PanDocRepository
	panAndPanDocRepository       PanAndPanDocRepository
	digioRepository              DigioRepository
	digioExternalProvider        DigioExternalProvider
	nsdlExternalProvider         NsdlExternalProvider
	clcmExternalProvider         ClcmExternalProvider
	commonDigioBusinessLogic     CommonDigioBusinessLogic
	panBusinessLogic             PanBusinessLogic
	kraBusinessLogic             KRABusinessLogic
	lsqBusinessLogic             LSQBusinessLogic
	clientOptOutRepository       ClientOptOutRepository
	poaAndClientOptOutRepository PoaAndClientOptOutRepository
}

func NewOnboardingDigioBusinessLogic(panRepository PanRepository, panDocRepository PanDocRepository, panAndPanDocRepository PanAndPanDocRepository, poaRepository POARepository, addressRepository AddressRepository, digioRepository DigioRepository,
	sebiBannedPANRepository SEBIBannedPANRepository, digioExternalProvider DigioExternalProvider, nsdlExternalProvider NsdlExternalProvider,
	clcmExternalProvider ClcmExternalProvider, commonDigioBusinessLogic CommonDigioBusinessLogic,
	panBusinessLogic PanBusinessLogic, kraBusinessLogic KRABusinessLogic, lsqBusinessLogic LSQBusinessLogic, startRepository StartRepository, personalRepository PersonalRepository,
	prismSQSBusinessLogic PrismSQSBusinessLogic, commonPrismBusinessLogic CommonPrismBusinessLogic, workflowExternalProvider WorkflowExternalProvider,
) *OnboardingDigioBusinessLogic {
	return &OnboardingDigioBusinessLogic{
		panRepository:            panRepository,
		panDocRepository:         panDocRepository,
		panAndPanDocRepository:   panAndPanDocRepository,
		poaRepository:            poaRepository,
		addressRepository:        addressRepository,
		digioRepository:          digioRepository,
		sebiBannedPANRepository:  sebiBannedPANRepository,
		digioExternalProvider:    digioExternalProvider,
		nsdlExternalProvider:     nsdlExternalProvider,
		clcmExternalProvider:     clcmExternalProvider,
		commonDigioBusinessLogic: commonDigioBusinessLogic,
		panBusinessLogic:         panBusinessLogic,
		kraBusinessLogic:         kraBusinessLogic,
		lsqBusinessLogic:         lsqBusinessLogic,
		startRepository:          startRepository,
		personalRepository:       personalRepository,
		prismSQSBusinessLogic:    prismSQSBusinessLogic,
		commonPrismBusinessLogic: commonPrismBusinessLogic,
		workflowExternalProvider: workflowExternalProvider,
	}
}

func NewNRIDigioBusinessLogic(panRepository PanRepository, panDocRepository PanDocRepository, panAndPanDocRepository PanAndPanDocRepository, digioRepository DigioRepository,
	digioExternalProvider DigioExternalProvider, nsdlExternalProvider NsdlExternalProvider,
	clcmExternalProvider ClcmExternalProvider, commonDigioBusinessLogic CommonDigioBusinessLogic,
	panBusinessLogic PanBusinessLogic, kraBusinessLogic KRABusinessLogic, lsqBusinessLogic LSQBusinessLogic,
	clientOptOutRepository ClientOptOutRepository, poaAndClientOptOutRepository PoaAndClientOptOutRepository) *NRIDigioBusinessLogic {
	return &NRIDigioBusinessLogic{
		panRepository:                panRepository,
		panDocRepository:             panDocRepository,
		panAndPanDocRepository:       panAndPanDocRepository,
		digioRepository:              digioRepository,
		digioExternalProvider:        digioExternalProvider,
		nsdlExternalProvider:         nsdlExternalProvider,
		clcmExternalProvider:         clcmExternalProvider,
		commonDigioBusinessLogic:     commonDigioBusinessLogic,
		panBusinessLogic:             panBusinessLogic,
		kraBusinessLogic:             kraBusinessLogic,
		lsqBusinessLogic:             lsqBusinessLogic,
		clientOptOutRepository:       clientOptOutRepository,
		poaAndClientOptOutRepository: poaAndClientOptOutRepository,
	}
}
