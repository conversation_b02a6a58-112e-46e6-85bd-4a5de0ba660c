package cams

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsV1 "github.com/angel-one/kyc-user-journey/models/externals"
)

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type CamsRepository interface {
	FetchRecordByAppNumber(ctx context.Context, appNumber string) (*business.Cams, error)
	FetchRecordByID(ctx context.Context, id string) (*business.Cams, error)
	Save(ctx context.Context, camsDTO *business.Cams) error
	UpdateCamsJourneyStatusWithAppNumber(appNumber, journeyStatus, updateSource string) error
}

type DerivativesCamsAndClientOptOutRepository interface {
	DeleteAllDerivativesData(ctx context.Context, appNumber, source string, shouldClearSegments bool) error
	SaveCamsDerivativesAndDeleteClientOptOut(ctx context.Context, camsDTO *business.Cams, derivativesDTO *business.Derivatives) error
}

type CamsExternalProvider interface {
	Authenticate(ctx context.Context) (*modelsV1.CamsAuthenticateExtResponse, error)
	CreateConsent(ctx context.Context, request *modelsV1.CamsCreateConsentExtRequest) (*modelsV1.CamsCreateConsentExtResponse, error)
}

type CamsAndUserPreferencesRepository interface {
	SaveCamsAndUpdateSegment(ctx context.Context, camsDTO *business.Cams, userPreferencesDTO *business.UserPreferences) error
}

type CommonOnboardingBusinessLogic interface {
	IsOnboardingAppNumber(appNumber string) bool
}

type CamsBusinessLogic struct {
	lsqBusinessLogic                         LSQBusinessLogic
	camsRepository                           CamsRepository
	camsExternalProvider                     CamsExternalProvider
	derivativesCamsAndClientOptOutRepository DerivativesCamsAndClientOptOutRepository
	camsAndUserPreferencesRepository         CamsAndUserPreferencesRepository
	commonOnboardingBusinessLogic            CommonOnboardingBusinessLogic
}

func NewCamsBusinessLogic(lsqBusinessLogic LSQBusinessLogic, camsRepository CamsRepository, derivativesCamsAndClientOptOutRepository DerivativesCamsAndClientOptOutRepository,
	camsExternalProvider CamsExternalProvider, camsAndUserPreferencesRepository CamsAndUserPreferencesRepository, commonOnboardingBusinessLogic CommonOnboardingBusinessLogic) *CamsBusinessLogic {
	return &CamsBusinessLogic{
		lsqBusinessLogic:                         lsqBusinessLogic,
		camsRepository:                           camsRepository,
		derivativesCamsAndClientOptOutRepository: derivativesCamsAndClientOptOutRepository,
		camsExternalProvider:                     camsExternalProvider,
		camsAndUserPreferencesRepository:         camsAndUserPreferencesRepository,
		commonOnboardingBusinessLogic:            commonOnboardingBusinessLogic,
	}
}
