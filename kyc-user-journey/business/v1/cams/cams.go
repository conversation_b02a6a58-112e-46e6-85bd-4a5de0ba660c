package cams

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/angel-one/kyc-user-journey/aws/s3"

	"github.com/angel-one/go-pii-utils/log"
	ifsc "github.com/angel-one/ifsc/v2/src/go"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/angel-one/kyc-user-journey/utils"
)

func (c *CamsBusinessLogic) FetchAvailableBanks(ctx context.Context) (*modelsAPIv1.CamsAvailableBanksResponse, error) {
	banks := config.Cams().SupportedBanks
	var res modelsAPIv1.CamsAvailableBanksResponse

	for bankCode, bankDetails := range banks {
		if bankName, bankErr := ifsc.GetBankName(strings.ToUpper(bankCode)); bankErr == nil {
			res.AvailableBanks = append(res.AvailableBanks, modelsAPIv1.CamsAvailableBankDetails{
				BankName: bankName,
				BankCode: bankCode,
				BankLogo: bankDetails.Logo,
			})
		} else {
			log.Error(ctx).Err(bankErr).Str("BankCode", bankCode).
				Msg("FetchAvailableBanks: Error while getting bank name")
			return &res, constants.ErrGettingCamsBankList.WithDetails(bankErr.Error())
		}
	}
	sort.Slice(res.AvailableBanks, func(i, j int) bool {
		return res.AvailableBanks[i].BankName < res.AvailableBanks[j].BankName
	})
	return &res, nil
}

func (c *CamsBusinessLogic) CreateConsent(ctx context.Context, req *modelsAPIv1.CamsCreateConsentRequest,
	journeyType string) (*modelsAPIv1.CamsCreateConsentResponse, error) {
	var shouldClearSegments bool
	if journeyType == constants.Onboarding {
		shouldClearSegments = true
	}
	if shouldClearSegments {
		err := c.derivativesCamsAndClientOptOutRepository.DeleteAllDerivativesData(ctx, req.AppNumber, req.Source, shouldClearSegments)
		if err != nil {
			log.Error(ctx).Err(err).Msg("CreateConsent: error deleting all derivatives data")
			return nil, err
		}
	}

	callbackURL, err := getCallBackURL(req, journeyType)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CreateConsent: error getting callback URL")
		return nil, err
	}
	camsID := utils.GenerateUUID()
	// get request params for external create-consent api.
	camsCreateConsentReq, err := c.getCamsCreateConsentRequest(ctx, req, callbackURL, camsID)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CreateConsent: error creating consent request")
		return nil, err
	}
	// create consent for cams via external call.
	response, err := c.camsExternalProvider.CreateConsent(ctx, camsCreateConsentReq)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CreateConsent: external camsCreateConsent request failed")
		return nil, err
	}
	log.Info(ctx).Msg("CreateConsent - cams consent is successfully created")
	// store details in db.
	camsDTO := getCamsDTOForCreateConsent(req, camsID)
	err = c.camsRepository.Save(ctx, camsDTO)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CreateConsent: error inserting in db")
		return nil, err
	}
	// success
	return &modelsAPIv1.CamsCreateConsentResponse{RedirectURL: response.RedirectURL}, nil
}

func (c *CamsBusinessLogic) CheckStatus(ctx context.Context, req *modelsAPIv1.CamsStatusRequest) (
	*modelsAPIv1.CamsStatusResponse, error) {
	camsDTO, err := c.camsRepository.FetchRecordByAppNumber(ctx, req.AppNumber)
	if err != nil {
		log.Error(ctx).Err(err).Msg("CheckStatus: error getting cams details from db")
		return nil, err
	}
	camsStatusRes := modelsAPIv1.CamsStatusResponse{
		Status:        constants.PendingStatus,
		Message:       constants.Empty,
		ConsentStatus: camsDTO.ConsentStatus,
		FileStatus:    camsDTO.FileStatus,
	}
	// cams success.
	if camsDTO.Path != constants.Empty && camsDTO.FileStatus == constants.CamsFileStatusReceived {
		camsStatusRes.Status = constants.SuccessStatus
		camsStatusRes.Message = config.Cams().SuccessMessage
	}
	// cams rejected by user.
	if camsDTO.ConsentStatus == constants.CamsConsentStatusRejected {
		camsStatusRes.Status = constants.FailureStatus
		camsStatusRes.Message = config.Cams().RejectionMessage
	}
	// cams failure
	if camsDTO.ErrorMessage != constants.Empty {
		camsStatusRes.Status = constants.FailureStatus
		camsStatusRes.Message = camsDTO.ErrorMessage
	}
	return &camsStatusRes, nil
}

func (c *CamsBusinessLogic) CamsRedirection(ctx context.Context, queryParams *url.Values, journeyType string) *modelsAPIv1.CamsRedirectionResponse {
	baseRedirectionURL := config.Cams().JourneyConfig[journeyType].RedirectionURL
	appNumber := queryParams.Get(constants.CamsAppNumberQueryParamsKey)
	if appNumber != constants.Empty {
		err := c.camsRepository.UpdateCamsJourneyStatusWithAppNumber(appNumber, constants.CamsRedirected, constants.CamsSource)
		if err != nil {
			log.Error(ctx).Err(err).Str(constants.LogAppNumberKey, appNumber).Msg("CamsRedirection: error updating cams journey status")
		}
	}
	redirectionURL := baseRedirectionURL + "?" + queryParams.Encode()
	return &modelsAPIv1.CamsRedirectionResponse{RedirectURL: redirectionURL}
}

func (c *CamsBusinessLogic) ProcessCamsWebhook(ctx context.Context, req *modelsAPIv1.CamsWebhookRequest) error {
	// gets cams record by cams ID.
	camsDTO, err := c.camsRepository.FetchRecordByID(ctx, req.ID)
	if err != nil {
		log.Error(ctx).Err(err).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("ProcessCamsWebhook: error getting cams details from db")
		return err
	}
	if req.Purpose == constants.CamsWebhookPurposeConsentStatus {
		log.Debug(ctx).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("ProcessCamsWebhook: Processing Cams status Webhook")
		err = c.processCamsStatusWebhook(ctx, camsDTO, req)
		return err
	} else if req.Purpose == constants.CamsWebhookPurposeBankStatement {
		log.Debug(ctx).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("ProcessCamsWebhook: Processing Cams data Webhook")
		err = c.processCamsDataWebhook(ctx, camsDTO, req)
		return err
	}
	return constants.ErrCamsUnsupportedPurpose.Value()
}

func (c *CamsBusinessLogic) processCamsStatusWebhook(ctx context.Context, camsDTO *business.Cams,
	req *modelsAPIv1.CamsWebhookRequest) error {
	consentStatus := constants.CamsConsentStatusRequested
	switch strings.ToUpper(req.ConsentStatusNotification.Status) {
	case constants.CamsWebhookConsentStatusApproved:
		consentStatus = constants.CamsConsentStatusApproved
	case constants.CamsWebhookConsentStatusRejected:
		consentStatus = constants.CamsConsentStatusRejected
	}
	camsDTO.ConsentStatus = consentStatus
	var err error

	if c.commonOnboardingBusinessLogic.IsOnboardingAppNumber(camsDTO.AppNumber) && consentStatus == constants.CamsConsentStatusApproved {
		userPreferencesDTO := getUserPreferencesDTOWithSegments(camsDTO.AppNumber, camsDTO.LastUpdateSource)
		err = c.camsAndUserPreferencesRepository.SaveCamsAndUpdateSegment(ctx, camsDTO, userPreferencesDTO)
	} else {
		err = c.camsRepository.Save(ctx, camsDTO)
	}
	if err != nil {
		log.Error(ctx).Err(err).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsStatusWebhook: error updating status in db")
		return err
	}
	log.Info(ctx).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsStatusWebhook - cams status web hook processed successfully")
	return nil
}

func (c *CamsBusinessLogic) processCamsDataWebhook(ctx context.Context, camsDTO *business.Cams,
	req *modelsAPIv1.CamsWebhookRequest) error {
	if req.BankStatement.PDFBase64 == constants.Empty {
		log.Info(ctx).Msg("processCamsStatusWebhook: empty pdf data")
		// update error message in db.
		camsDTO.ErrorMessage = req.ErrorMessage
		err := c.camsRepository.Save(ctx, camsDTO)
		if err != nil {
			log.Error(ctx).Err(err).Msg("processCamsStatusWebhook: error updating error msg in db")
			return err
		}
		return nil
	}

	var pdfFilePath, jsonFilePath string
	pdfFilePath = c.GetPdfFilePath(camsDTO.AppNumber)
	pdfData := regexp.MustCompile(config.Application().Validations.AllowedCharacters.Base64Regex).
		ReplaceAllString(req.BankStatement.PDFBase64, constants.Empty)
	if pdfData == constants.Empty {
		log.Info(ctx).Stack().Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsDataWebhook: received empty pdf data")
		return constants.ErrEmptyPDFFile.Value()
	}
	err := s3.GetS3Client().UploadBase64File(ctx, pdfFilePath, []byte(pdfData))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsDataWebhook: error uploading bank-statement pdf to s3")
		return constants.ErrUploadingFile.WithDetails(err.Error())
	}

	jsonFilePath = c.GetJSONFilePath(camsDTO.AppNumber)
	bankStatementJSON, err := json.Marshal(req.BankStatement.JSONData)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsDataWebhook: error marshalling json")
		return constants.ErrUploadingFile.WithDetails(err.Error())
	}
	err = s3.GetS3Client().Upload(ctx, jsonFilePath, bytes.NewReader(bankStatementJSON))
	if err != nil {
		log.Error(ctx).Stack().Err(err).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsDataWebhook: error uploading bank-statement json to s3")
		return constants.ErrUploadingFile.WithDetails(err.Error())
	}

	// update db.
	camsDTO.FileStatus = constants.CamsFileStatusReceived
	camsDTO.Path = pdfFilePath
	camsDTO.JSONContent = getJSONContentFromCamsAccountProfile(req)
	camsDTO.LastUpdateSource = constants.CamsSource
	camsDTO.ErrorMessage = constants.Empty

	derivativesDTO := getDerivativesDTO(camsDTO, pdfFilePath)
	err = c.derivativesCamsAndClientOptOutRepository.SaveCamsDerivativesAndDeleteClientOptOut(ctx, camsDTO, derivativesDTO)
	if err != nil {
		log.Error(ctx).Stack().Err(err).Msg("processCamsDataWebhook: error updating cams details in db")
		return err
	}
	if strings.HasPrefix(camsDTO.AppNumber, "EQ") {
		c.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, camsDTO.AppNumber, constants.CamsDerivativeSuccessLSQEvent)
	}
	log.Info(ctx).Str(constants.LogAppNumberKey, camsDTO.AppNumber).Msg("processCamsDataWebhook - cams data web hook processed successfully")
	return nil
}

func (c *CamsBusinessLogic) GetJSONFilePath(appNumber string) string {
	if c.commonOnboardingBusinessLogic.IsOnboardingAppNumber(appNumber) {
		return fmt.Sprintf("%s%s%s%d.json", appNumber, constants.CamsBankStatementPath, "-", time.Now().Unix())
	}
	return fmt.Sprintf("%s%s", appNumber, config.Cams().JSONFilePath)
}

func (c *CamsBusinessLogic) GetPdfFilePath(appNumber string) string {
	if c.commonOnboardingBusinessLogic.IsOnboardingAppNumber(appNumber) {
		return fmt.Sprintf("%s%s%s%d.pdf", appNumber, constants.CamsBankStatementPath, "-", time.Now().Unix())
	}
	return fmt.Sprintf("%s%s", appNumber, config.Cams().PDFFilePath)
}
