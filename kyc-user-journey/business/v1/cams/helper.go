package cams

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"

	modelsAPIv1 "github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/models/business"

	"github.com/angel-one/go-pii-utils/log"
	ifsc "github.com/angel-one/ifsc/v2/src/go"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	modelsExtv1 "github.com/angel-one/kyc-user-journey/models/externals"
)

func getCallBackURL(req *modelsAPIv1.CamsCreateConsentRequest, journeyType string) (string, error) {
	callbackURL := config.Cams().JourneyConfig[journeyType].CallbackURL
	parsedURL, err := url.Parse(callbackURL)
	if err != nil {
		return constants.Empty, err
	}
	// add additional query params.
	params := url.Values{}
	if journeyType == constants.Onboarding {
		params.Add(constants.CamsAppNumberQueryParamsKey, req.AppNumber)
	} else {
		params.Add(constants.CamsIntentQueryParamsKey, req.Intent)
	}
	parsedURL.RawQuery = params.Encode()
	callbackURL = parsedURL.String()
	return callbackURL, nil
}

func (c *CamsBusinessLogic) getCamsCreateConsentRequest(ctx context.Context, req *modelsAPIv1.CamsCreateConsentRequest, callbackURL,
	camsID string) (*modelsExtv1.CamsCreateConsentExtRequest, error) {
	fipID, err := getFipID(req.Data.BankCode)
	if err != nil {
		log.Error(ctx).Err(err).Msg("getCamsCreateConsentRequest: Error getting FipID")
		return nil, err
	}
	// get auth token from cams external call.
	authRes, err := c.camsExternalProvider.Authenticate(ctx)
	if err != nil {
		log.Error(ctx).Err(err).Msg("getCamsCreateConsentRequest: Error in cams authentication")
		return nil, err
	}
	return &modelsExtv1.CamsCreateConsentExtRequest{
		Token:              authRes.Token,
		ID:                 camsID,
		FiuID:              config.Cams().FiuID,
		UserID:             config.Cams().UserID,
		SessionID:          authRes.SessionID,
		UseCaseID:          config.Cams().UseCaseID,
		CustomerHandleID:   req.Data.Mobile + config.Cams().AAHandleSuffix,
		Mobile:             req.Data.Mobile,
		TriggerSMSAndEmail: "N",
		AllowOtherPhones:   false,
		RedirectURL:        callbackURL,
		FipID:              fipID,
	}, nil
}

func getFipID(bankCode string) (string, error) {
	if bankCode == constants.Empty {
		return constants.Empty, nil
	}
	bankDetails, _ := ifsc.GetBankDetailsFromIfscCode(bankCode)
	if len(bankCode) >= 4 && bankDetails == nil {
		if bankName, bankErr := ifsc.GetBankName(strings.ToUpper(bankCode[:4])); bankErr == nil {
			bankDetails = &ifsc.BankDetails{Name: bankName, Code: bankCode}
		}
	}
	if bankDetails == nil {
		return constants.Empty, constants.ErrGettingBankFromIFSC.WithDetails("Unable to fetch bank details")
	}
	fipID := config.Cams().SupportedBanks[strings.ToLower(bankDetails.Code)].FipID
	if fipID == constants.Empty {
		return constants.Empty, constants.ErrGettingBankFromIFSC.WithDetails("bank not supported by cams")
	}
	return fipID, nil
}

func getCamsDTOForCreateConsent(req *modelsAPIv1.CamsCreateConsentRequest, camsID string) *business.Cams {
	return &business.Cams{
		AppNumber:        req.AppNumber,
		ID:               camsID,
		Mobile:           req.Data.Mobile,
		ConsentStatus:    constants.CamsConsentStatusRequested,
		FileStatus:       constants.CamsFileStatusPending,
		CreateSource:     req.Source,
		LastUpdateSource: req.Source,
		JSONContent:      constants.EmptyJSON,
		JourneyStatus:    constants.CamsInitiated,
	}
}

func getJSONContentFromCamsAccountProfile(req *modelsAPIv1.CamsWebhookRequest) string {
	profile, ok := req.BankStatement.JSONData["Account"].(map[string]any)["Profile"].(map[string]any)
	if !ok {
		return constants.Empty
	}
	// Only profile key of account map is required rest all can be omitted.
	jsonBytes, err := json.Marshal(map[string]any{
		"Account": map[string]any{
			"Profile": profile,
		},
	})
	if err != nil {
		return constants.Empty
	}

	return string(jsonBytes)
}

func getDerivativesDTO(camsDTO *business.Cams, pdfFilePath string) *business.Derivatives {
	return &business.Derivatives{
		AppNumber:           camsDTO.AppNumber,
		Type:                constants.DerivativesTypeBankStatement,
		Path:                pdfFilePath,
		ActivationStatus:    constants.DerivativesStatusPending,
		CreateSource:        constants.CamsSource,
		LastUpdateSource:    constants.CamsSource,
		IsPasswordProtected: false,
	}
}

func getSegmentObjectWithAllSegmentsSet() business.UserOptedSegments {
	return business.UserOptedSegments{
		IsFnO:       true,
		IsCurrency:  true,
		IsCommodity: true,
	}
}

func getUserPreferencesDTOWithSegments(appNumber, source string) *business.UserPreferences {
	return &business.UserPreferences{
		AppNumber:              appNumber,
		CreateSource:           source,
		LastUpdateSource:       source,
		Segments:               getSegmentObjectWithAllSegmentsSet(),
		IsSegmentsAcknowledged: false,
	}
}
