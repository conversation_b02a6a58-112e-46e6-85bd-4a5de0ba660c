package prism

import (
	"context"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/kyc-user-journey/aws/kinesis"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
)

func (p *PrismBusinessLogic) TriggerKinesisMessageForHistoryEvent(ctx context.Context, appNumber string, prismMessageType int, eventData any) {
	payload := p.createKinesisPayloadForHistoryEvent(ctx, appNumber, prismMessageType, eventData)
	err := p.kinesisClient.PublishMessage(ctx, *payload)
	if err != nil {
		log.Error(ctx).Err(err).Msgf("TriggerKinesisMessageForHistoryEvent: Received non success, payload: %v", payload)
	}
}

func (p *PrismBusinessLogic) createKinesisPayloadForHistoryEvent(_ context.Context, appNumber string, prismMessageType int, eventData any) *kinesis.Message {
	var streamName string
	switch prismMessageType {
	case constants.PrismMetadataEvent:
		streamName = config.Kinesis().PrismMetadataEvent.StreamName
	default:
		streamName = config.Kinesis().PrismHistoryEvent.StreamName
	}
	return &kinesis.Message{
		StreamName:   streamName,
		PartitionKey: appNumber,
		EventData:    eventData,
	}
}
