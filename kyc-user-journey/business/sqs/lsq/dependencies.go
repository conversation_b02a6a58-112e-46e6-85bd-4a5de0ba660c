package lsq

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	modelsExternals "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

type SqsClient interface {
	SendMessage(ctx context.Context, queueURL, messageBody, groupID, dedupID string, messageAttributes map[string]types.MessageAttributeValue) error
}

type LsqRepository interface {
	GetLSQDataByAppNumber(ctx context.Context, appNumber string) (business.LSQData, error)
	GetLSQEmodDataByAppNumber(ctx context.Context, appNumber string) (business.LSQData, error)
}

type BankSearchRepository interface {
	GetBankDataByIfsc(ctx context.Context, ifsc string, from, size int) ([]business.BankBranchMasterSearchData, error)
}

type SubBrokerRepository interface {
	GetSubBrokerCommunicationDetails(ctx context.Context, subBrokerTag string, subBrokerBranch string) (*business.SubBroker, error)
}

type KYCRepository interface {
	GetAppNumberWithUserID(userID string) (string, error)
}

type LsqExternalProvider interface {
	CaptureLead(ctx context.Context, request *modelsExternals.LSQLeadCaptureAPIRequest) error
}

type GeolocationExternalProvider interface {
	GetGeolocation(ctx context.Context, ipAddress string) (*modelsExternals.GeoLocationExternalResponse, error)
}

type GeoLocationRepository interface {
	FetchGeolocationForAppNumberAndFlow(ctx context.Context, appNumber, flow string) (*business.GeoLocation, error)
}

type SPSeriesArchiveRepository interface {
	GetSPSeriesArchiveApplication(ctx context.Context, mobile string) (*business.SPSeriesArchive, error)
}

type ClcmExternalProvider interface {
	GetProfileDetailsFromCLCM(ctx context.Context, request *modelsExternals.ProfileDetailsFromCLCMRequest) (*modelsExternals.ProfileDetailsFromCLCM, error)
}

type LsqBusinessLogic struct {
	sqsClient                   SqsClient
	lsqRepository               LsqRepository
	bankSearchRepository        BankSearchRepository
	subBrokerRepository         SubBrokerRepository
	geolocationRepository       GeoLocationRepository
	spSeriesArchiveRepository   SPSeriesArchiveRepository
	lsqExternalProvider         LsqExternalProvider
	geolocationExternalProvider GeolocationExternalProvider
	clcmExternalProvider        ClcmExternalProvider
	kycRepository               KYCRepository
}

func NewLsqBusinessLogic(sqsClient SqsClient, lsqRepository LsqRepository, bankSearchRepository BankSearchRepository, subBrokerRepository SubBrokerRepository, geolocationRepository GeoLocationRepository, geolocationExternalProvider GeolocationExternalProvider,
	clcmExternalProvider ClcmExternalProvider, lsqExternalProvider LsqExternalProvider, kycRepository KYCRepository, spSeriesArchiveRepository SPSeriesArchiveRepository) *LsqBusinessLogic {
	return &LsqBusinessLogic{
		sqsClient:                   sqsClient,
		lsqRepository:               lsqRepository,
		bankSearchRepository:        bankSearchRepository,
		subBrokerRepository:         subBrokerRepository,
		geolocationRepository:       geolocationRepository,
		geolocationExternalProvider: geolocationExternalProvider,
		clcmExternalProvider:        clcmExternalProvider,
		lsqExternalProvider:         lsqExternalProvider,
		kycRepository:               kycRepository,
		spSeriesArchiveRepository:   spSeriesArchiveRepository,
	}
}
