package lsq

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils"
	"github.com/angel-one/go-utils/errors"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelsv1 "github.com/angel-one/kyc-user-journey/models/externals"
	kycUtils "github.com/angel-one/kyc-user-journey/utils"
	"github.com/angel-one/kyc-user-journey/utils/flags"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

func (l *LsqBusinessLogic) TriggerLSQ(ctx context.Context, lsqMessage *business.LSQMessage) error {
	var isNRI bool
	lsqData, err := l.getLSQData(ctx, lsqMessage.AppNumber)
	if err != nil {
		return err
	}

	lsqData.AppNumber = lsqMessage.AppNumber
	if strings.HasPrefix(lsqData.AppNumber, constants.NR) {
		isNRI = true
	}
	lsqData.Status = config.LSQ().StepsToEventMapping[lsqMessage.StepID]
	lsqData.ObjectionRemark = config.LSQ().StepsToObjectionRemarkMapping[lsqMessage.StepID]
	clientType := l.getLSQClientType(lsqData.ClientType)
	if l.shouldExcludePlanName(lsqData) {
		log.Debug(ctx).Interface(constants.AppNumberKey, lsqMessage.AppNumber).Msg("Custom Plan lead")
		return nil
	}

	l.handleClientTypeSpecificData(lsqData, clientType)
	lsqData.Journey = l.getLSQJourney(lsqData)
	lsqData.BankName = l.getLSQBankName(ctx, lsqData.IFSC)
	lsqData.SPDeprecatedFlag = l.getSPDeprecatedFlag(ctx, lsqData.Mobile)
	lsqData.City, lsqData.State, _ = l.getLSQLocationDetails(ctx, lsqData.AppNumber, lsqData.IPAddress)
	if lsqData.City == constants.Empty {
		lsqData.City = lsqData.DBCity
		lsqData.State = lsqData.DBState
	}
	lsqData.AppointmentDate = l.getLSQAppointmentTimeShot(lsqData.AppointmentDate, lsqData.AppointmentTime)
	lsqData.Environment = l.getLSQEnv()
	lsqData.IsAadhaarMobileLinked = l.computeIsAadhaarMobileLinked(lsqData.KRAStatus, lsqData.POAType)
	lsqData.DOB = l.getLSQDob(lsqData.DOB)
	lsqData.IsAadhaarPanSeeded = l.getPanAadhaarSeedingStatus(lsqData.DBIsAadhaarPanSeeded)

	lsqData.CodingStatus = constants.LSQCodingStatusInProcess
	if lsqData.IPVPath != constants.Empty {
		lsqData.IPV = constants.IPVTypeUpload
	}

	lsqData.LeadSource, lsqData.LeadMedium, lsqData.LeadCampaign, lsqData.ReferreringClientID = l.getData(lsqData)

	if strings.TrimSpace(lsqData.ReferreringClientID) == constants.Empty && lsqData.ReferralCode != constants.Empty {
		lsqData.ReferreringClientID = lsqData.ReferralCode
	}

	if lsqData.ClientType == constants.Empty || lsqData.ClientType == constants.B2CReferralType {
		lsqData.ReferringClientName = l.getReferrerDetails(ctx, lsqData.ReferreringClientID, lsqData.ReferralType)
		lsqData.LeadAdGroup = constants.Empty
		lsqData.LeadSource = constants.Empty
		lsqData.LeadMedium = constants.Empty
		lsqData.LeadCampaign = constants.Empty
		lsqData.LeadType = constants.Empty
	}

	if lsqData.ClientType == constants.B2BReferralType {
		lsqData.DRARegistrationCode, lsqData.ReferringClientName = l.getDRARegistrationCode(lsqData)
	}

	lsqData.DRAName, lsqData.DRAMobile, lsqData.DRACode = l.getDRADetails(ctx, lsqData)
	if strings.EqualFold(lsqData.Status, constants.SubmittedByDAE) {
		lsqData.ApplicationFirstSubmittedSource = lsqData.EsignCreateSource
		lsqData.ApplicationLastSubmittedSource = lsqData.EsignLastUpdateSource
	}

	lsqData.FnOProofLastUpdatedAtIST = l.getTSInIST(lsqData.FnOProofLastUpdatedAt)
	lsqData.LastEsignDateIST = l.getTSInIST(lsqData.LastEsignDate)
	lsqData.LastSubmissionDateIST = l.getTSInIST(lsqData.LastEsignDate)
	lsqData.PanAadhaarSeedingCheckedTSIST = l.getTSInIST(lsqData.PanAadhaarSeedingCheckedTS)
	lsqData.SelfieLastUpdateTSIST = l.getTSInIST(lsqData.SelfieLastUpdateTS)
	lsqData.AssistanceRequestedTimestampIST = l.getTSInIST(lsqData.AssistanceRequestedTimestamp)

	lsqData.KRAStatusCodeWithReason = fmt.Sprintf("%s %s", lsqData.KRAStatus, config.KRA().KRAStatusWithReasons[lsqData.KRAStatus])
	lsqData.KRAUpdateStatusCodeWithReason = fmt.Sprintf("%s %s", lsqData.KRAUpdateStatus, config.KRA().KRAStatusWithReasons[lsqData.KRAUpdateStatus])
	l.setFnOData(lsqData)
	l.setTradingExperience(lsqData)

	lsqData.NomineeStatus = l.getNomineeStatus(lsqData.EsignAppNumber, lsqData.KYCNomineeIsValid)

	if clientType == constants.B2CReferralType {
		lsqData.AssignmentStatus = l.getAssignmentStatus(ctx, lsqData.ApplicationCreateTS, lsqMessage.AppNumber)
	}

	lsqData.HelpRequestedUserStatus = l.getHelpRequestedUserStatus(lsqMessage.AppNumber)

	if lsqData.ClientType == constants.B2B {
		lsqData.SubmitterCode = lsqData.EsignSubmitterCode
		lsqData.UTMSource, lsqData.SourceMedium, lsqData.UTMCampaign = l.getUTMParameters(lsqData)
	}

	// call lsq lead capture api
	if err := l.captureLead(ctx, lsqData, clientType, isNRI); err != nil {
		log.Error(ctx).Err(err).Interface(constants.AppNumberKey, lsqMessage.AppNumber).Msg("error capturing lead")
		return err
	}
	log.Debug(ctx).Interface(constants.AppNumberKey, lsqMessage.AppNumber).Str("Status", constants.SuccessStatus).Interface("LsqData", lsqData).Msg("successfully captured lead")
	return nil
}

func (l *LsqBusinessLogic) TriggerLSQForEMOD(ctx context.Context, lsqMessage *business.LSQMessage, clientCode string) error {
	lsqData, err := l.getLSQEmodData(ctx, lsqMessage.AppNumber)
	if err != nil {
		return err
	}

	lsqData.EmodAppNumber = lsqMessage.AppNumber
	lsqData.Status = config.LSQ().StepsToEventMapping[lsqMessage.StepID]
	lsqData.FnOProofLastUpdatedAtIST = formatTime(l.getTSInIST(lsqData.FnOProofLastUpdatedAt))
	lsqData.ClientCode = clientCode
	lsqData.FullName = lsqMessage.Name
	t, _ := time.Parse(time.RFC3339, lsqMessage.ClientActivationDate)
	lsqData.ClientActivationDate = formatTime(l.getTSInIST(t))
	lsqData.FnOLeadEligibility = lsqMessage.FnOLeadEligibility
	clientType := constants.LSQClientTypeB2C

	// call lsq lead capture api
	if err := l.captureLead(ctx, lsqData, clientType, false); err != nil {
		log.Error(ctx).Err(err).Interface(constants.AppNumberKey, lsqMessage.AppNumber).Msg("error capturing lead")
		return err
	}
	log.Debug(ctx).Interface(constants.AppNumberKey, lsqMessage.AppNumber).Str("Status", constants.SuccessStatus).Interface("LsqData", lsqData).Msg("successfully captured lead")
	return nil
}

func formatTime(timeStamp string) string {
	return strings.ReplaceAll(strings.ReplaceAll(timeStamp, "T", " "), "Z", "")
}

func (l *LsqBusinessLogic) TriggerSQSMessagePublish(ctx context.Context, appNumber string, stepIDList []string) error {
	queueURL := config.SQS().KycUserJourney.QueueURL
	for _, stepID := range stepIDList {
		if _, ok := config.LSQ().StepsToEventMapping[stepID]; !ok {
			continue
		}
		sqsPayload := l.createSqsPayloadForAppnumber(ctx, appNumber, stepID)
		jsonBytes, err := utils.MarshalJSON(sqsPayload)
		if err != nil {
			return err
		}
		err = l.sqsClient.SendMessage(ctx, queueURL, string(jsonBytes), appNumber, fmt.Sprintf("%s-%s", appNumber, stepID), map[string]types.MessageAttributeValue{
			"MessageType": {
				DataType:    aws.String("String"),
				StringValue: aws.String("LSQ"),
			},
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (l *LsqBusinessLogic) TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string) {
	if stepIDList, ok := config.LSQ().UsecaseToStepTriggerMap[usecase]; ok {
		go func() {
			err := l.TriggerSQSMessagePublish(ctx, appNumber, stepIDList)
			if err != nil {
				log.Error(ctx).Err(err).Msg("TriggerSQSMessagePublishForUsecase: Received non success")
			}
		}()
	}
}

func (l *LsqBusinessLogic) createSqsPayloadForAppnumber(_ context.Context, appNumber, stepID string) *business.LSQMessage {
	// use message attributes and concrete struct instead of map.
	return &business.LSQMessage{
		AppNumber: appNumber,
		StepID:    stepID,
		Data:      map[string]any{},
	}
}

func (l *LsqBusinessLogic) getLSQClientType(referralType string) string {
	if strings.ToUpper(strings.TrimSpace(referralType)) == "B2B" || strings.ToUpper(strings.TrimSpace(referralType)) == "SB" {
		return constants.LSQClientTypeB2B
	}
	return constants.LSQClientTypeB2C
}

func (l *LsqBusinessLogic) getLSQJourney(lsqData *business.LSQData) string {
	var journeyType string

	if slices.Contains(config.KRA().KRACompliantStatuses, lsqData.KRAStatus) {
		journeyType = constants.LSQJourneyKRACompliant
	}
	if lsqData.POAType == constants.DigilockerPOA {
		journeyType = constants.LSQJourneyDigilocker
	}
	if lsqData.POAType == constants.AadhaarPOA {
		if lsqData.POAFirstPath != constants.Empty && lsqData.POASecondPath != constants.Empty {
			journeyType = constants.LSQJourneyOnlineAadhaar
		} else {
			journeyType = constants.LSQJourneyManual
		}
	}

	return journeyType
}

func (l *LsqBusinessLogic) getLSQBankName(ctx context.Context, ifsc string) string {
	if ifsc == constants.Empty {
		return constants.Empty
	}

	bankIfscData, err := l.bankSearchRepository.GetBankDataByIfsc(ctx, ifsc, 0, 1)
	if err != nil {
		log.Error(ctx).Err(err).Msgf("error fetching bank for ifsc %s", ifsc)
		return constants.Empty
	}

	if len(bankIfscData) == 0 {
		return constants.Empty
	}
	return bankIfscData[0].BankName
}

func (l *LsqBusinessLogic) getSPDeprecatedFlag(ctx context.Context, mobile string) string {
	spSeriesArchive, err := l.spSeriesArchiveRepository.GetSPSeriesArchiveApplication(ctx, mobile)
	if err != nil {
		log.Error(ctx).Err(err).Msg("error fetching data from db")
		return constants.Empty
	}

	if spSeriesArchive.AppNumber != constants.Empty {
		return constants.YesString
	} else {
		return constants.NoString
	}
}

func (l *LsqBusinessLogic) getLSQLocationDetails(ctx context.Context, appNumber, ipAddress string) (string, string, error) {
	geolocation, err := l.geolocationRepository.FetchGeolocationForAppNumberAndFlow(ctx, appNumber, constants.SelfieFlowName)
	if err == nil && kycUtils.IsAllGeolocationDataPresent(geolocation.GeoCity, geolocation.GeoState, geolocation.Latitude, geolocation.Longitude) {
		return geolocation.GeoCity, geolocation.GeoState, nil
	} else {
		if ipAddress == constants.Empty {
			return constants.Empty, constants.Empty, nil
		}

		geolocationResp, err := l.geolocationExternalProvider.GetGeolocation(ctx, ipAddress)
		if err != nil {
			log.Error(ctx).Err(err).Msgf("error fetching location details for %s", ipAddress)
			return constants.Empty, constants.Empty, err
		}

		if geolocationResp == nil {
			log.Error(ctx).Err(errors.New("nil response")).Msgf("getGeolocation nil response for %s", ipAddress)
			return constants.Empty, constants.Empty, nil
		}

		return geolocationResp.City, geolocationResp.State, nil
	}
}

func (l *LsqBusinessLogic) getLSQAppointmentTimeShot(appointmentDate, appointmentTime string) string {
	if appointmentDate == constants.Empty {
		return constants.Empty
	}
	result := strings.ToUpper(appointmentDate)
	if appointmentTime != constants.Empty {
		ts := strings.Split(appointmentTime, "_")[1:]
		result = fmt.Sprintf("%s : %s", result, strings.Join(ts, " "))
	}
	return result
}

func (l *LsqBusinessLogic) getLSQEnv() string {
	env := flags.Env()
	if env == constants.EnvProd {
		return config.LSQ().Environments[env]
	}
	return config.LSQ().Environments[env]
}

func (l *LsqBusinessLogic) computeIsAadhaarMobileLinked(cvlKRAStatus, poaType string) string {
	if cvlKRAStatus == constants.Empty && poaType == constants.Empty {
		return constants.Empty
	}
	if slices.Contains(config.KRA().KRACompliantStatuses, cvlKRAStatus) || poaType == constants.DigilockerPOA {
		return constants.Yes
	} else {
		return constants.No
	}
}

func (l *LsqBusinessLogic) getDRADetails(ctx context.Context, lsqData *business.LSQData) (string, string, string) {
	if lsqData.SBTag == constants.Empty || lsqData.Branch == constants.Empty || strings.TrimSpace(strings.ToUpper(lsqData.Branch)) != constants.LSQDRABranch ||
		lsqData.ReferralCode == constants.Empty {
		return constants.Empty, constants.Empty, constants.Empty
	}

	if strings.TrimSpace(strings.ToUpper(lsqData.ReferralCode)) != strings.TrimSpace(strings.ToUpper(lsqData.SBTag)) {
		log.Info(ctx).Msg(fmt.Sprintf("non-level1 DRA case, referralCode: %s, SBTag: %s", lsqData.ReferralCode, lsqData.SBTag))
		return constants.Empty, constants.Empty, constants.Empty
	}

	sbDetails, err := l.subBrokerRepository.GetSubBrokerCommunicationDetails(ctx, lsqData.SBTag, lsqData.Branch)
	if err != nil {
		log.Warn(ctx).Stack().Err(err).Msg("error fetching sb dra details for " + lsqData.SBTag)
		return constants.Empty, constants.Empty, constants.Empty
	}

	return sbDetails.Name, sbDetails.Mobile, lsqData.SBTag
}

func (l *LsqBusinessLogic) setFnOData(lsqData *business.LSQData) {
	if lsqData.DerivativesAppNumber != constants.Empty { // entry present in derivatives table
		lsqData.FnOIntent = constants.YesString
		if lsqData.FnoProofPath == constants.Empty {
			lsqData.FnOProofUploadStatus = constants.DeletedStatus
			lsqData.FnOProofLastUpdatedFrom = constants.Empty
			lsqData.FnOProofLastUpdatedAtIST = constants.Empty
		} else {
			lsqData.FnOProofUploadStatus = constants.UploadedStatus
		}
		return
	}

	lsqData.FnOIntent = constants.NoString
	lsqData.FnOProofUploadStatus = constants.NotUploadedStatus
}

func (l *LsqBusinessLogic) setTradingExperience(lsqData *business.LSQData) {
	if slices.Contains(config.KRA().NoTradingExperienceStatuses, lsqData.KRAStatus) || lsqData.KRAStatus == constants.Empty {
		lsqData.TradingExperience = constants.NoString
		return
	}
	lsqData.TradingExperience = constants.YesString
}

func (l *LsqBusinessLogic) getNomineeStatus(esignAppNumber string, kycNomineeIsValid bool) string {
	if esignAppNumber != constants.Empty {
		if kycNomineeIsValid {
			return constants.OptedIn
		} else {
			return constants.OptedOut
		}
	}
	return constants.Empty
}

func (l *LsqBusinessLogic) getReferrerDetails(ctx context.Context, referringClientID, referralType string) string {
	if referringClientID == constants.Empty || referralType == constants.Empty ||
		strings.TrimSpace(strings.ToUpper(referralType)) != constants.B2CReferralType {
		return constants.Empty
	}

	clientDetails, err := l.clcmExternalProvider.GetProfileDetailsFromCLCM(ctx, &modelsv1.ProfileDetailsFromCLCMRequest{ClientID: referringClientID, Fields: []string{"clientDetails.LongName"}})

	// If profile not found
	if len(clientDetails.Data) == 0 {
		log.Error(ctx).Msg("No profile found")
		return constants.Empty
	}

	if clientDetails.Data[0].ClientDetails.LongName == constants.Empty {
		log.Error(ctx).Msg("Empty full name")
	}
	if err != nil {
		log.Error(ctx).Err(err).Msg("error getting referrer details for " + referringClientID)
	}

	return clientDetails.Data[0].ClientDetails.LongName
}

func (l *LsqBusinessLogic) getDRARegistrationCode(lsqData *business.LSQData) (string, string) {
	branch := strings.ToLower(lsqData.Branch)
	referralCode := strings.ToLower(lsqData.ReferralCode)
	sbTag := strings.ToLower(lsqData.SBTag)
	referralType := strings.ToUpper(lsqData.ReferralType)
	var draRegistrationCode, referringClientName string
	if branch == constants.Itrade {
		if referralType == constants.SBReferralType || referralCode == sbTag {
			draRegistrationCode = constants.Itrade
		} else if referralType == constants.B2BReferralType || referralCode != sbTag {
			draRegistrationCode = constants.ItradeRNE
			referringClientName = lsqData.ReferrerName
		}
	} else {
		if referralType == constants.B2BReferralType || referralCode != sbTag {
			draRegistrationCode = constants.ParentRNE
			referringClientName = lsqData.ReferrerName
		} else if referralType == constants.SBReferralType || referralCode != sbTag {
			draRegistrationCode = constants.ParentTagNC
		}
	}
	return draRegistrationCode, referringClientName
}

func (l *LsqBusinessLogic) getAssignmentStatus(ctx context.Context, applicationCreateTS time.Time, appNumber string) string {
	percentages := config.LSQ().LeadAssignmentPercentages
	applicationCreateTimeStamps := config.LSQ().ApplicationCreateTimeStamps
	var percentage int64
	for i, timeStampStr := range applicationCreateTimeStamps {
		timeStamp, _ := time.Parse(config.Application().Validations.ISTFormat, timeStampStr)
		if !applicationCreateTS.IsZero() && (applicationCreateTS.Unix() > timeStamp.Unix()) {
			percentage = int64(percentages[i])
			break
		}
	}
	if kycUtils.PercentageBasedFeatureEnabling(ctx, appNumber, percentage, false) {
		return constants.No
	} else {
		return constants.YesStr
	}
}

func (l *LsqBusinessLogic) getHelpRequestedUserStatus(appNumber string) string {
	percentage := config.LSQ().DaeAssistanceFalsePercentage
	if kycUtils.PercentageBasedFeatureEnablingReversed(appNumber, int64(percentage)) {
		return constants.YesStr
	} else {
		return constants.No
	}
}

func (l *LsqBusinessLogic) getTSInIST(timeStamp time.Time) string {
	if timeStamp.IsZero() {
		return constants.Empty
	} else {
		timeStampInIST := kycUtils.ConvertTimeToIndianTimezone(&timeStamp)
		return timeStampInIST.Format(config.Application().Validations.ISTFormat)
	}
}

func (l *LsqBusinessLogic) getLSQType(referralType string) string {
	if referralType != constants.Empty && utils.DoesStringOptionsContain([]string{}, referralType) {
		return constants.Empty
	}
	return constants.LSQDiy
}

func (l *LsqBusinessLogic) getLSQDob(dob string) string {
	if dob == constants.Empty {
		return constants.Empty
	}
	var result = dob
	t, err := time.Parse("02/01/2006", result)
	if err == nil {
		result = t.Format("2006-01-02")
	}
	return result
}

// GetDataFromReferralString is used to split referral string and get data from it.
func (l *LsqBusinessLogic) GetDataFromReferralString(r, platform string) map[string]string {
	var (
		refData          map[string]string
		significantSlice []string
	)
	paramSeparator := config.Referral().Brokerage.ReferralString.ParamSeperator[platform]
	keyValueSeparator := config.Referral().Brokerage.ReferralString.KeyValueSeperator[platform]

	s := strings.Split(r, paramSeparator)

	significantValueSeparator := config.Referral().Brokerage.ReferralString.SignificantValueSeperator[platform]
	if significantValueSeparator != constants.Empty {
		for _, v := range s {
			significantSlice = append(significantSlice, strings.Split(v, significantValueSeparator)...)
		}
	} else {
		significantSlice = s
	}

	for _, v := range significantSlice {
		dataSplitIndex := strings.Index(v, keyValueSeparator)
		if len(v) > dataSplitIndex+1 && dataSplitIndex != -1 {
			if refData == nil {
				refData = make(map[string]string)
			}
			refData[v[:dataSplitIndex]] = v[dataSplitIndex+1:]
		}
	}

	return refData
}

func (l *LsqBusinessLogic) getLSQData(ctx context.Context, appNumber string) (*business.LSQData, error) {
	lsqData, err := l.lsqRepository.GetLSQDataByAppNumber(ctx, appNumber)
	if err != nil {
		return nil, err
	}
	return &lsqData, nil
}

func (l *LsqBusinessLogic) getLSQEmodData(ctx context.Context, appNumber string) (*business.LSQData, error) {
	lsqData, err := l.lsqRepository.GetLSQEmodDataByAppNumber(ctx, appNumber)
	if err != nil {
		return nil, err
	}
	return &lsqData, nil
}

func (l *LsqBusinessLogic) getPanAadhaarSeedingStatus(isPanAadhaarSeeded bool) string {
	if isPanAadhaarSeeded {
		return constants.Y
	}
	return constants.No
}
func (l *LsqBusinessLogic) captureLead(ctx context.Context, lsqData *business.LSQData, clientType string, isNRI bool) error {
	var apiRequest modelsv1.LSQLeadCaptureAPIRequest
	var data any = lsqData
	lsqDataMap := make(map[string]any)

	// Marshal struct to JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	// Unmarshal JSON into map
	err = json.Unmarshal(jsonData, &lsqDataMap)
	if err != nil {
		return err
	}

	d := make(map[string]string)
	// Iterate over the map and convert key-value pairs to string-string map
	for key, val := range lsqDataMap {
		strVal, _ := val.(string)
		d[key] = strVal
	}

	apiRequest.ClientType = clientType
	apiRequest.IsNRI = isNRI
	apiRequest.Data = make([]modelsv1.LSQLeadCaptureAPIRequestData, 0)
	for k, v := range d {
		apiRequest.Data = append(apiRequest.Data, modelsv1.LSQLeadCaptureAPIRequestData{
			Attribute: k,
			Value:     v,
		})
	}
	err = l.lsqExternalProvider.CaptureLead(ctx, &apiRequest)
	return err
}

func (l *LsqBusinessLogic) shouldExcludePlanName(lsqData *business.LSQData) bool {
	if lsqData.ClientType == constants.B2B && slices.Contains(config.LSQ().PlanNamesToBeExcluded, strings.ToUpper(lsqData.PlanName)) {
		return true
	}
	return false
}

func (l *LsqBusinessLogic) getData(lsqData *business.LSQData) (string, string, string, string) {
	var leadSource, leadMedium, leadCampaign, referringClientID string
	if lsqData.RNESource != constants.Empty && lsqData.RNECampaign != constants.Empty &&
		lsqData.RNEField2 != constants.Empty && lsqData.ReferreringClientID != constants.Empty {
		leadSource = lsqData.RNESource
		leadMedium = lsqData.RNEField2
		leadCampaign = lsqData.RNECampaign
	} else {
		referrerParamString := lsqData.Referrer
		referrerParam := l.GetDataFromReferralString(referrerParamString, lsqData.Platform)
		for k, v := range referrerParam {
			switch k {
			case constants.ReferrerClientID:
				referringClientID = v
			case constants.LeadSource:
				leadSource = v
			case constants.LeadMedium:
				leadMedium = v
			case constants.LeadCampaign:
				leadCampaign = v
			default:
			}
		}
	}
	return leadSource, leadMedium, leadCampaign, referringClientID
}

func (l *LsqBusinessLogic) getUTMParameters(lsqData *business.LSQData) (string, string, string) {
	var utmSource, utmMedium, utmCampaign string
	referrerParamString := lsqData.Referrer
	referrerParam := l.GetDataFromReferralString(referrerParamString, lsqData.Platform)
	for k, v := range referrerParam {
		switch k {
		case constants.UTMSource:
			utmSource = v
		case constants.UTMMedium:
			utmMedium = v
		case constants.UTMCampaign:
			utmCampaign = v
		default:
		}
	}
	return utmSource, utmMedium, utmCampaign
}

func (l *LsqBusinessLogic) handleClientTypeSpecificData(lsqData *business.LSQData, clientType string) {
	if clientType == constants.B2BReferralType {
		lsqData.JourneyType = lsqData.ApplicationJourneyType
	}
	lsqData.Type = l.getLSQType(lsqData.ClientType)
	lsqData.ApplicationType = clientType
}
