package prismsqs

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils/errors"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"

	"github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/aws/aws-sdk-go-v2/aws"
)

func (p *PrismSQSBusinessLogic) HandlePrismEvent(ctx context.Context, prismEventMessage *business.PrismEventMessage) error {
	// Fetch required data

	startData, err := p.kycRepository.FetchUserJourneyAndFulfilmentStartData(ctx, prismEventMessage.AppNumber)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx).Err(err).Msg("Error fetching start data")
			return err
		}
		log.Error(ctx).Err(err).Msg("no record found in start table for user for prism event")
		// add check for emod event
	} else if startData != nil && startData.AppNumber != constants.Empty {
		prismEventMessage.JourneyType = constants.Onboarding
		prismEventMessage.Intent = startData.Intent
	}
	if startData == nil || startData.AppNumber == constants.Empty {
		startData, err = p.kycRepository.FetchEmodUserJourneyAndFulfilmentStartData(ctx, prismEventMessage.AppNumber)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Error(ctx).Err(err).Msg("Error fetching start data")
				return err
			}
			log.Error(ctx).Err(err).Msg("no record found in start table for user for prism event")
			return nil
		} else if startData != nil && startData.AppNumber != constants.Empty {
			prismEventMessage.JourneyType = constants.EmodJourneyType
			prismEventMessage.Intent = startData.Intent
		} else if startData == nil || startData.AppNumber == constants.Empty {
			log.Error(ctx).Err(err).Msg("no record found in start table for user for prism event")
			return nil
		}
	}

	addJourneyStartTimeAndFulfilmentTime(startData, prismEventMessage)
	// Adding Event Sequence.
	prismEventMessage.SequenceNumber = fetchSequenceNumber(ctx, prismEventMessage)
	p.triggerKinesisMsg(ctx, prismEventMessage)
	return nil
}

func (p *PrismSQSBusinessLogic) triggerKinesisMsg(ctx context.Context, prismEventMessage *business.PrismEventMessage) {
	// if the eventName = 'metadata', then send the messages to the metadata stream
	if prismEventMessage.EventName == constants.Metadata {
		p.prismBusinessLogic.TriggerKinesisMessageForHistoryEvent(ctx, prismEventMessage.AppNumber, constants.PrismMetadataEvent, prismEventMessage)
		return
	}

	// else send to the main stream
	if prismEventMessage.EventName != "" && prismEventMessage.EventSubtype != "" {
		p.prismBusinessLogic.TriggerKinesisMessageForHistoryEvent(ctx, prismEventMessage.AppNumber, constants.PrismMainEvent, prismEventMessage)
	}
}

func (p *PrismSQSBusinessLogic) SendMessageToQueue(ctx context.Context, prismEventMessage *business.PrismEventMessage) {
	defer func() {
		if r := recover(); r != nil {
			log.Info(ctx).Interface(constants.AppNumberKey, prismEventMessage.AppNumber).Interface("panic", r).Msg("SendPrismEventToSqs: Recovered from panic")
		}
	}()

	jsonBytes, err := utils.MarshalJSON(prismEventMessage)
	if err != nil {
		return
	}
	messageAttribute := map[string]types.MessageAttributeValue{
		constants.MessageType: {
			DataType:    aws.String("String"),
			StringValue: aws.String("prism"),
		},
	}
	// add three 3 retry to send message to sqs
	maxRetry := constants.MaxRetryForSendMessageInPrismSqs
	for i := 0; i < maxRetry; i++ {
		err = p.sqsClient.SendMessage(ctx, config.SQS().Prism.QueueURL, string(jsonBytes), prismEventMessage.AppNumber, utils.GenerateUUID(), messageAttribute)
		if err != nil {
			continue
		}
	}
	if err != nil {
		log.Info(ctx).Err(err).Msg("error sending message to SQS for prism")
		return
	}
}

func addJourneyStartTimeAndFulfilmentTime(startData *business.UserJourneyAndFulfilmentStart, prismEventMessage *business.PrismEventMessage) {
	if startData != nil && !startData.UserJourneyStartTS.IsZero() {
		prismEventMessage.ApplicationStartTime = startData.UserJourneyStartTS.UnixMilli()
	}
	if startData != nil && !startData.FulfilmentStartTS.IsZero() {
		prismEventMessage.FulfilmentStartTime = startData.FulfilmentStartTS.UnixMilli()
	}
}
func fetchSequenceNumber(ctx context.Context, prismEventMessage *business.PrismEventMessage) int64 {
	if prismEventMessage.JourneyType == constants.Onboarding {
		seq := config.Prism().PrismOnboardingEventSubtypeSequenceMap[prismEventMessage.Workflow].PrismEventSubtypeSequenceMap[prismEventMessage.EventSubtype]
		if seq == 0 {
			log.Info(ctx).Msg(fmt.Sprintf("seq no is not found for workflow %s subtype %s", prismEventMessage.Workflow, prismEventMessage.EventSubtype))
		}
		seq = config.Prism().PrismOnboardingEventSubtypeSequenceMap[constants.Default].PrismEventSubtypeSequenceMap[prismEventMessage.EventSubtype]
		if seq == 0 {
			seq = config.Prism().PrismEventSubtypeSequenceMap[prismEventMessage.EventSubtype]
		}
		return seq
	} else if prismEventMessage.JourneyType == constants.EmodJourneyType {
		seq := config.Prism().PrismOnboardingEventSubtypeSequenceMap[prismEventMessage.Workflow].PrismEventSubtypeSequenceMap[prismEventMessage.EventSubtype]
		if seq == 0 {
			log.Info(ctx).Msg(fmt.Sprintf("seq no is not found for workflow %s subtype %s", prismEventMessage.Workflow, prismEventMessage.EventSubtype))
		}
		return seq
	}
	return 0
}
