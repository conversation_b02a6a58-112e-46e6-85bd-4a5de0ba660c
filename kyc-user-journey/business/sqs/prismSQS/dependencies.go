package prismsqs

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

type PrismBusinessLogic interface {
	TriggerKinesisMessageForHistoryEvent(ctx context.Context, appNumber string, prismMessageType int, eventData any)
}

type KYCRepository interface {
	FetchUserJourneyAndFulfilmentStartData(ctx context.Context, appNumber string) (*business.UserJourneyAndFulfilmentStart, error)
	FetchEmodUserJourneyAndFulfilmentStartData(ctx context.Context, appNumber string) (*business.UserJourneyAndFulfilmentStart, error)
}

type SqsClient interface {
	SendMessage(ctx context.Context, queueURL, messageBody, groupID, dedupID string, messageAttributes map[string]types.MessageAttributeValue) error
}
type PrismSQSBusinessLogic struct {
	prismBusinessLogic PrismBusinessLogic
	kycRepository      KYCRepository
	sqsClient          SqsClient
}

func NewPrismSQSBusinessLogic(prismBusinessLogic PrismBusinessLogic, kycRepository KYCRepository, sqsClient SqsClient) *PrismSQSBusinessLogic {
	return &PrismSQSBusinessLogic{
		prismBusinessLogic: prismBusinessLogic,
		kycRepository:      kycRepository,
		sqsClient:          sqsClient,
	}
}
