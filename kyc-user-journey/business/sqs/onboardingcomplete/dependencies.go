package onboardingcomplete

import (
	"context"

	"github.com/angel-one/kyc-user-journey/models/business"

	modelsExternal "github.com/angel-one/kyc-user-journey/models/externals"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

type ESignRepository interface {
	FetchRecordByAppNumber(ctx context.Context, appNumber string) (*business.Esign, error)
}

type LSQBusinessLogic interface {
	TriggerSQSMessagePublishForUsecase(ctx context.Context, appNumber, usecase string)
}

type SqsClient interface {
	SendMessage(ctx context.Context, queueURL, messageBody, groupID, dedupID string, messageAttributes map[string]types.MessageAttributeValue) error
}

type WorkflowExternalProvider interface {
	StartProcessInstance(ctx context.Context, workflowName string, request modelsExternal.ProcessStartRequest) (*modelsExternal.ProcessStartResponse, error)
	GetProcessInstanceByBusinessKey(ctx context.Context, key string) ([]modelsExternal.WorkflowInstance, error)
	DeleteProcessInstance(ctx context.Context, instanceID, skipCustomListeners, skipIoMappings, skipSubprocesses string) error
	GetProcessInstanceVariables(ctx context.Context, processInstanceID string, _ map[string]string) (map[string]any, error)
}

type OnboardingCompleteBusinessLogic struct {
	lsqBusinessLogic         LSQBusinessLogic
	sqsClient                SqsClient
	workflowExternalProvider WorkflowExternalProvider
	eSignRepository          ESignRepository
}

func NewOnboardingCompleteBusinessLogic(lsqBusinessLogic LSQBusinessLogic, sqsClient SqsClient, workflowExternalProvider WorkflowExternalProvider, eSignRepository ESignRepository) *OnboardingCompleteBusinessLogic {
	return &OnboardingCompleteBusinessLogic{
		lsqBusinessLogic:         lsqBusinessLogic,
		sqsClient:                sqsClient,
		workflowExternalProvider: workflowExternalProvider,
		eSignRepository:          eSignRepository,
	}
}
