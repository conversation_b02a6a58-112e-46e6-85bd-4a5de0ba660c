package onboardingcomplete

import (
	"context"
	"maps"
	"slices"
	"strings"

	"github.com/angel-one/kyc-user-journey/models/api"
	"github.com/angel-one/kyc-user-journey/utils/percentagerollout"

	"github.com/angel-one/go-pii-utils/log"
	"github.com/angel-one/go-utils"
	"github.com/angel-one/kyc-user-journey/config"
	"github.com/angel-one/kyc-user-journey/constants"
	"github.com/angel-one/kyc-user-journey/models/business"
	modelExtv1 "github.com/angel-one/kyc-user-journey/models/externals"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

func (o *OnboardingCompleteBusinessLogic) HandleOnboardingCompleteMessage(ctx context.Context, onboardingCompleteMessage *business.OnboardingCompleteMessage) error {
	workflowInstances, err := o.workflowExternalProvider.GetProcessInstanceByBusinessKey(ctx, onboardingCompleteMessage.Mobile)
	if err != nil {
		return err
	}
	onekycWorkflowVariables := make(map[string]any)
	for _, instance := range workflowInstances {
		if strings.HasPrefix(instance.DefinitionID, "onekyc-admin:") {
			onekycWorkflowVariables, err = o.workflowExternalProvider.GetProcessInstanceVariables(ctx, instance.ID, nil)
			if err != nil {
				log.Error(ctx).Err(err).Msg("Error getting one kyc process variables")
				return err
			}
		}

		if checkForEligibleWorkflowToDelete(instance.DefinitionID) {
			err = o.workflowExternalProvider.DeleteProcessInstance(ctx, instance.ID, "false", "true", "false")
			if err != nil {
				return err
			}
		}
	}

	consolidatedMap := maps.Clone(onekycWorkflowVariables)
	maps.Copy(consolidatedMap, onboardingCompleteMessage.Data)

	workflowName := constants.KycFulfilmentWorkflow
	physicalMfTOEquityWorkflowName := config.GetClient().GetStringD(constants.ApplicationConfig, constants.ConfigMfToEquityFulfilmentProcessDefinition, constants.MfToEquityFulfilmentProcessDefinitionDefaultValue)
	if journeyType, ok := consolidatedMap[constants.KycTypeWorkflowKey]; ok && journeyType == constants.PhysicalMFOnlyJourney {
		workflowName = constants.PhysicalMFFulfilmentWorkflow
	} else if journeyType, ok = consolidatedMap[constants.KycTypeWorkflowKey]; ok && journeyType == constants.PhysicalMFEquityJourney {
		workflowName = physicalMfTOEquityWorkflowName
	}
	workflowVariable := getProcessVariables(consolidatedMap)
	_, workflowErr := o.workflowExternalProvider.StartProcessInstance(ctx, workflowName, modelExtv1.ProcessStartRequest{
		BusinessKey: onboardingCompleteMessage.Mobile,
		Variables:   workflowVariable,
	})
	if workflowErr != nil {
		log.Error(ctx).Err(workflowErr).Str(constants.CorrelationIDContextKey, onboardingCompleteMessage.AppNumber).Msg("error in starting kyc-fulfilment workflow")
		return workflowErr
	}
	if strings.HasPrefix(onboardingCompleteMessage.AppNumber, "EQ") {
		o.lsqBusinessLogic.TriggerSQSMessagePublishForUsecase(ctx, onboardingCompleteMessage.AppNumber, constants.ApplicationSubmitSuccessLSQEvent)
	}

	communicationWorkflowVariables := getProcessVariablesForCommunicationWorkflow(onboardingCompleteMessage.Data)
	_, workflowErr = o.workflowExternalProvider.StartProcessInstance(ctx, constants.ClientCommunicationWorkflow, modelExtv1.ProcessStartRequest{
		BusinessKey: onboardingCompleteMessage.Mobile,
		Variables:   communicationWorkflowVariables,
	})
	if workflowErr != nil {
		log.Error(ctx).Err(workflowErr).Str(constants.CorrelationIDContextKey, onboardingCompleteMessage.AppNumber).Msg("error in starting client-communication workflow")
	}

	return nil
}

func (o *OnboardingCompleteBusinessLogic) HandleNriOnboardingCompleteMessage(ctx context.Context, message *business.NriOnboardingCompleteMessage) error {
	// determining if scrutiny 3 is required based on eSign
	isScrutiny3Allowed := true
	eSign, err := o.eSignRepository.FetchRecordByAppNumber(ctx, message.AppNumber)
	if err != nil {
		log.Warn(ctx).Err(err).Msg("HandleNriOnboardingCompleteMessage: error fetching eSign details")
	}

	if eSign.FullName != constants.Empty || eSign.Type != constants.Empty {
		isScrutiny3Allowed = false
	}

	// trigger nri fulfilment
	_, workflowError := o.workflowExternalProvider.StartProcessInstance(ctx, constants.NRIFulfilmentProcessDefinition, modelExtv1.ProcessStartRequest{
		BusinessKey: message.Mobile,
		Variables:   getNriProcessVariables(message, isScrutiny3Allowed),
	})

	if workflowError != nil {
		log.Error(ctx).Err(workflowError).Str(constants.CorrelationIDContextKey, message.AppNumber).Msg("error in starting physical-fulfilment workflow for nri")
		return workflowError
	}

	return nil
}

func getNriProcessVariables(message *business.NriOnboardingCompleteMessage, isScrutiny3Allowed bool) map[string]modelExtv1.VariableObject {
	variables := map[string]modelExtv1.VariableObject{
		constants.AppNumberWorkflowKey: {
			Type:  constants.WorkflowVariableStringType,
			Value: message.AppNumber,
		},
		constants.SourceWorkflowKey: {
			Type:  constants.WorkflowVariableStringType,
			Value: message.Data[constants.SourceWorkflowKey],
		},
		constants.MobileWorkflowKey: {
			Type:  constants.WorkflowVariableStringType,
			Value: message.Mobile,
		},
		constants.CheckMobileAgainstSBWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckMobileAgainstSB,
		},
		constants.CheckEmailAgainstSBWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckEmailAgainstSB,
		},
		constants.CheckIfMobileExistsInBOWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckIfMobileExistsInBO,
		},
		constants.CheckIfEmailExistsInBOWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckIfEmailExistsInBO,
		},
		constants.CheckIfPANExistsInBOWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckIfPANExistsInBO,
		},
		constants.CheckIfSEBIBannedWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckIfSEBIBanned,
		},
		constants.CheckIfValidateAadhaarWithKRAWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.CheckIfValidateAadhaarWithKRA,
		},
		constants.BoPushEnabledWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.BOPushEnabled,
		},
		constants.MfssPushEnabledWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.MFSSPushEnabled,
		},
		constants.AmxPushEnabledWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.AMXPushEnabled,
		},
		constants.ProfilePushEnabledWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.ProfilePushEnabled,
		},
		constants.MFKycUCCPushEnabledWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: config.NRI().Fulfilment.MFKYCUCCPushEnabled,
		}, // no need for NRI
		constants.IsScrutiny3AllowedWorkflowKey: {
			Type:  constants.WorkflowVariableBooleanType,
			Value: isScrutiny3Allowed,
		},
	}

	return variables
}

func (o *OnboardingCompleteBusinessLogic) TriggerOnboardingCompleteMessagePublish(ctx context.Context, data *api.ESignSetuRedirectURLData) error {
	queueURL := config.SQS().KycUserJourney.QueueURL
	sqsPayload := o.createSqsPayload(ctx, data)
	jsonBytes, err := utils.MarshalJSON(sqsPayload)
	if err != nil {
		return err
	}
	dedupID := data.AppNumber
	if strings.HasPrefix(data.AppNumber, "EQ") && data.RejectionType != constants.Empty &&
		(percentagerollout.IsFeatureRolloutEnabled(ctx, data.AppNumber, constants.ApplicationRolloutDiyRejectionJourneyAdoption, config.Feature().Enable.DiyRejectionJourneyAdoption, false) ||
			slices.Contains(config.Feature().Whitelist.DiyRejectionJourneyAdoption, data.Mobile)) {
		dedupID = data.AppNumber + "-" + data.RejectionType
	}
	err = o.sqsClient.SendMessage(ctx, queueURL, string(jsonBytes), data.AppNumber, dedupID, map[string]types.MessageAttributeValue{
		"MessageType": {
			DataType:    aws.String("String"),
			StringValue: aws.String("OnboardingComplete"),
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (o *OnboardingCompleteBusinessLogic) TriggerNRIOnboardingCompleteMessagePublish(ctx context.Context, data *api.ESignSetuRedirectURLData) error {
	queueURL := config.SQS().KycUserJourney.QueueURL
	sqsPayload := o.createSqsPayload(ctx, data)
	jsonBytes, err := utils.MarshalJSON(sqsPayload)
	if err != nil {
		return err
	}
	err = o.sqsClient.SendMessage(ctx, queueURL, string(jsonBytes), data.AppNumber, data.AppNumber, map[string]types.MessageAttributeValue{
		"MessageType": {
			DataType:    aws.String("String"),
			StringValue: aws.String("NRIOnboardingComplete"),
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (o *OnboardingCompleteBusinessLogic) createSqsPayload(_ context.Context, data *api.ESignSetuRedirectURLData) *business.OnboardingCompleteMessage {
	return &business.OnboardingCompleteMessage{
		AppNumber: data.AppNumber,
		Mobile:    data.Mobile,
		Data: map[string]any{
			constants.MobileWorkflowKey:        data.Mobile,
			constants.AppNumberWorkflowKey:     data.AppNumber,
			constants.CorrelationIDWorkflowKey: data.AppNumber,
			constants.AppVersionWorkflowKey:    data.AppVersion,
			constants.PlatformWorkflowKey:      data.Platform,
			constants.SourceWorkflowKey:        data.Source,
			constants.KycTypeWorkflowKey:       data.JourneyType,
			constants.DeviceWorkflowKey:        data.Device,
			constants.DeviceOSWorkflowKey:      data.DeviceOS,
		},
	}
}

func getProcessVariables(data map[string]any) map[string]modelExtv1.VariableObject {
	variables := map[string]modelExtv1.VariableObject{}
	variables[constants.MobileWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.MobileWorkflowKey],
	}
	variables[constants.AppNumberWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.AppNumberWorkflowKey],
	}
	variables[constants.CorrelationIDWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.CorrelationIDWorkflowKey],
	}
	variables[constants.AppVersionWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.AppVersionWorkflowKey],
	}
	variables[constants.PlatformWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.PlatformWorkflowKey],
	}
	variables[constants.SourceWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.SourceWorkflowKey],
	}

	if scrutinyStatus, ok := data[constants.ScrutinyStatusWorkflowKey]; ok {
		variables[constants.ScrutinyStatusWorkflowKey] = modelExtv1.VariableObject{
			Type:  constants.WorkflowVariableStringType,
			Value: scrutinyStatus,
		}
	}

	if journeyType, ok := data[constants.KycTypeWorkflowKey]; ok {
		variables[constants.KycTypeWorkflowKey] = modelExtv1.VariableObject{
			Type:  constants.WorkflowVariableStringType,
			Value: journeyType,
		}
	}

	if device, ok := data[constants.DeviceWorkflowKey]; ok {
		variables[constants.DeviceWorkflowKey] = modelExtv1.VariableObject{
			Type:  constants.WorkflowVariableStringType,
			Value: device,
		}
	}

	if deviceOS, ok := data[constants.DeviceOSWorkflowKey]; ok {
		variables[constants.DeviceOSWorkflowKey] = modelExtv1.VariableObject{
			Type:  constants.WorkflowVariableStringType,
			Value: deviceOS,
		}
	}

	return variables
}

func getProcessVariablesForCommunicationWorkflow(data map[string]any) map[string]modelExtv1.VariableObject {
	variables := map[string]modelExtv1.VariableObject{}
	variables[constants.MobileWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.MobileWorkflowKey],
	}
	variables[constants.AppNumberWorkflowKey] = modelExtv1.VariableObject{
		Type:  constants.WorkflowVariableStringType,
		Value: data[constants.AppNumberWorkflowKey],
	}

	return variables
}

func checkForEligibleWorkflowToDelete(definitionID string) bool {
	physicalMfTOEquityWorkflowName := config.GetClient().GetStringD(constants.ApplicationConfig, constants.ConfigMfToEquityFulfilmentProcessDefinition, constants.MfToEquityFulfilmentProcessDefinitionDefaultValue)
	if strings.HasPrefix(definitionID, "kyc-services:") ||
		strings.HasPrefix(definitionID, "onekyc-admin:") ||
		strings.HasPrefix(definitionID, "kyc-fulfilment:") ||
		strings.HasPrefix(definitionID, "kyc-phymf-fulfilment:") ||
		strings.HasPrefix(definitionID, "client-communication:") ||
		strings.HasPrefix(definitionID, physicalMfTOEquityWorkflowName) {
		return true
	}
	return false
}

func (o *OnboardingCompleteBusinessLogic) SendOnboardingCompleteMessage(ctx context.Context, sqsPayload *business.OnboardingCompleteMessage) error {
	queueURL := config.SQS().KycUserJourney.QueueURL
	jsonBytes, err := utils.MarshalJSON(sqsPayload)
	if err != nil {
		return err
	}
	err = o.sqsClient.SendMessage(ctx, queueURL, string(jsonBytes), sqsPayload.AppNumber, sqsPayload.AppNumber, map[string]types.MessageAttributeValue{
		"MessageType": {
			DataType:    aws.String("String"),
			StringValue: aws.String("OnboardingComplete"),
		},
	})
	if err != nil {
		return err
	}
	return nil
}
